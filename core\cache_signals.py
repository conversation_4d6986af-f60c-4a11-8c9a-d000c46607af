"""
Cache invalidation signals for automatic cache management
"""

from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver
from django.utils import timezone
import logging

from .cache_utils import CacheManager, OddsCache, UserCache, SportsCache

logger = logging.getLogger(__name__)


# Sports and Events Cache Invalidation
@receiver(post_save, sender='sports.Sport')
@receiver(post_delete, sender='sports.Sport')
def invalidate_sports_cache(sender, instance, **kwargs):
    """Invalidate sports cache when sport is modified"""
    try:
        # Invalidate sports list cache
        CacheManager.invalidate_by_tags(['sports_data'])
        logger.info(f"Invalidated sports cache for sport: {instance.name}")
    except Exception as e:
        logger.error(f"Error invalidating sports cache: {e}")


@receiver(post_save, sender='sports.Event')
@receiver(post_delete, sender='sports.Event')
def invalidate_events_cache(sender, instance, **kwargs):
    """Invalidate events cache when event is modified"""
    try:
        # Invalidate sport-specific events cache
        CacheManager.invalidate_by_tags([f"sport_{instance.sport_id}", 'events_data'])
        
        # Invalidate event-specific odds cache
        OddsCache.invalidate_event_odds(instance.id)
        
        logger.info(f"Invalidated events cache for event: {instance.id}")
    except Exception as e:
        logger.error(f"Error invalidating events cache: {e}")


@receiver(post_save, sender='sports.Market')
@receiver(post_delete, sender='sports.Market')
def invalidate_market_cache(sender, instance, **kwargs):
    """Invalidate market cache when market is modified"""
    try:
        # Invalidate event odds cache
        OddsCache.invalidate_event_odds(instance.event_id)
        logger.info(f"Invalidated market cache for market: {instance.id}")
    except Exception as e:
        logger.error(f"Error invalidating market cache: {e}")


@receiver(post_save, sender='sports.Odds')
def invalidate_odds_cache(sender, instance, **kwargs):
    """Invalidate odds cache when odds are updated"""
    try:
        # Get the event ID through the market
        event_id = instance.market.event_id
        
        # Invalidate event odds cache
        OddsCache.invalidate_event_odds(event_id)
        
        # Also invalidate general odds cache patterns
        CacheManager.invalidate_by_tags(['odds_data'])
        
        logger.info(f"Invalidated odds cache for event: {event_id}")
    except Exception as e:
        logger.error(f"Error invalidating odds cache: {e}")


# User Cache Invalidation
@receiver(post_save, sender='accounts.CustomUser')
def invalidate_user_cache(sender, instance, **kwargs):
    """Invalidate user cache when user is modified"""
    try:
        UserCache.invalidate_user_cache(instance.id)
        logger.info(f"Invalidated user cache for user: {instance.id}")
    except Exception as e:
        logger.error(f"Error invalidating user cache: {e}")


@receiver(post_save, sender='accounts.UserProfile')
def invalidate_user_profile_cache(sender, instance, **kwargs):
    """Invalidate user profile cache when profile is modified"""
    try:
        UserCache.invalidate_user_cache(instance.user_id)
        logger.info(f"Invalidated user profile cache for user: {instance.user_id}")
    except Exception as e:
        logger.error(f"Error invalidating user profile cache: {e}")


# Betting Cache Invalidation
@receiver(post_save, sender='betting.Bet')
def invalidate_bet_cache(sender, instance, **kwargs):
    """Invalidate bet-related cache when bet is modified"""
    try:
        # Invalidate user-specific bet cache
        UserCache.invalidate_user_cache(instance.user_id)
        
        # Invalidate general betting statistics cache
        CacheManager.invalidate_by_tags([f"user_{instance.user_id}"])
        
        logger.info(f"Invalidated bet cache for user: {instance.user_id}")
    except Exception as e:
        logger.error(f"Error invalidating bet cache: {e}")


@receiver(post_save, sender='betting.BetSelection')
def invalidate_bet_selection_cache(sender, instance, **kwargs):
    """Invalidate cache when bet selection is modified"""
    try:
        # Invalidate user bet cache
        UserCache.invalidate_user_cache(instance.bet.user_id)
        logger.info(f"Invalidated bet selection cache for bet: {instance.bet.id}")
    except Exception as e:
        logger.error(f"Error invalidating bet selection cache: {e}")


# Payment Cache Invalidation
@receiver(post_save, sender='payments.Transaction')
def invalidate_transaction_cache(sender, instance, **kwargs):
    """Invalidate transaction cache when transaction is modified"""
    try:
        # Invalidate user cache (includes balance and transaction history)
        UserCache.invalidate_user_cache(instance.user_id)
        logger.info(f"Invalidated transaction cache for user: {instance.user_id}")
    except Exception as e:
        logger.error(f"Error invalidating transaction cache: {e}")


# Jackpot Cache Invalidation
@receiver(post_save, sender='jackpot.Jackpot')
@receiver(post_delete, sender='jackpot.Jackpot')
def invalidate_jackpot_cache(sender, instance, **kwargs):
    """Invalidate jackpot cache when jackpot is modified"""
    try:
        CacheManager.invalidate_by_tags(['jackpot_data'])
        logger.info(f"Invalidated jackpot cache for jackpot: {instance.id}")
    except Exception as e:
        logger.error(f"Error invalidating jackpot cache: {e}")


@receiver(post_save, sender='jackpot.JackpotEntry')
def invalidate_jackpot_entry_cache(sender, instance, **kwargs):
    """Invalidate jackpot entry cache when entry is modified"""
    try:
        # Invalidate user cache
        UserCache.invalidate_user_cache(instance.user_id)
        
        # Invalidate jackpot-specific cache
        CacheManager.invalidate_by_tags([f"jackpot_{instance.jackpot_id}"])
        
        logger.info(f"Invalidated jackpot entry cache for user: {instance.user_id}")
    except Exception as e:
        logger.error(f"Error invalidating jackpot entry cache: {e}")


# Analytics Cache Invalidation
@receiver(post_save, sender='analytics.DailyMetrics')
def invalidate_analytics_cache(sender, instance, **kwargs):
    """Invalidate analytics cache when metrics are updated"""
    try:
        CacheManager.invalidate_by_tags(['analytics_data'])
        logger.info(f"Invalidated analytics cache for date: {instance.date}")
    except Exception as e:
        logger.error(f"Error invalidating analytics cache: {e}")


@receiver(post_save, sender='analytics.UserActivity')
def invalidate_user_activity_cache(sender, instance, **kwargs):
    """Invalidate user activity cache when activity is recorded"""
    try:
        # Invalidate user-specific cache
        UserCache.invalidate_user_cache(instance.user_id)
        
        # Invalidate analytics cache
        CacheManager.invalidate_by_tags(['analytics_data'])
        
        logger.info(f"Invalidated user activity cache for user: {instance.user_id}")
    except Exception as e:
        logger.error(f"Error invalidating user activity cache: {e}")


# Support Cache Invalidation
@receiver(post_save, sender='support.Ticket')
def invalidate_support_cache(sender, instance, **kwargs):
    """Invalidate support cache when ticket is modified"""
    try:
        # Invalidate user-specific support cache
        UserCache.invalidate_user_cache(instance.user_id)
        
        # Invalidate general support cache
        CacheManager.invalidate_by_tags(['support_data'])
        
        logger.info(f"Invalidated support cache for ticket: {instance.id}")
    except Exception as e:
        logger.error(f"Error invalidating support cache: {e}")


# Bulk Cache Invalidation Utilities
def invalidate_all_user_cache(user_id: int):
    """Invalidate all cache related to a specific user"""
    try:
        UserCache.invalidate_user_cache(user_id)
        CacheManager.invalidate_by_tags([f"user_{user_id}"])
        logger.info(f"Invalidated all cache for user: {user_id}")
    except Exception as e:
        logger.error(f"Error invalidating all user cache: {e}")


def invalidate_all_odds_cache():
    """Invalidate all odds-related cache"""
    try:
        CacheManager.get_cache(CacheManager.ODDS_CACHE).clear()
        CacheManager.invalidate_by_tags(['odds_data'])
        logger.info("Invalidated all odds cache")
    except Exception as e:
        logger.error(f"Error invalidating all odds cache: {e}")


def invalidate_all_sports_cache():
    """Invalidate all sports-related cache"""
    try:
        CacheManager.invalidate_by_tags(['sports_data', 'events_data'])
        logger.info("Invalidated all sports cache")
    except Exception as e:
        logger.error(f"Error invalidating all sports cache: {e}")


# Scheduled cache cleanup (can be called by Celery tasks)
def cleanup_expired_cache():
    """Clean up expired cache entries"""
    try:
        # This would typically be handled by Redis automatically
        # But we can implement custom cleanup logic here
        logger.info("Cache cleanup completed")
    except Exception as e:
        logger.error(f"Error during cache cleanup: {e}")
