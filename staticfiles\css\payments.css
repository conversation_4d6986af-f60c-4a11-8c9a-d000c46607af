/* Payments CSS */

/* Dashboard Styles */
.payments-dashboard {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.dashboard-header h1 {
    color: var(--primary-color);
    margin: 0;
    font-size: 2rem;
}

.balance-card {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 20px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 20px;
    min-width: 300px;
}

.balance-info {
    display: flex;
    flex-direction: column;
}

.balance-label {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-bottom: 5px;
}

.balance-amount {
    font-size: 1.8rem;
    font-weight: bold;
}

.balance-actions {
    display: flex;
    gap: 10px;
}

.balance-actions .btn {
    padding: 8px 16px;
    font-size: 0.9rem;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.quick-actions-card,
.recent-transactions-card,
.payment-methods-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.card-header h3 {
    margin: 0;
    color: var(--text-dark);
}

.view-all-link,
.add-method-link {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

.view-all-link:hover,
.add-method-link:hover {
    text-decoration: underline;
}

/* Quick Actions */
.action-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background: var(--bg-light);
    border-radius: 8px;
    text-decoration: none;
    color: var(--text-dark);
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: var(--primary-light);
    transform: translateY(-2px);
    color: var(--primary-color);
}

.action-btn i {
    font-size: 1.5rem;
    margin-bottom: 8px;
}

/* Transaction List */
.transaction-item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--bg-light);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.transaction-details {
    flex: 1;
}

.transaction-desc {
    font-weight: 500;
    color: var(--text-dark);
    margin-bottom: 4px;
}

.transaction-date {
    font-size: 0.85rem;
    color: var(--text-muted);
}

.transaction-amount {
    text-align: right;
}

.amount {
    display: block;
    font-weight: bold;
    margin-bottom: 4px;
}

.amount.positive {
    color: var(--success-color);
}

.amount.negative {
    color: var(--danger-color);
}

.status {
    font-size: 0.8rem;
    padding: 2px 8px;
    border-radius: 12px;
    text-transform: uppercase;
    font-weight: 500;
}

.status-pending {
    background: var(--warning-light);
    color: var(--warning-color);
}

.status-processing {
    background: var(--info-light);
    color: var(--info-color);
}

.status-completed {
    background: var(--success-light);
    color: var(--success-color);
}

.status-failed {
    background: var(--danger-light);
    color: var(--danger-color);
}

/* Payment Methods */
.method-item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
}

.method-item:last-child {
    border-bottom: none;
}

.method-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--bg-light);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.method-details {
    flex: 1;
}

.method-name {
    font-weight: 500;
    color: var(--text-dark);
    display: block;
    margin-bottom: 4px;
}

.method-info {
    font-size: 0.85rem;
    color: var(--text-muted);
}

.method-status .verified {
    color: var(--success-color);
    font-size: 0.85rem;
}

.method-status .unverified {
    color: var(--warning-color);
    font-size: 0.85rem;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-muted);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

.empty-state p {
    margin: 10px 0 5px;
    font-weight: 500;
}

.empty-state small {
    font-size: 0.85rem;
}

/* Deposit Page */
.deposit-page {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.header-content h1 {
    color: var(--primary-color);
    margin: 0 0 10px 0;
    font-size: 2rem;
}

.current-balance {
    display: flex;
    align-items: center;
    gap: 10px;
}

.balance-label {
    color: var(--text-muted);
}

.balance-amount {
    font-weight: bold;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.deposit-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
}

/* Deposit Form */
.deposit-form-container {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 25px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-dark);
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-errors {
    margin-top: 8px;
}

.form-errors .error {
    display: block;
    color: var(--danger-color);
    font-size: 0.85rem;
    margin-bottom: 4px;
}

.amount-info {
    margin-top: 8px;
}

.amount-info small {
    color: var(--text-muted);
    font-size: 0.85rem;
}

/* Quick Amounts */
.quick-amounts {
    margin-bottom: 25px;
}

.quick-amounts-label {
    display: block;
    margin-bottom: 10px;
    font-size: 0.9rem;
    color: var(--text-muted);
}

.quick-amount-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.quick-amount-btn {
    padding: 8px 16px;
    border: 2px solid var(--border-color);
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.quick-amount-btn:hover,
.quick-amount-btn.active {
    border-color: var(--primary-color);
    background: var(--primary-light);
    color: var(--primary-color);
}

/* Payment Methods */
.payment-methods {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.payment-method-option {
    position: relative;
}

.payment-method-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.payment-method-label {
    display: flex;
    align-items: center;
    padding: 16px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.payment-method-option input[type="radio"]:checked + .payment-method-label {
    border-color: var(--primary-color);
    background: var(--primary-light);
}

.method-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--bg-light);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 1.2rem;
}

.method-details {
    flex: 1;
}

.method-name {
    font-weight: 500;
    color: var(--text-dark);
    display: block;
    margin-bottom: 4px;
}

.method-desc {
    font-size: 0.85rem;
    color: var(--text-muted);
}

/* M-Pesa Fields */
.mpesa-fields {
    background: var(--bg-light);
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.field-info {
    margin-top: 8px;
}

.field-info small {
    color: var(--text-muted);
    font-size: 0.85rem;
}

/* Saved Methods */
.saved-methods {
    margin-top: 30px;
    padding-top: 30px;
    border-top: 1px solid var(--border-color);
}

.saved-methods h4 {
    margin-bottom: 15px;
    color: var(--text-dark);
}

.saved-methods-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.saved-method-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: white;
}

.saved-method-item .method-icon {
    width: 40px;
    height: 40px;
    margin-right: 15px;
}

.saved-method-item .method-info {
    flex: 1;
}

.saved-method-item .method-name {
    font-weight: 500;
    display: block;
    margin-bottom: 4px;
}

.saved-method-item .method-details {
    font-size: 0.85rem;
    color: var(--text-muted);
}

.use-method-btn {
    padding: 6px 12px;
    font-size: 0.85rem;
}

/* Checkbox Group */
.checkbox-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.checkbox-group input[type="checkbox"] {
    width: 18px;
    height: 18px;
}

.checkbox-group label {
    margin: 0;
    cursor: pointer;
}

/* Form Actions */
.form-actions {
    margin-top: 30px;
    text-align: center;
}

.btn-lg {
    padding: 15px 30px;
    font-size: 1.1rem;
    min-width: 200px;
}

/* Deposit Info */
.deposit-info {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.info-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.info-card h3 {
    margin: 0 0 15px 0;
    color: var(--primary-color);
}

.info-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.info-list li {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    color: var(--text-dark);
}

.info-list li i {
    color: var(--success-color);
    margin-right: 10px;
    width: 16px;
}

.payment-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.payment-info-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.payment-info-item i {
    font-size: 1.2rem;
    color: var(--primary-color);
    margin-top: 2px;
}

.payment-info-item strong {
    display: block;
    margin-bottom: 4px;
    color: var(--text-dark);
}

.payment-info-item p {
    margin: 0;
    font-size: 0.85rem;
    color: var(--text-muted);
}

/* Transaction Detail Page */
.transaction-detail-page {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.transaction-card {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.transaction-header {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

.transaction-header .transaction-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    margin-right: 20px;
}

.transaction-title {
    flex: 1;
}

.transaction-title h2 {
    margin: 0 0 5px 0;
    color: var(--text-dark);
}

.transaction-id {
    font-size: 0.85rem;
    color: var(--text-muted);
}

.transaction-header .transaction-amount {
    text-align: right;
}

.transaction-header .amount {
    font-size: 1.8rem;
    font-weight: bold;
}

.transaction-status {
    margin-bottom: 30px;
    text-align: center;
}

.transaction-status .status {
    font-size: 1rem;
    padding: 8px 16px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.status-info {
    margin-top: 15px;
}

.status-info p {
    margin: 0;
    color: var(--text-muted);
}

.transaction-details {
    margin-bottom: 30px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-light);
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 500;
    color: var(--text-muted);
}

.detail-value {
    color: var(--text-dark);
    font-weight: 500;
}

/* M-Pesa Details */
.mpesa-details {
    margin-bottom: 30px;
    padding: 20px;
    background: var(--success-light);
    border-radius: 8px;
    border-left: 4px solid var(--success-color);
}

.mpesa-details h3 {
    margin: 0 0 15px 0;
    color: var(--success-color);
}

.mpesa-instructions {
    margin-top: 20px;
}

.instruction-card {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 20px;
    background: white;
    border-radius: 8px;
}

.instruction-card i {
    font-size: 2rem;
    color: var(--success-color);
    margin-top: 5px;
}

.instruction-content h4 {
    margin: 0 0 10px 0;
    color: var(--text-dark);
}

.instruction-content p {
    margin: 0 0 15px 0;
    color: var(--text-muted);
}

.instruction-steps ol {
    margin: 0;
    padding-left: 20px;
}

.instruction-steps li {
    margin-bottom: 5px;
    color: var(--text-dark);
}

/* Bank Transfer Details */
.bank-transfer-details {
    margin-bottom: 30px;
    padding: 20px;
    background: var(--info-light);
    border-radius: 8px;
    border-left: 4px solid var(--info-color);
}

.bank-transfer-details h3 {
    margin: 0 0 15px 0;
    color: var(--info-color);
}

.bank-details-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
}

.bank-info {
    margin-bottom: 20px;
}

.transfer-instructions h4 {
    margin: 0 0 10px 0;
    color: var(--text-dark);
}

.transfer-instructions p {
    margin: 0 0 10px 0;
    color: var(--text-muted);
}

/* Transaction Actions */
.transaction-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .deposit-content {
        grid-template-columns: 1fr;
    }
    
    .balance-card {
        flex-direction: column;
        text-align: center;
        min-width: auto;
    }
    
    .page-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .transaction-header {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .transaction-header .transaction-amount {
        text-align: center;
    }
    
    .detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .quick-amount-buttons {
        justify-content: center;
    }
    
    .action-buttons {
        grid-template-columns: 1fr;
    }
}

/* Withdrawal Specific Styles */
.withdrawal-form-container {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.payments-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.payments-header h1 {
    color: var(--primary-color);
    margin: 0;
    font-size: 2rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.balance-display {
    display: flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
}

.balance-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.balance-amount {
    font-size: 1.3rem;
    font-weight: bold;
}

.payment-methods {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 15px;
}

.payment-method-option {
    position: relative;
}

.payment-method-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.payment-method-label {
    display: block;
    padding: 20px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.payment-method-label:hover {
    border-color: var(--primary-color);
    background: var(--primary-light);
}

.payment-method-option input[type="radio"]:checked + .payment-method-label {
    border-color: var(--primary-color);
    background: var(--primary-light);
}

.method-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.method-info i {
    font-size: 1.8rem;
    color: var(--primary-color);
    width: 40px;
    text-align: center;
}

.method-info span {
    font-weight: 600;
    color: var(--text-dark);
    font-size: 1.1rem;
}

.method-info small {
    color: var(--text-muted);
    margin-left: auto;
    font-size: 0.85rem;
}

.payment-details {
    margin-top: 20px;
    padding: 20px;
    background: var(--bg-light);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.checkbox-group input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin-top: 2px;
}

.checkbox-label {
    color: var(--text-dark);
    line-height: 1.4;
    cursor: pointer;
    margin: 0;
}

.withdrawal-info {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-top: 30px;
}

.withdrawal-info h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.3rem;
}

.withdrawal-info h3 i {
    color: var(--primary-color);
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.info-item h4 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.info-item ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.info-item li {
    padding: 8px 0;
    color: var(--text-dark);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.info-item li:last-child {
    border-bottom: none;
}

.info-item strong {
    color: var(--primary-color);
    font-weight: 600;
}

.validation-error {
    color: var(--danger-color);
    font-size: 0.875rem;
    margin-top: 5px;
    display: block;
}

.form-validation-errors {
    background: var(--danger-light);
    color: var(--danger-color);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid var(--danger-color);
}

.form-validation-errors ul {
    margin: 0;
    padding-left: 20px;
}

.form-validation-errors li {
    margin-bottom: 8px;
}

.form-validation-errors li:last-child {
    margin-bottom: 0;
}

input.error {
    border-color: var(--danger-color) !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-help {
    display: block;
    margin-top: 5px;
    font-size: 0.85rem;
    color: var(--text-muted);
}

/* Withdrawal specific form actions */
.form-actions {
    margin-top: 30px;
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-large {
    padding: 15px 30px;
    font-size: 1.1rem;
    min-width: 200px;
}

/* Responsive adjustments for withdrawal */
@media (max-width: 768px) {
    .payments-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .balance-display {
        width: 100%;
        justify-content: space-between;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .method-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .method-info small {
        margin-left: 0;
    }
    
    .form-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .btn-large {
        min-width: auto;
    }
}
/* 
Cancel Withdrawal Page */
.cancel-withdrawal-container {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.cancel-withdrawal-content {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.warning-card {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 20px;
    background: var(--danger-light);
    border-radius: 8px;
    margin-bottom: 30px;
    border-left: 4px solid var(--danger-color);
}

.warning-icon {
    font-size: 2rem;
    color: var(--danger-color);
    margin-top: 5px;
}

.warning-text h3 {
    margin: 0 0 10px 0;
    color: var(--danger-color);
}

.warning-text p {
    margin: 0;
    color: var(--text-dark);
}

.transaction-summary {
    margin-bottom: 30px;
    padding: 20px;
    background: var(--bg-light);
    border-radius: 8px;
}

.transaction-summary h3 {
    margin: 0 0 15px 0;
    color: var(--text-dark);
}

.cancel-form {
    margin-top: 30px;
}

.cancel-form .form-group {
    margin-bottom: 20px;
}

.cancel-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-dark);
}

.cancel-form textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    resize: vertical;
    min-height: 100px;
}

.cancel-form textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* Next Steps Section */
.next-steps {
    margin-top: 20px;
    padding: 15px;
    background: var(--info-light);
    border-radius: 8px;
    border-left: 4px solid var(--info-color);
}

.next-steps h4 {
    margin: 0 0 10px 0;
    color: var(--info-color);
}

.next-steps ul {
    margin: 0;
    padding-left: 20px;
}

.next-steps li {
    margin-bottom: 5px;
    color: var(--text-dark);
}

/* Status Message */
.status-message {
    margin: 20px 0;
    padding: 15px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
    animation: slideIn 0.3s ease-out;
}

.status-message.alert-info {
    background: var(--info-light);
    color: var(--info-color);
    border: 1px solid var(--info-color);
}

.status-message.alert-success {
    background: var(--success-light);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.status-message.alert-error {
    background: var(--danger-light);
    color: var(--danger-color);
    border: 1px solid var(--danger-color);
}

.alert-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    margin-left: auto;
    opacity: 0.7;
}

.alert-close:hover {
    opacity: 1;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive adjustments for cancel withdrawal */
@media (max-width: 768px) {
    .warning-card {
        flex-direction: column;
        text-align: center;
    }
    
    .warning-icon {
        margin: 0 auto 15px;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .btn-lg {
        width: 100%;
    }
}