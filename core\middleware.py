"""
Security middleware for the betting platform
"""

import time
import json
import logging
from typing import Dict, Op<PERSON>, Tuple
from django.core.cache import cache
from django.http import JsonResponse, HttpResponse
from django.conf import settings
from django.utils.deprecation import MiddlewareMixin
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.exceptions import SuspiciousOperation
from accounts.utils import get_client_ip, log_security_event
from .cache_utils import CacheManager

logger = logging.getLogger(__name__)
User = get_user_model()


class RateLimitMiddleware(MiddlewareMixin):
    """
    Rate limiting middleware to prevent abuse and DDoS attacks
    """
    
    # Default rate limits (requests per minute)
    DEFAULT_LIMITS = {
        'api': 60,          # API endpoints
        'auth': 10,         # Authentication endpoints
        'payment': 5,       # Payment endpoints
        'global': 100,      # Global per IP
    }
    
    # Endpoints that require special rate limiting
    ENDPOINT_PATTERNS = {
        '/api/v1/accounts/login/': 'auth',
        '/api/v1/accounts/register/': 'auth',
        '/api/v1/payments/': 'payment',
        '/api/v1/': 'api',
        '/accounts/login/': 'auth',
        '/accounts/register/': 'auth',
    }
    
    def __init__(self, get_response):
        super().__init__(get_response)
        self.limits = getattr(settings, 'RATE_LIMITS', self.DEFAULT_LIMITS)
    
    def process_request(self, request):
        """
        Check rate limits before processing request
        """
        # Skip rate limiting for certain conditions
        if self._should_skip_rate_limiting(request):
            return None
        
        client_ip = get_client_ip(request)
        user_id = request.user.id if hasattr(request, 'user') and request.user.is_authenticated else None
        
        # Determine rate limit category
        limit_category = self._get_limit_category(request.path)
        limit = self.limits.get(limit_category, self.DEFAULT_LIMITS['global'])
        
        # Check rate limits
        if not self._check_rate_limit(client_ip, user_id, limit_category, limit):
            # Log security event
            if user_id:
                try:
                    user = User.objects.get(id=user_id)
                    log_security_event(
                        user, 
                        'RATE_LIMIT_EXCEEDED', 
                        f'Category: {limit_category}, Path: {request.path}',
                        client_ip
                    )
                except User.DoesNotExist:
                    pass
            
            logger.warning(
                f"Rate limit exceeded for IP {client_ip}, "
                f"user {user_id}, category {limit_category}"
            )
            
            return self._rate_limit_response(request)
        
        return None
    
    def _should_skip_rate_limiting(self, request) -> bool:
        """
        Determine if rate limiting should be skipped
        """
        # Skip for superusers (check if user attribute exists first)
        if hasattr(request, 'user') and request.user.is_authenticated and request.user.is_superuser:
            return True
        
        # Skip for static files
        if request.path.startswith('/static/') or request.path.startswith('/media/'):
            return True
        
        # Skip for health checks
        if request.path in ['/health/', '/ping/']:
            return True
        
        return False
    
    def _get_limit_category(self, path: str) -> str:
        """
        Determine rate limit category based on request path
        """
        for pattern, category in self.ENDPOINT_PATTERNS.items():
            if path.startswith(pattern):
                return category
        
        return 'global'
    
    def _check_rate_limit(self, client_ip: str, user_id: Optional[int], 
                         category: str, limit: int) -> bool:
        """
        Check if request is within rate limits
        """
        current_time = int(time.time())
        window_start = current_time - 60  # 1-minute window
        
        # Create cache keys
        ip_key = f"rate_limit:ip:{client_ip}:{category}:{window_start // 60}"
        user_key = f"rate_limit:user:{user_id}:{category}:{window_start // 60}" if user_id else None
        
        # Check IP-based rate limit
        ip_count = cache.get(ip_key, 0)
        if ip_count >= limit:
            return False
        
        # Check user-based rate limit (if authenticated)
        if user_key:
            user_count = cache.get(user_key, 0)
            if user_count >= limit:
                return False
        
        # Increment counters
        cache.set(ip_key, ip_count + 1, 120)  # 2-minute expiry
        if user_key:
            user_count = cache.get(user_key, 0)
            cache.set(user_key, user_count + 1, 120)
        
        return True
    
    def _rate_limit_response(self, request) -> HttpResponse:
        """
        Return appropriate rate limit response
        """
        if request.path.startswith('/api/'):
            return JsonResponse({
                'error': 'Rate limit exceeded',
                'message': 'Too many requests. Please try again later.',
                'retry_after': 60
            }, status=429)
        else:
            return HttpResponse(
                'Rate limit exceeded. Please try again later.',
                status=429,
                content_type='text/plain'
            )


class SecurityHeadersMiddleware(MiddlewareMixin):
    """
    Middleware to add security headers for XSS and other protections
    """
    
    def process_response(self, request, response):
        """
        Add security headers to response
        """
        # Content Security Policy
        csp_directives = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://code.jquery.com",
            "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com",
            "font-src 'self' https://fonts.gstatic.com",
            "img-src 'self' data: https:",
            "connect-src 'self' wss: ws:",
            "frame-ancestors 'none'",
            "base-uri 'self'",
            "form-action 'self'"
        ]
        response['Content-Security-Policy'] = '; '.join(csp_directives)
        
        # Additional security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        response['Permissions-Policy'] = 'geolocation=(), microphone=(), camera=()'
        
        # Remove server information
        if 'Server' in response:
            del response['Server']
        
        return response


class LoginAttemptMiddleware(MiddlewareMixin):
    """
    Middleware to monitor login attempts and implement account lockout
    """
    
    def __init__(self, get_response):
        super().__init__(get_response)
        self.max_attempts = getattr(settings, 'MAX_LOGIN_ATTEMPTS', 5)
        self.lockout_duration = getattr(settings, 'LOGIN_LOCKOUT_DURATION', 900)  # 15 minutes
    
    def process_request(self, request):
        """
        Check if IP or user is locked out from login attempts
        """
        if not self._is_login_request(request):
            return None
        
        client_ip = get_client_ip(request)
        
        # Check IP-based lockout
        if self._is_ip_locked_out(client_ip):
            logger.warning(f"Login attempt from locked IP: {client_ip}")
            return self._lockout_response(request, 'IP address temporarily locked')
        
        # Check user-based lockout if username provided
        username = self._get_username_from_request(request)
        if username and self._is_user_locked_out(username):
            logger.warning(f"Login attempt for locked user: {username} from IP: {client_ip}")
            return self._lockout_response(request, 'Account temporarily locked')
        
        return None
    
    def process_response(self, request, response):
        """
        Track failed login attempts
        """
        if not self._is_login_request(request):
            return response
        
        client_ip = get_client_ip(request)
        username = self._get_username_from_request(request)
        
        # Check if login failed
        if self._is_login_failure(request, response):
            self._record_failed_attempt(client_ip, username)
            
            # Log security event
            if username:
                try:
                    user = User.objects.get(phone_number=username)
                    log_security_event(
                        user,
                        'FAILED_LOGIN_ATTEMPT',
                        f'Attempt from IP: {client_ip}',
                        client_ip
                    )
                except User.DoesNotExist:
                    pass
        
        elif self._is_login_success(request, response):
            # Clear failed attempts on successful login
            self._clear_failed_attempts(client_ip, username)
        
        return response
    
    def _is_login_request(self, request) -> bool:
        """Check if this is a login request"""
        login_paths = ['/accounts/login/', '/api/v1/accounts/login/']
        return request.method == 'POST' and any(request.path.startswith(path) for path in login_paths)
    
    def _get_username_from_request(self, request) -> Optional[str]:
        """Extract username from request"""
        if hasattr(request, 'POST'):
            return request.POST.get('username') or request.POST.get('phone_number')
        return None
    
    def _is_ip_locked_out(self, client_ip: str) -> bool:
        """Check if IP is locked out"""
        key = f"login_lockout:ip:{client_ip}"
        return cache.get(key, 0) >= self.max_attempts
    
    def _is_user_locked_out(self, username: str) -> bool:
        """Check if user is locked out"""
        key = f"login_lockout:user:{username}"
        return cache.get(key, 0) >= self.max_attempts
    
    def _record_failed_attempt(self, client_ip: str, username: Optional[str]):
        """Record a failed login attempt"""
        # Record IP-based attempt
        ip_key = f"login_lockout:ip:{client_ip}"
        ip_count = cache.get(ip_key, 0) + 1
        cache.set(ip_key, ip_count, self.lockout_duration)
        
        # Record user-based attempt
        if username:
            user_key = f"login_lockout:user:{username}"
            user_count = cache.get(user_key, 0) + 1
            cache.set(user_key, user_count, self.lockout_duration)
    
    def _clear_failed_attempts(self, client_ip: str, username: Optional[str]):
        """Clear failed login attempts"""
        cache.delete(f"login_lockout:ip:{client_ip}")
        if username:
            cache.delete(f"login_lockout:user:{username}")
    
    def _is_login_failure(self, request, response) -> bool:
        """Check if login failed"""
        if response.status_code == 401:
            return True
        
        # Check for form errors in HTML response
        if hasattr(response, 'content') and b'error' in response.content.lower():
            return True
        
        return False
    
    def _is_login_success(self, request, response) -> bool:
        """Check if login succeeded"""
        return response.status_code in [200, 302] and not self._is_login_failure(request, response)
    
    def _lockout_response(self, request, message: str) -> HttpResponse:
        """Return lockout response"""
        if request.path.startswith('/api/'):
            return JsonResponse({
                'error': 'Account locked',
                'message': message,
                'retry_after': self.lockout_duration
            }, status=423)
        else:
            return HttpResponse(
                f'{message}. Please try again later.',
                status=423,
                content_type='text/plain'
            )


class SessionSecurityMiddleware(MiddlewareMixin):
    """
    Middleware to handle session security and timeouts
    """

    def __init__(self, get_response):
        super().__init__(get_response)
        self.timeout = getattr(settings, 'SESSION_TIMEOUT', 1800)  # 30 minutes default

    def process_request(self, request):
        """
        Check session timeout and security
        """
        if not hasattr(request, 'user') or not request.user.is_authenticated:
            return None

        # Check session timeout
        if self._is_session_expired(request):
            # Log security event
            log_security_event(
                request.user,
                'SESSION_TIMEOUT',
                'Session expired due to inactivity',
                get_client_ip(request)
            )

            # Clear session
            request.session.flush()

            # Redirect to login for web requests
            if not request.path.startswith('/api/'):
                from django.shortcuts import redirect
                from django.contrib import messages
                messages.warning(request, 'Your session has expired. Please log in again.')
                return redirect('accounts:login')
            else:
                # Return 401 for API requests
                return JsonResponse({
                    'error': 'Session expired',
                    'message': 'Your session has expired. Please log in again.'
                }, status=401)

        # Update last activity
        request.session['last_activity'] = time.time()

        # Check for session hijacking
        if self._detect_session_hijacking(request):
            log_security_event(
                request.user,
                'SESSION_HIJACKING_DETECTED',
                'Potential session hijacking detected',
                get_client_ip(request)
            )

            # Invalidate session
            request.session.flush()

            return JsonResponse({
                'error': 'Security violation',
                'message': 'Session security violation detected.'
            }, status=403)

        return None

    def _is_session_expired(self, request) -> bool:
        """
        Check if session has expired due to inactivity
        """
        last_activity = request.session.get('last_activity')

        if not last_activity:
            # No last activity recorded, set it now
            request.session['last_activity'] = time.time()
            return False

        # Check if session has expired
        return (time.time() - last_activity) > self.timeout

    def _detect_session_hijacking(self, request) -> bool:
        """
        Detect potential session hijacking
        """
        current_ip = get_client_ip(request)
        session_ip = request.session.get('session_ip')

        # Store IP on first request
        if not session_ip:
            request.session['session_ip'] = current_ip
            return False

        # Check for IP change (potential hijacking)
        if session_ip != current_ip:
            # Allow IP change for mobile users (they might switch networks)
            # But log it for monitoring
            logger.warning(
                f"IP change detected for user {getattr(request.user, 'id', 'unknown')}: "
                f"{session_ip} -> {current_ip}"
            )

            # Update session IP
            request.session['session_ip'] = current_ip

            # For now, don't block but could be enhanced with more sophisticated detection
            return False

        return False


class CachePerformanceMiddleware(MiddlewareMixin):
    """
    Middleware for cache performance monitoring and optimization
    """

    def __init__(self, get_response):
        super().__init__(get_response)
        self.cache_stats = {}

    def process_request(self, request):
        """
        Initialize request timing and cache stats
        """
        request._cache_start_time = time.time()
        request._cache_hits = 0
        request._cache_misses = 0
        return None

    def process_response(self, request, response):
        """
        Add cache performance headers and logging
        """
        if not hasattr(request, '_cache_start_time'):
            return response

        # Calculate request duration
        duration = time.time() - request._cache_start_time

        # Add performance headers for API requests
        if request.path.startswith('/api/'):
            response['X-Response-Time'] = f"{duration:.3f}s"
            response['X-Cache-Hits'] = str(getattr(request, '_cache_hits', 0))
            response['X-Cache-Misses'] = str(getattr(request, '_cache_misses', 0))

        # Log slow requests
        if duration > 1.0:  # Log requests taking more than 1 second
            logger.warning(
                f"Slow request: {request.method} {request.path} "
                f"took {duration:.3f}s"
            )

        # Add cache control headers for static content
        if self._is_static_content(request.path):
            response['Cache-Control'] = 'public, max-age=86400'  # 24 hours
            response['Expires'] = timezone.now() + timezone.timedelta(days=1)

        # Add cache headers for API responses
        elif request.path.startswith('/api/'):
            if request.method == 'GET':
                # Cache GET requests for a short time
                response['Cache-Control'] = 'public, max-age=60'  # 1 minute
            else:
                # Don't cache non-GET requests
                response['Cache-Control'] = 'no-cache, no-store, must-revalidate'

        return response

    def _is_static_content(self, path: str) -> bool:
        """Check if path is for static content"""
        static_extensions = ['.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.ico', '.svg']
        return any(path.endswith(ext) for ext in static_extensions)


class DatabaseQueryMiddleware(MiddlewareMixin):
    """
    Middleware for monitoring database query performance
    """

    def process_request(self, request):
        """Initialize query monitoring"""
        if settings.DEBUG:
            from django.db import reset_queries
            reset_queries()
        return None

    def process_response(self, request, response):
        """Log database query statistics"""
        if settings.DEBUG:
            from django.db import connection

            query_count = len(connection.queries)
            if query_count > 10:  # Log if more than 10 queries
                logger.warning(
                    f"High query count: {request.method} {request.path} "
                    f"executed {query_count} queries"
                )

                # Log slow queries
                for query in connection.queries:
                    query_time = float(query['time'])
                    if query_time > 0.1:  # Log queries taking more than 100ms
                        logger.warning(
                            f"Slow query ({query_time:.3f}s): {query['sql'][:200]}..."
                        )

            # Add query count header for API requests
            if request.path.startswith('/api/'):
                response['X-DB-Queries'] = str(query_count)

        return response
