/* Jackpot Styles */
.jackpot-card {
    background: #1a1a1a;
    border-radius: 8px;
    margin-bottom: 20px;
    overflow: hidden;
    border: 1px solid #333;
}

.jackpot-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 20px;
    background: linear-gradient(135deg, #2c5530 0%, #1e3a21 100%);
    border-bottom: 1px solid #333;
}

.jackpot-info h2 {
    color: #fff;
    font-size: 24px;
    font-weight: bold;
    margin: 0 0 8px 0;
}

.jackpot-info p {
    color: #ccc;
    margin: 0 0 12px 0;
    font-size: 14px;
}

.jackpot-meta {
    display: flex;
    gap: 20px;
    font-size: 12px;
    color: #999;
}

.jackpot-id {
    font-weight: bold;
    color: #4CAF50;
}

.jackpot-countdown {
    display: flex;
    align-items: center;
}

.countdown-timer {
    display: flex;
    gap: 15px;
}

.time-unit {
    text-align: center;
    background: rgba(0, 0, 0, 0.3);
    padding: 8px 12px;
    border-radius: 4px;
    min-width: 50px;
}

.time-value {
    display: block;
    font-size: 20px;
    font-weight: bold;
    color: #fff;
    line-height: 1;
}

.time-label {
    display: block;
    font-size: 10px;
    color: #ccc;
    text-transform: uppercase;
    margin-top: 2px;
}

.jackpot-matches {
    background: #1a1a1a;
}

.matches-header {
    display: grid;
    grid-template-columns: 50px 150px 1fr 80px 80px 80px;
    gap: 10px;
    padding: 15px 20px;
    background: #2a2a2a;
    border-bottom: 1px solid #333;
    font-weight: bold;
    color: #fff;
    font-size: 12px;
    text-transform: uppercase;
}

.match-row {
    display: grid;
    grid-template-columns: 50px 150px 1fr 80px 80px 80px;
    gap: 10px;
    padding: 15px 20px;
    border-bottom: 1px solid #333;
    align-items: center;
}

.match-row:hover {
    background: rgba(76, 175, 80, 0.05);
}

.match-number {
    font-weight: bold;
    color: #4CAF50;
    text-align: center;
}

.match-datetime {
    font-size: 12px;
}

.match-date {
    color: #fff;
    font-weight: bold;
}

.match-time {
    color: #ccc;
    margin: 2px 0;
}

.match-league {
    color: #999;
    font-size: 10px;
}

.match-teams {
    color: #fff;
}

.home-team {
    font-weight: bold;
    margin-bottom: 2px;
}

.away-team {
    color: #ccc;
}

.odds-cell {
    text-align: center;
}

.odds-btn {
    background: #333;
    color: #fff;
    border: 1px solid #555;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    min-width: 60px;
    transition: all 0.2s ease;
}

.odds-btn:hover {
    background: #4CAF50;
    border-color: #4CAF50;
    transform: translateY(-1px);
}

.odds-btn.selected {
    background: #4CAF50;
    border-color: #4CAF50;
    color: #fff;
}

.jackpot-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #2a2a2a;
    border-top: 1px solid #333;
}

.auto-pick {
    display: flex;
    gap: 10px;
}

.auto-pick-btn, .clear-all-btn {
    background: #333;
    color: #fff;
    border: 1px solid #555;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.auto-pick-btn:hover {
    background: #4CAF50;
    border-color: #4CAF50;
}

.clear-all-btn:hover {
    background: #f44336;
    border-color: #f44336;
}

.jackpot-summary {
    display: flex;
    gap: 20px;
    align-items: center;
    font-size: 12px;
}

.jackpot-summary > div {
    text-align: center;
}

.jackpot-summary span:first-child {
    display: block;
    color: #999;
    margin-bottom: 2px;
}

.jackpot-summary span:last-child {
    display: block;
    color: #fff;
    font-weight: bold;
}

.place-jackpot-bet-btn, .login-to-bet-btn {
    background: #4CAF50;
    color: #fff;
    border: none;
    padding: 12px 24px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    font-size: 14px;
    transition: all 0.2s ease;
}

.place-jackpot-bet-btn:hover, .login-to-bet-btn:hover {
    background: #45a049;
    transform: translateY(-1px);
}

.login-to-bet-btn {
    background: #2196F3;
}

.login-to-bet-btn:hover {
    background: #1976D2;
}

.place-jackpot-bet-btn:disabled {
    background: #666;
    cursor: not-allowed;
    transform: none;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #999;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 20px;
    color: #555;
}

.empty-state h3 {
    color: #fff;
    margin-bottom: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .jackpot-header {
        flex-direction: column;
        gap: 20px;
    }
    
    .matches-header,
    .match-row {
        grid-template-columns: 30px 120px 1fr 60px 60px 60px;
        gap: 5px;
        padding: 10px 15px;
        font-size: 11px;
    }
    
    .jackpot-actions {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .jackpot-summary {
        justify-content: space-around;
    }
    
    .countdown-timer {
        gap: 8px;
    }
    
    .time-unit {
        padding: 6px 8px;
        min-width: 40px;
    }
    
    .time-value {
        font-size: 16px;
    }
}
