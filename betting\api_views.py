from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.db.models import Prefetch, Q
from django.core.paginator import Paginator
from decimal import Decimal

from .models import Bet, BetSelection
from core.cache_utils import cache_result, UserCache
from core.query_optimization import QueryOptimizer

class PlaceBetAPIView(APIView):
    def post(self, request):
        return Response({"message": "Place Bet API - Coming Soon"})

class BetHistoryAPIView(APIView):
    """Optimized bet history API with caching and pagination"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        # Get query parameters
        status_filter = request.GET.get('status')
        page = int(request.GET.get('page', 1))
        page_size = min(int(request.GET.get('page_size', 20)), 50)

        # Use optimized query
        bets_queryset = QueryOptimizer.get_user_recent_bets_optimized(
            request.user.id, limit=1000  # Get more for pagination
        )

        # Apply status filter
        if status_filter and status_filter in ['pending', 'won', 'lost', 'void']:
            bets_queryset = bets_queryset.filter(status=status_filter)

        # Pagination
        paginator = Paginator(bets_queryset, page_size)
        page_obj = paginator.get_page(page)

        # Serialize data
        bets_data = []
        for bet in page_obj:
            bets_data.append({
                'id': str(bet.id),
                'bet_type': bet.bet_type,
                'stake': float(bet.stake),
                'potential_winnings': float(bet.potential_winnings),
                'actual_winnings': float(bet.actual_winnings or 0),
                'status': bet.status,
                'placed_at': bet.placed_at.isoformat(),
                'settled_at': bet.settled_at.isoformat() if bet.settled_at else None,
                'selections_count': bet.selections_count,
                'selections': [
                    {
                        'id': selection.id,
                        'odds_value': float(selection.odds_value),
                        'status': selection.status,
                        'event': f"{selection.odds.market.event.home_team} vs {selection.odds.market.event.away_team}",
                        'market': selection.odds.market.name,
                        'selection': selection.odds.selection,
                    }
                    for selection in bet.selections.all()
                ]
            })

        return Response({
            'bets': bets_data,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_pages': paginator.num_pages,
                'total_count': paginator.count,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous(),
            }
        })

class BetSlipAPIView(APIView):
    def get(self, request):
        return Response({"message": "Bet Slip API - Coming Soon"})

class BetDetailAPIView(APIView):
    """Optimized bet detail API with caching"""
    permission_classes = [IsAuthenticated]

    @cache_result(timeout=300, key_prefix='bet_detail')
    def get_bet_data(self, bet_id, user_id):
        """Get optimized bet data"""
        bet = get_object_or_404(
            Bet.objects.select_related('user').prefetch_related(
                Prefetch(
                    'selections',
                    queryset=BetSelection.objects.select_related(
                        'odds', 'odds__market', 'odds__market__event', 'odds__market__event__sport'
                    )
                )
            ),
            id=bet_id,
            user_id=user_id
        )

        return {
            'id': str(bet.id),
            'bet_type': bet.bet_type,
            'stake': float(bet.stake),
            'potential_winnings': float(bet.potential_winnings),
            'actual_winnings': float(bet.actual_winnings or 0),
            'status': bet.status,
            'placed_at': bet.placed_at.isoformat(),
            'settled_at': bet.settled_at.isoformat() if bet.settled_at else None,
            'selections_count': bet.selections_count,
            'selections': [
                {
                    'id': selection.id,
                    'odds_value': float(selection.odds_value),
                    'status': selection.status,
                    'event': {
                        'id': selection.odds.market.event.id,
                        'home_team': selection.odds.market.event.home_team,
                        'away_team': selection.odds.market.event.away_team,
                        'start_time': selection.odds.market.event.start_time.isoformat(),
                        'status': selection.odds.market.event.status,
                        'sport': selection.odds.market.event.sport.name,
                    },
                    'market': {
                        'id': selection.odds.market.id,
                        'name': selection.odds.market.name,
                        'market_type': selection.odds.market.market_type,
                    },
                    'odds': {
                        'id': selection.odds.id,
                        'selection': selection.odds.selection,
                        'odds_value': float(selection.odds.odds_value),
                    }
                }
                for selection in bet.selections.all()
            ]
        }

    def get(self, request, bet_id):
        try:
            bet_data = self.get_bet_data(bet_id, request.user.id)
            return Response(bet_data)
        except Bet.DoesNotExist:
            return Response(
                {'error': 'Bet not found'},
                status=status.HTTP_404_NOT_FOUND
            )

class CancelBetAPIView(APIView):
    def post(self, request, bet_id):
        return Response({"message": f"Cancel Bet API for {bet_id} - Coming Soon"})

class MultiBetAPIView(APIView):
    def post(self, request):
        return Response({"message": "Multi Bet API - Coming Soon"})

class CalculateMultiBetAPIView(APIView):
    def post(self, request):
        return Response({"message": "Calculate Multi Bet API - Coming Soon"})

class AddToSlipAPIView(APIView):
    def post(self, request):
        return Response({"message": "Add to Slip API - Coming Soon"})

class RemoveFromSlipAPIView(APIView):
    def post(self, request):
        return Response({"message": "Remove from Slip API - Coming Soon"})

class ClearSlipAPIView(APIView):
    def post(self, request):
        return Response({"message": "Clear Slip API - Coming Soon"})