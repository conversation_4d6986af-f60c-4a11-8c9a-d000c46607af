/* Sports Module Styles */

/* Sports List Page */
.sports-banner {
    background-color: var(--secondary-dark);
    border-radius: 8px;
    margin-bottom: 20px;
    padding: 20px;
}

.banner-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.banner-content h1 {
    color: var(--text-primary);
    margin: 0;
    font-size: 24px;
}

.search-bar {
    flex: 1;
    max-width: 400px;
}

.search-form {
    display: flex;
    gap: 10px;
}

.search-input {
    flex: 1;
    padding: 10px 15px;
    background-color: var(--primary-dark);
    border: 1px solid var(--border-color);
    border-radius: 25px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s;
    color: var(--text-primary);
}

.search-input::placeholder {
    color: var(--text-muted);
}

.search-input:focus {
    border-color: var(--accent-green);
}

.search-btn {
    background: var(--accent-green);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.3s;
}

.search-btn:hover {
    background: var(--accent-dark-green);
}

/* Event Tabs */
.event-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.tab-list {
    display: flex;
    gap: 10px;
    overflow-x: auto;
    padding-bottom: 5px;
}

.tab-btn {
    background: var(--secondary-dark);
    border: none;
    color: var(--text-secondary);
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.tab-btn:hover {
    background: var(--tertiary-dark);
    color: var(--text-primary);
}

.tab-btn.active {
    background: var(--accent-green);
    color: white;
}

.event-filters {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-group {
    position: relative;
}

.filter-btn {
    background: var(--secondary-dark);
    border: none;
    color: var(--text-secondary);
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-select {
    background: var(--secondary-dark);
    border: none;
    color: var(--text-secondary);
    padding: 10px 30px 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23b0b0b0' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: calc(100% - 10px) center;
}

/* Tab Content */
.tab-content-container {
    margin-bottom: 30px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Featured Events */
.featured-events {
    margin-bottom: 40px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.section-header h2 {
    color: var(--text-primary);
    margin: 0;
    font-size: 18px;
}

.view-all {
    color: var(--accent-green);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
}

.featured-slider {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.featured-event-card {
    background: var(--secondary-dark);
    border-radius: 8px;
    padding: 15px;
    transition: transform 0.3s, box-shadow 0.3s;
}

.featured-event-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0,0,0,0.2);
}

.event-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.sport-badge {
    background: var(--accent-green);
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: bold;
}

.event-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-upcoming { background: var(--warning-orange); color: white; }
.status-live { background: var(--danger-red); color: white; animation: pulse 2s infinite; }
.status-finished { background: var(--text-muted); color: white; }
.status-cancelled { background: var(--text-muted); color: white; }

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.event-teams {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin: 15px 0;
}

.team {
    font-weight: bold;
    font-size: 16px;
    color: var(--text-primary);
}

.vs {
    color: var(--text-secondary);
    font-size: 14px;
}

.event-score {
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    color: var(--danger-red);
    margin: 10px 0;
}

.event-time {
    text-align: center;
    color: var(--text-secondary);
    font-size: 14px;
    margin-bottom: 15px;
}

.event-link {
    display: block;
    text-align: center;
    background: var(--accent-green);
    color: white;
    padding: 10px;
    border-radius: 5px;
    text-decoration: none;
    transition: background-color 0.3s;
}

.event-link:hover {
    background: var(--accent-dark-green);
    color: white;
}

/* Sports Categories */
.sports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.sport-card {
    background: var(--secondary-dark);
    border-radius: 8px;
    padding: 25px;
    transition: transform 0.3s, box-shadow 0.3s;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.sport-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0,0,0,0.2);
}

.sport-icon {
    font-size: 48px;
    color: var(--accent-green);
    margin-bottom: 15px;
}

.sport-info h3 {
    color: var(--text-primary);
    margin: 0 0 10px 0;
    font-size: 20px;
}

.event-count {
    color: var(--text-secondary);
    margin: 0 0 20px 0;
    font-size: 14px;
}

.sport-link {
    background: var(--accent-green);
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s;
    font-weight: 500;
}

.sport-link:hover {
    background: var(--accent-dark-green);
    color: white;
}

/* Quick Links */
.quick-links h3 {
    color: var(--text-primary);
    margin-bottom: 15px;
}

.links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.quick-link {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 20px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s;
}

.quick-link.live {
    background: var(--danger-red);
    color: white;
}

.quick-link.upcoming {
    background: var(--warning-orange);
    color: white;
}

.quick-link.filter {
    background: var(--accent-green);
    color: white;
}

.quick-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    color: white;
}

/* Sport Detail Page */
.sport-detail-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.sport-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: var(--secondary-dark);
    border-radius: 8px;
}

.sport-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.sport-info .sport-icon {
    font-size: 48px;
    color: var(--accent-green);
}

.sport-info h1 {
    margin: 0;
    color: var(--text-primary);
}

.sport-description {
    color: var(--text-secondary);
    margin: 5px 0 0 0;
}

/* Filters Section */
.filters-section {
    background: var(--secondary-dark);
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
}

.filters-form {
    display: flex;
    gap: 20px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 14px;
}

.filter-group input,
.filter-group select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
}

/* Events Section */
.events-section {
    background: var(--secondary-dark);
    border-radius: 8px;
    overflow: hidden;
}

.events-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background: var(--tertiary-dark);
}

.events-header h2 {
    margin: 0;
    color: var(--text-primary);
}

.events-list {
    padding: 0;
}

.event-card {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.3s;
    background: var(--secondary-dark);
    margin-bottom: 15px;
    border-radius: 8px;
}

.event-card:hover {
    background: var(--tertiary-dark);
}

.event-card:last-child {
    border-bottom: none;
}

.event-info {
    margin-bottom: 15px;
}

.event-details {
    display: flex;
    gap: 15px;
    align-items: center;
    margin: 10px 0;
    flex-wrap: wrap;
}

.league {
    background: var(--tertiary-dark);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    color: var(--text-secondary);
}

.start-time {
    color: var(--text-secondary);
    font-size: 14px;
}

/* Markets Preview */
.markets-preview {
    margin: 15px 0;
}

.market-preview {
    margin-bottom: 15px;
}

.market-name {
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 8px;
    font-size: 14px;
}

.odds-preview {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.odds-btn {
    background: var(--primary-dark);
    border: none;
    color: var(--text-primary);
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 80px;
}

.odds-btn:hover {
    background: var(--accent-green);
    color: white;
}

.odds-btn.selected {
    background: var(--accent-green);
    color: white;
}

.selection {
    font-size: 12px;
    font-weight: 500;
}

.odds-value {
    font-size: 14px;
    font-weight: bold;
}

.event-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* Event Detail Page */
.event-detail-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.event-header {
    background: var(--secondary-dark);
    padding: 30px;
    border-radius: 8px;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.event-teams {
    display: flex;
    align-items: center;
    gap: 30px;
    margin: 20px 0;
}

.team {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.team-name {
    font-size: 24px;
    font-weight: bold;
    color: var(--text-primary);
}

.team-score {
    font-size: 32px;
    font-weight: bold;
    color: var(--danger-red);
}

.live-indicator {
    background: var(--danger-red);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: bold;
    animation: pulse 2s infinite;
}

/* Markets Section */
.markets-section {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.market-group {
    background: var(--secondary-dark);
    border-radius: 8px;
    overflow: hidden;
}

.market-type-header {
    background: var(--tertiary-dark);
    padding: 20px;
    margin: 0;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
}

.market-card {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.market-card:last-child {
    border-bottom: none;
}

.market-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.market-name {
    margin: 0;
    color: var(--text-primary);
    font-size: 16px;
}

.market-parameter {
    color: var(--text-secondary);
    font-size: 14px;
}

.odds-container {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.odds-btn {
    background: var(--primary-dark);
    border: none;
    color: var(--text-primary);
    padding: 15px 20px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 100px;
    position: relative;
}

.odds-btn:hover {
    background: var(--accent-green);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
}

.odds-btn.selected {
    background: var(--accent-green);
    color: white;
}

.odds-changed {
    position: relative;
}

.odds-up {
    border-color: var(--success-green);
    color: var(--success-green);
}

.odds-down {
    border-color: var(--danger-red);
    color: var(--danger-red);
}

.odds-change {
    position: absolute;
    top: -5px;
    right: -5px;
    background: currentColor;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
}

/* Search Page */
.search-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.search-form-container {
    background: var(--secondary-dark);
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 30px;
}

.advanced-search-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.search-row {
    display: flex;
    gap: 20px;
    align-items: end;
    flex-wrap: wrap;
}

.search-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
    flex: 1;
    min-width: 200px;
}

.search-group label {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 14px;
}

.search-group input,
.search-group select {
    background-color: var(--primary-dark);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 10px 12px;
    border-radius: 4px;
    font-size: 14px;
}

.search-actions {
    display: flex;
    gap: 10px;
}

.search-results {
    background: var(--secondary-dark);
    border-radius: 8px;
    overflow: hidden;
}

.results-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background: var(--tertiary-dark);
}

.results-header h2 {
    color: var(--text-primary);
    margin: 0;
}

.search-query {
    margin: 10px 0 0 0;
    color: var(--text-secondary);
}

.search-result {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.event-sport {
    flex-shrink: 0;
    color: var(--accent-green);
}

.search-suggestions {
    background: var(--secondary-dark);
    padding: 25px;
    border-radius: 8px;
    margin-top: 30px;
}

.search-suggestions h3 {
    color: var(--text-primary);
    margin-bottom: 15px;
}

.suggestion-tags {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.suggestion-tag {
    background: var(--tertiary-dark);
    color: var(--text-primary);
    padding: 8px 16px;
    border-radius: 20px;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s;
}

.suggestion-tag:hover {
    background: var(--accent-green);
    color: white;
}

/* Pagination */
.pagination-container {
    padding: 20px;
    display: flex;
    justify-content: center;
    border-top: 1px solid var(--border-color);
}

.pagination {
    display: flex;
    gap: 10px;
    align-items: center;
}

.page-link {
    padding: 8px 16px;
    background: var(--accent-green);
    color: white;
    text-decoration: none;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.page-link:hover {
    background: var(--accent-dark-green);
    color: white;
}

.page-info {
    padding: 8px 16px;
    color: var(--text-secondary);
}

/* No Results States */
.no-events,
.no-results,
.no-markets,
.no-sports {
    text-align: center;
    padding: 60px 20px;
    color: #7f8c8d;
}

.no-events i,
.no-results i,
.no-markets i,
.no-sports i {
    font-size: 48px;
    margin-bottom: 20px;
    color: #bdc3c7;
}

.no-events h3,
.no-results h3,
.no-markets h3,
.no-sports h3 {
    color: #2c3e50;
    margin-bottom: 15px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sports-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-bar {
        max-width: none;
    }
    
    .featured-grid,
    .sports-grid {
        grid-template-columns: 1fr;
    }
    
    .event-teams {
        gap: 15px;
    }
    
    .team-name {
        font-size: 18px;
    }
    
    .team-score {
        font-size: 24px;
    }
    
    .odds-container {
        justify-content: center;
    }
    
    .search-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-group {
        min-width: auto;
    }
    
    .search-result {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }
    
    .event-header {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }
    
    .sport-header {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .sports-container,
    .sport-detail-container,
    .search-container,
    .event-detail-container {
        padding: 10px;
    }
    
    .odds-btn {
        min-width: 80px;
        padding: 10px 15px;
    }
    
    .event-teams {
        gap: 10px;
    }
    
    .links-grid {
        grid-template-columns: 1fr;
    }
}