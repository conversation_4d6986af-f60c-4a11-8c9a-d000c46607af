// Sidebar Navigation JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeSidebar();
});

function initializeSidebar() {
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebarToggle');
    const mobileOverlay = document.getElementById('mobileOverlay');
    
    // Toggle sidebar on mobile
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('open');
            
            // Show/hide overlay on mobile
            if (mobileOverlay) {
                mobileOverlay.classList.toggle('active');
            }
        });
    }
    
    // Close sidebar when clicking on overlay
    if (mobileOverlay) {
        mobileOverlay.addEventListener('click', function() {
            sidebar.classList.remove('open');
            mobileOverlay.classList.remove('active');
        });
    }
    
    // Handle sport links active state
    const sportLinks = document.querySelectorAll('.sport-link');
    
    sportLinks.forEach(link => {
        // Check if current URL matches the link
        if (window.location.pathname.includes(link.getAttribute('href'))) {
            // Remove active class from all links
            sportLinks.forEach(l => l.classList.remove('active'));
            
            // Add active class to current link
            link.classList.add('active');
        }
        
        // Add click event to set active class
        link.addEventListener('click', function() {
            sportLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
        });
    });
    
    // Close sidebar on window resize (desktop mode)
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            if (mobileOverlay && mobileOverlay.classList.contains('active')) {
                mobileOverlay.classList.remove('active');
            }
        }
    });
}

// Function to toggle sidebar visibility
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mobileOverlay = document.getElementById('mobileOverlay');
    
    if (sidebar) {
        sidebar.classList.toggle('open');
    }
    
    if (mobileOverlay) {
        mobileOverlay.classList.toggle('active');
    }
}

// Export functions for use in other scripts
window.sidebarModule = {
    toggleSidebar
};