"""
Core views for the Betika clone application
"""

from django.shortcuts import render
from django.db.models import Q, Count, Prefetch
from django.utils import timezone
from datetime import timedelta

from sports.models import Sport, Event, Market, Odds
from betting.models import Bet


def home_view(request):
    """
    Enhanced home view with real betting data
    """
    # Get featured/highlighted events
    highlighted_events = Event.objects.filter(
        Q(status__in=['upcoming', 'live']) &
        Q(is_featured=True) |
        Q(start_time__gte=timezone.now()) &
        Q(start_time__lte=timezone.now() + timedelta(hours=24))
    ).select_related('sport').prefetch_related(
        Prefetch(
            'markets',
            queryset=Market.objects.filter(
                is_active=True,
                market_type='Match Result'
            ).prefetch_related(
                Prefetch(
                    'odds',
                    queryset=Odds.objects.filter(is_active=True).order_by('display_order')
                )
            )
        )
    ).order_by('-is_featured', 'start_time')[:10]

    # Get upcoming events for next 7 days
    upcoming_events = Event.objects.filter(
        status='upcoming',
        start_time__gte=timezone.now(),
        start_time__lte=timezone.now() + timedelta(days=7)
    ).select_related('sport').prefetch_related(
        Prefetch(
            'markets',
            queryset=Market.objects.filter(
                is_active=True,
                market_type='Match Result'
            ).prefetch_related(
                Prefetch(
                    'odds',
                    queryset=Odds.objects.filter(is_active=True).order_by('display_order')
                )
            )
        )
    ).order_by('start_time')[:20]

    # Get live events
    live_events = Event.objects.filter(
        status='live'
    ).select_related('sport').prefetch_related(
        Prefetch(
            'markets',
            queryset=Market.objects.filter(
                is_active=True,
                market_type='Match Result'
            ).prefetch_related(
                Prefetch(
                    'odds',
                    queryset=Odds.objects.filter(is_active=True).order_by('display_order')
                )
            )
        )
    ).order_by('-start_time')[:15]

    # Get sports with active events count
    sports_with_counts = Sport.objects.annotate(
        active_events_count=Count(
            'events',
            filter=Q(events__status__in=['upcoming', 'live'])
        )
    ).filter(active_events_count__gt=0).order_by('display_order', 'name')

    # Get user's recent bets if authenticated
    recent_bets = []
    if request.user.is_authenticated:
        recent_bets = Bet.objects.filter(
            user=request.user
        ).select_related('user').prefetch_related(
            'selections__market__event',
            'selections__odds'
        ).order_by('-placed_at')[:5]

    context = {
        'highlighted_events': highlighted_events,
        'upcoming_events': upcoming_events,
        'live_events': live_events,
        'sports_with_counts': sports_with_counts,
        'recent_bets': recent_bets,
        'page_title': 'Home - Sports Betting',
        'show_betslip': True,  # Enable betslip functionality
    }

    return render(request, 'home.html', context)


def terms_view(request):
    """Terms and Conditions page"""
    return render(request, 'core/terms.html', {
        'page_title': 'Terms and Conditions'
    })


def privacy_view(request):
    """Privacy Policy page"""
    return render(request, 'core/privacy.html', {
        'page_title': 'Privacy Policy'
    })
