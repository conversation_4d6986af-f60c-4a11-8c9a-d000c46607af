{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/pages/home.css' %}">
<link rel="stylesheet" href="{% static 'css/sports.css' %}">
{% endblock %}

{% block content %}
<!-- Promotional Banner -->
<div class="promo-banner">
    <div class="promo-content">
        <div class="promo-image">
            <i class="fas fa-trophy"></i>
        </div>
        <div class="promo-text">
            <h2>{{ page_title }}</h2>
            <p>Explore all sports and find the <strong>best betting opportunities</strong> available!</p>
        </div>
        <div class="promo-action">
            <a href="{% url 'home' %}" class="btn btn-outline">Back to Home</a>
            <button class="promo-close">&times;</button>
        </div>
    </div>
</div>

<!-- Event Navigation Tabs -->
<div class="event-tabs">
    <div class="tab-list">
        <button class="tab-btn active" data-tab="featured">Featured</button>
        <button class="tab-btn" data-tab="all-sports">All Sports</button>
        <button class="tab-btn" data-tab="live-now">Live Now</button>
        <button class="tab-btn" data-tab="upcoming">Starting Soon</button>
    </div>
    
    <div class="event-filters">
        <div class="filter-group">
            <button class="filter-btn">
                <i class="fas fa-filter"></i>
                Filters
            </button>
        </div>
        <div class="filter-group">
            <select class="filter-select">
                <option>All Sports</option>
                <option>Soccer</option>
                <option>Basketball</option>
                <option>Tennis</option>
            </select>
        </div>
    </div>
</div>

<!-- Tab Content -->
<div class="tab-content-container">
    <!-- Featured Events Tab -->
    <div class="tab-content active" id="featured">
        {% if featured_events %}
        <div class="featured-events">
            <div class="featured-slider">
                {% for event in featured_events %}
                <div class="featured-event-card">
                    <div class="event-header">
                        <span class="sport-badge">{{ event.sport.name }}</span>
                        <span class="event-status status-{{ event.status }}">{{ event.get_status_display }}</span>
                    </div>
                    <div class="event-teams">
                        <div class="team">{{ event.home_team }}</div>
                        <div class="vs">vs</div>
                        <div class="team">{{ event.away_team }}</div>
                    </div>
                    {% if event.get_score_display %}
                    <div class="event-score">{{ event.get_score_display }}</div>
                    {% endif %}
                    <div class="event-time">{{ event.start_time|date:"M d, H:i" }}</div>
                    <div class="event-markets">
                        <div class="market">
                            <div class="market-header">
                                <span>1</span>
                                <span>X</span>
                                <span>2</span>
                            </div>
                            <div class="odds-row">
                                <button class="odds-btn" data-event-id="{{ event.id }}" data-selection="1" data-odds="1.85">1.85</button>
                                <button class="odds-btn" data-event-id="{{ event.id }}" data-selection="X" data-odds="3.40">3.40</button>
                                <button class="odds-btn" data-event-id="{{ event.id }}" data-selection="2" data-odds="4.20">4.20</button>
                            </div>
                        </div>
                    </div>
                    <a href="{% url 'sports:event_detail' event.id %}" class="event-link">+186 Markets</a>
                </div>
                {% endfor %}
            </div>
        </div>
        {% else %}
        <div class="empty-state">
            <i class="fas fa-star"></i>
            <h3>No Featured Events</h3>
            <p>Check back later for featured matches</p>
        </div>
        {% endif %}
        
        <!-- Popular Events Section -->
        <div class="section-header">
            <h2>Popular Events</h2>
            <a href="{% url 'sports:search' %}" class="view-all">View All</a>
        </div>
        
        <div class="events-list">
            {% for event in featured_events|slice:":5" %}
            <div class="event-card">
                <div class="event-header">
                    <div class="event-info">
                        <span class="competition">
                            <i class="fas fa-globe"></i>
                            {{ event.league }}
                        </span>
                        <span class="event-time">{{ event.start_time|date:"d/m, H:i" }}</span>
                    </div>
                </div>
                <div class="event-match">
                    <div class="teams">
                        <span class="team-name">{{ event.home_team }}</span>
                        <span class="vs">vs</span>
                        <span class="team-name">{{ event.away_team }}</span>
                    </div>
                    <div class="odds-row">
                        <div class="market-header">
                            <span class="market-label">Match Result</span>
                            <span class="market-options">
                                <span>1</span>
                                <span>X</span>
                                <span>2</span>
                            </span>
                        </div>
                        <div class="odds-buttons">
                            <button class="odds-btn" data-event-id="{{ event.id }}" data-selection="1" data-odds="1.85">1.85</button>
                            <button class="odds-btn" data-event-id="{{ event.id }}" data-selection="X" data-odds="3.40">3.40</button>
                            <button class="odds-btn" data-event-id="{{ event.id }}" data-selection="2" data-odds="4.20">4.20</button>
                        </div>
                    </div>
                    <div class="market-expansion">
                        <a href="{% url 'sports:event_detail' event.id %}" class="expand-btn">+186 Markets</a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    
    <!-- All Sports Tab -->
    <div class="tab-content" id="all-sports">
        <div class="sports-grid">
            {% for sport in sports %}
            <div class="sport-card">
                <div class="sport-icon">
                    {% if sport.icon %}
                    <i class="{{ sport.icon }}"></i>
                    {% else %}
                    <i class="fas fa-trophy"></i>
                    {% endif %}
                </div>
                <div class="sport-info">
                    <h3>{{ sport.name }}</h3>
                    <p class="event-count">{{ sport.active_events_count }} active event{{ sport.active_events_count|pluralize }}</p>
                </div>
                <a href="{% url 'sports:sport_detail' sport.slug %}" class="sport-link">
                    View Events
                    <i class="fas fa-arrow-right"></i>
                </a>
            </div>
            {% empty %}
            <div class="no-sports">
                <i class="fas fa-exclamation-circle"></i>
                <h3>No Sports Available</h3>
                <p>No sports categories are available at the moment.</p>
            </div>
            {% endfor %}
        </div>
    </div>
    
    <!-- Live Now Tab -->
    <div class="tab-content" id="live-now">
        <div class="empty-state">
            <i class="fas fa-broadcast-tower"></i>
            <h3>No Live Events</h3>
            <p>There are no live events at the moment. Check back later.</p>
        </div>
    </div>
    
    <!-- Upcoming Tab -->
    <div class="tab-content" id="upcoming">
        <div class="empty-state">
            <i class="fas fa-clock"></i>
            <h3>Starting Soon</h3>
            <p>Events starting in the next hour will appear here.</p>
        </div>
    </div>
</div>

<!-- Quick Links -->
<section class="quick-links">
    <h3>Quick Access</h3>
    <div class="links-grid">
        <a href="{% url 'sports:search' %}?status=live" class="quick-link live">
            <i class="fas fa-broadcast-tower"></i>
            Live Events
        </a>
        <a href="{% url 'sports:search' %}?status=upcoming" class="quick-link upcoming">
            <i class="fas fa-clock"></i>
            Upcoming Events
        </a>
        <a href="{% url 'sports:filter' %}" class="quick-link filter">
            <i class="fas fa-filter"></i>
            Advanced Filter
        </a>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/sports.js' %}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Tab switching
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons and contents
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));
                
                // Add active class to clicked button
                this.classList.add('active');
                
                // Show corresponding content
                const tabId = this.dataset.tab;
                document.getElementById(tabId).classList.add('active');
            });
        });
        
        // Initialize odds buttons
        const oddsButtons = document.querySelectorAll('.odds-btn');
        oddsButtons.forEach(button => {
            button.addEventListener('click', function() {
                this.classList.toggle('selected');
                
                // Get selection data
                const eventId = this.dataset.eventId;
                const selection = this.dataset.selection;
                const odds = this.dataset.odds;
                
                // If selected, add to betslip
                if (this.classList.contains('selected')) {
                    if (window.sportsModule && window.sportsModule.addToBetSlip) {
                        window.sportsModule.addToBetSlip({
                            eventId: eventId,
                            selection: selection,
                            oddsValue: parseFloat(odds)
                        });
                    }
                } else {
                    if (window.sportsModule && window.sportsModule.removeFromBetSlip) {
                        window.sportsModule.removeFromBetSlip(selection);
                    }
                }
            });
        });
    });
</script>
{% endblock %}