{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/pages/home.css' %}">
<link rel="stylesheet" href="{% static 'css/sports.css' %}">
{% endblock %}

{% block content %}
<!-- Promotional Banner -->
<div class="promo-banner">
    <div class="promo-content">
        <div class="promo-image">
            <i class="fas fa-trophy"></i>
        </div>
        <div class="promo-text">
            <h2>{{ page_title }}</h2>
            <p>Explore all sports and find the <strong>best betting opportunities</strong> available!</p>
        </div>
        <div class="promo-action">
            <a href="{% url 'home' %}" class="btn btn-outline">Back to Home</a>
            <button class="promo-close">&times;</button>
        </div>
    </div>
</div>

<!-- Event Navigation Tabs -->
<div class="event-tabs">
    <div class="tab-list">
        <button class="tab-btn active" data-tab="highlights">Highlights</button>
        <button class="tab-btn" data-tab="upcoming">Upcoming</button>
        <button class="tab-btn" data-tab="live">Live</button>
        <button class="tab-btn" data-tab="all-sports">All Sports</button>
    </div>

    <div class="event-filters">
        <div class="filter-group">
            <button class="filter-btn">
                <i class="fas fa-filter"></i>
                Filters
            </button>
        </div>
        <div class="filter-group">
            <select class="filter-select">
                <option>All Sports</option>
                {% for sport in sports %}
                <option value="{{ sport.slug }}">{{ sport.name }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="filter-group">
            <select class="filter-select">
                <option>1-5</option>
                <option>1-10</option>
            </select>
        </div>
    </div>
</div>

<!-- Events Content -->
<div class="events-content">
    <div class="tab-content active" id="highlights">
        {% for event in featured_events %}
        <div class="event-card" data-event-id="{{ event.id }}">
            <div class="event-header">
                <div class="event-info">
                    <span class="competition">
                        <i class="fas fa-globe"></i>
                        {{ event.sport.name }} • {{ event.league|default:"League" }}
                    </span>
                    <span class="event-time">{{ event.start_time|date:"d/m, H:i" }}</span>
                    {% if event.status == 'live' %}
                    <span class="live-indicator">
                        <i class="fas fa-circle"></i> LIVE
                    </span>
                    {% endif %}
                </div>
            </div>
            <div class="event-match">
                <div class="teams">
                    <span class="team-name">{{ event.home_team }}</span>
                    <span class="vs">vs</span>
                    <span class="team-name">{{ event.away_team }}</span>
                </div>

                {% for market in event.markets.all %}
                {% if market.market_type == 'Match Result' %}
                <div class="odds-row">
                    <div class="market-header">
                        <span class="market-label">{{ market.name|default:"Teams" }}</span>
                        <span class="market-options">
                            {% for odds in market.odds.all %}
                            <span>{{ odds.selection }}</span>
                            {% endfor %}
                        </span>
                    </div>
                    <div class="odds-buttons">
                        {% for odds in market.odds.all %}
                        <button class="odds-btn"
                                data-odds="{{ odds.odds_value }}"
                                data-selection="{{ odds.selection }}"
                                data-event-id="{{ event.id }}"
                                data-event-name="{{ event.home_team }} vs {{ event.away_team }}"
                                data-market-type="{{ market.market_type }}"
                                data-market-name="{{ market.name }}"
                                data-odds-id="{{ odds.id }}"
                                data-market-id="{{ market.id }}">{{ odds.odds_value }}</button>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
                {% endfor %}

                <div class="market-expansion">
                    <button class="expand-btn" data-event-id="{{ event.id }}">+{{ event.markets.count }} Markets</button>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="empty-state">
            <i class="fas fa-star"></i>
            <h3>No Featured Events</h3>
            <p>Check back later for featured matches</p>
        </div>
        {% endfor %}

        <!-- Load More Button -->
        {% if featured_events %}
        <div class="load-more">
            <button class="btn btn-outline" id="load-more-highlights">Load More Events</button>
        </div>
        {% endif %}
    </div>

    <div class="tab-content" id="upcoming">
        {% for event in featured_events %}
        <div class="event-card" data-event-id="{{ event.id }}">
            <div class="event-header">
                <div class="event-info">
                    <span class="competition">
                        <i class="fas fa-clock"></i>
                        {{ event.sport.name }} • {{ event.league|default:"League" }}
                    </span>
                    <span class="event-time">{{ event.start_time|date:"d/m, H:i" }}</span>
                </div>
            </div>
            <div class="event-match">
                <div class="teams">
                    <span class="team-name">{{ event.home_team }}</span>
                    <span class="vs">vs</span>
                    <span class="team-name">{{ event.away_team }}</span>
                </div>

                {% for market in event.markets.all %}
                {% if market.market_type == 'Match Result' %}
                <div class="odds-row">
                    <div class="market-header">
                        <span class="market-label">{{ market.name|default:"Teams" }}</span>
                        <span class="market-options">
                            {% for odds in market.odds.all %}
                            <span>{{ odds.selection }}</span>
                            {% endfor %}
                        </span>
                    </div>
                    <div class="odds-buttons">
                        {% for odds in market.odds.all %}
                        <button class="odds-btn"
                                data-odds="{{ odds.odds_value }}"
                                data-selection="{{ odds.selection }}"
                                data-event-id="{{ event.id }}"
                                data-event-name="{{ event.home_team }} vs {{ event.away_team }}"
                                data-market-type="{{ market.market_type }}"
                                data-market-name="{{ market.name }}"
                                data-odds-id="{{ odds.id }}"
                                data-market-id="{{ market.id }}">{{ odds.odds_value }}</button>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
                {% endfor %}

                <div class="market-expansion">
                    <button class="expand-btn" data-event-id="{{ event.id }}">+{{ event.markets.count }} Markets</button>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="empty-state">
            <i class="fas fa-calendar-alt"></i>
            <h3>No Upcoming Events</h3>
            <p>Check back later for upcoming matches</p>
        </div>
        {% endfor %}
    </div>

    <div class="tab-content" id="live">
        <div class="empty-state">
            <i class="fas fa-circle"></i>
            <h3>No Live Events</h3>
            <p>There are no live events at the moment</p>
        </div>
    </div>

    <div class="tab-content" id="all-sports">
        <div class="sports-grid">
            {% for sport in sports %}
            <div class="sport-card">
                <div class="sport-icon">
                    {% if sport.icon %}
                    <i class="{{ sport.icon }}"></i>
                    {% else %}
                    <i class="fas fa-trophy"></i>
                    {% endif %}
                </div>
                <div class="sport-info">
                    <h3>{{ sport.name }}</h3>
                    <p class="event-count">{{ sport.active_events_count }} active event{{ sport.active_events_count|pluralize }}</p>
                </div>
                <a href="{% url 'sports:sport_detail' sport.slug %}" class="sport-link">
                    View Events
                    <i class="fas fa-arrow-right"></i>
                </a>
            </div>
            {% empty %}
            <div class="no-sports">
                <i class="fas fa-exclamation-circle"></i>
                <h3>No Sports Available</h3>
                <p>No sports categories are available at the moment.</p>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- Betslip Component -->
<div class="betslip-container" id="betslip-container">
    <div class="betslip" id="betslip">
        <div class="betslip-header">
            <h3>Betslip (<span id="betslip-count">0</span>)</h3>
            <button class="betslip-clear" id="clear-betslip">Clear All</button>
        </div>

        <div class="betslip-content" id="betslip-content">
            <div class="betslip-empty" id="betslip-empty">
                <i class="fas fa-ticket-alt"></i>
                <p>Your betslip is empty</p>
                <small>Click on odds to add selections</small>
            </div>

            <div class="betslip-selections" id="betslip-selections">
                <!-- Dynamic selections will be added here -->
            </div>
        </div>

        <div class="betslip-footer" id="betslip-footer" style="display: none;">
            <div class="betslip-totals">
                <div class="total-odds">
                    <span>Total Odds:</span>
                    <span id="total-odds">0.00</span>
                </div>
                <div class="potential-win">
                    <span>Potential Win:</span>
                    <span id="potential-win">KES 0.00</span>
                </div>
            </div>

            <div class="stake-input">
                <label for="stake-amount">Stake Amount (KES)</label>
                <input type="number" id="stake-amount" placeholder="100" min="1" step="1">
            </div>

            {% if user.is_authenticated %}
            <div class="user-balance">
                <span>Balance: KES {{ user.balance|default:"0.00" }}</span>
                {% if user.balance < 100 %}
                <a href="{% url 'payments:deposit' %}" class="deposit-link">Deposit</a>
                {% endif %}
            </div>

            <button class="place-bet-btn" id="place-bet-btn">
                <i class="fas fa-check"></i>
                Place Bet
            </button>
            {% else %}
            <div class="login-prompt">
                <p>Login to place bets</p>
                <a href="{% url 'accounts:login' %}" class="btn btn-primary">Login</a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/pages/home.js' %}"></script>
<script src="{% static 'js/sports.js' %}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Tab switching
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons and contents
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));
                
                // Add active class to clicked button
                this.classList.add('active');
                
                // Show corresponding content
                const tabId = this.dataset.tab;
                document.getElementById(tabId).classList.add('active');
            });
        });
        
        // Initialize odds buttons
        const oddsButtons = document.querySelectorAll('.odds-btn');
        oddsButtons.forEach(button => {
            button.addEventListener('click', function() {
                this.classList.toggle('selected');
                
                // Get selection data
                const eventId = this.dataset.eventId;
                const selection = this.dataset.selection;
                const odds = this.dataset.odds;
                
                // If selected, add to betslip
                if (this.classList.contains('selected')) {
                    if (window.sportsModule && window.sportsModule.addToBetSlip) {
                        window.sportsModule.addToBetSlip({
                            eventId: eventId,
                            selection: selection,
                            oddsValue: parseFloat(odds)
                        });
                    }
                } else {
                    if (window.sportsModule && window.sportsModule.removeFromBetSlip) {
                        window.sportsModule.removeFromBetSlip(selection);
                    }
                }
            });
        });
    });
</script>
{% endblock %}