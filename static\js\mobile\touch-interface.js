/**
 * Touch Interface Enhancements for Mobile Devices
 * Betika Clone Mobile Experience
 */

class TouchInterface {
    constructor() {
        this.isMobile = this.detectMobile();
        this.touchStartX = 0;
        this.touchStartY = 0;
        this.touchEndX = 0;
        this.touchEndY = 0;
        this.minSwipeDistance = 50;
        
        if (this.isMobile) {
            this.init();
        }
    }

    detectMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               window.innerWidth <= 768;
    }

    init() {
        this.setupTouchEvents();
        this.setupMobileNavigation();
        this.setupTouchFeedback();
        this.setupSwipeGestures();
        this.optimizeForTouch();
    }

    setupTouchEvents() {
        // Add touch event listeners
        document.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true });
        document.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false });
        document.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: true });
        
        // Prevent zoom on double tap for betting buttons
        document.addEventListener('touchend', this.preventDoubleTabZoom.bind(this));
    }

    handleTouchStart(e) {
        this.touchStartX = e.touches[0].clientX;
        this.touchStartY = e.touches[0].clientY;
        
        // Add touch feedback
        const target = e.target.closest('.odds-button, .btn, .nav-tab');
        if (target) {
            target.classList.add('touch-active');
        }
    }

    handleTouchMove(e) {
        // Prevent scrolling when interacting with betslip
        if (e.target.closest('.betslip')) {
            const betslip = e.target.closest('.betslip');
            const isScrollable = betslip.scrollHeight > betslip.clientHeight;
            
            if (!isScrollable) {
                e.preventDefault();
            }
        }
    }

    handleTouchEnd(e) {
        this.touchEndX = e.changedTouches[0].clientX;
        this.touchEndY = e.changedTouches[0].clientY;
        
        // Remove touch feedback
        const target = e.target.closest('.odds-button, .btn, .nav-tab');
        if (target) {
            setTimeout(() => {
                target.classList.remove('touch-active');
            }, 150);
        }
        
        this.handleSwipe();
    }

    preventDoubleTabZoom(e) {
        const target = e.target.closest('.odds-button, .btn');
        if (target) {
            e.preventDefault();
        }
    }

    setupMobileNavigation() {
        // Mobile menu toggle
        const mobileMenuToggle = document.createElement('button');
        mobileMenuToggle.className = 'mobile-menu-toggle';
        mobileMenuToggle.innerHTML = '<i class="fas fa-bars"></i>';
        mobileMenuToggle.setAttribute('aria-label', 'Toggle navigation menu');
        
        document.body.appendChild(mobileMenuToggle);
        
        // Mobile betslip toggle
        const mobileBetslipToggle = document.createElement('button');
        mobileBetslipToggle.className = 'mobile-betslip-toggle';
        mobileBetslipToggle.innerHTML = '<i class="fas fa-ticket-alt"></i><span class="badge">0</span>';
        mobileBetslipToggle.setAttribute('aria-label', 'Toggle betslip');
        
        document.body.appendChild(mobileBetslipToggle);
        
        // Event listeners
        mobileMenuToggle.addEventListener('click', this.toggleMobileMenu.bind(this));
        mobileBetslipToggle.addEventListener('click', this.toggleMobileBetslip.bind(this));
        
        // Update betslip badge
        this.updateBetslipBadge();
    }

    toggleMobileMenu() {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.querySelector('.sidebar-overlay') || this.createOverlay();
        
        sidebar.classList.toggle('open');
        overlay.classList.toggle('active');
        
        // Prevent body scroll when menu is open
        document.body.style.overflow = sidebar.classList.contains('open') ? 'hidden' : '';
    }

    toggleMobileBetslip() {
        const betslip = document.querySelector('.betslip');
        betslip.classList.toggle('open');
        
        // Prevent body scroll when betslip is open
        document.body.style.overflow = betslip.classList.contains('open') ? 'hidden' : '';
    }

    createOverlay() {
        const overlay = document.createElement('div');
        overlay.className = 'sidebar-overlay';
        overlay.addEventListener('click', this.toggleMobileMenu.bind(this));
        document.body.appendChild(overlay);
        return overlay;
    }

    updateBetslipBadge() {
        const badge = document.querySelector('.mobile-betslip-toggle .badge');
        const betslipItems = document.querySelectorAll('.betslip-item');
        
        if (badge) {
            badge.textContent = betslipItems.length;
            badge.style.display = betslipItems.length > 0 ? 'block' : 'none';
        }
    }

    setupTouchFeedback() {
        // Add CSS for touch feedback
        const style = document.createElement('style');
        style.textContent = `
            .touch-active {
                transform: scale(0.95);
                opacity: 0.8;
                transition: all 0.1s ease;
            }
            
            .odds-button:active,
            .btn:active {
                transform: scale(0.95);
            }
            
            /* Improve touch targets */
            .odds-button,
            .btn,
            .nav-tab {
                min-height: 44px;
                min-width: 44px;
            }
            
            /* Better spacing for touch */
            @media (max-width: 767px) {
                .odds-button {
                    margin: 2px;
                }
                
                .nav-tab {
                    margin: 0 4px;
                }
            }
        `;
        document.head.appendChild(style);
    }

    setupSwipeGestures() {
        // Swipe to navigate between sports
        document.addEventListener('touchend', () => {
            const swipeDistance = Math.abs(this.touchEndX - this.touchStartX);
            const swipeDirection = this.touchEndX > this.touchStartX ? 'right' : 'left';
            
            if (swipeDistance > this.minSwipeDistance) {
                this.handleSwipeNavigation(swipeDirection);
            }
        });
    }

    handleSwipe() {
        const deltaX = this.touchEndX - this.touchStartX;
        const deltaY = this.touchEndY - this.touchStartY;
        const absDeltaX = Math.abs(deltaX);
        const absDeltaY = Math.abs(deltaY);
        
        // Only handle horizontal swipes
        if (absDeltaX > absDeltaY && absDeltaX > this.minSwipeDistance) {
            if (deltaX > 0) {
                this.handleSwipeRight();
            } else {
                this.handleSwipeLeft();
            }
        }
    }

    handleSwipeRight() {
        // Swipe right to open sidebar
        const sidebar = document.querySelector('.sidebar');
        if (!sidebar.classList.contains('open')) {
            this.toggleMobileMenu();
        }
    }

    handleSwipeLeft() {
        // Swipe left to close sidebar or open betslip
        const sidebar = document.querySelector('.sidebar');
        if (sidebar.classList.contains('open')) {
            this.toggleMobileMenu();
        } else {
            // Open betslip if there are items
            const betslipItems = document.querySelectorAll('.betslip-item');
            if (betslipItems.length > 0) {
                this.toggleMobileBetslip();
            }
        }
    }

    handleSwipeNavigation(direction) {
        const navTabs = document.querySelectorAll('.nav-tab');
        const activeTab = document.querySelector('.nav-tab.active');
        
        if (!activeTab || navTabs.length === 0) return;
        
        const currentIndex = Array.from(navTabs).indexOf(activeTab);
        let nextIndex;
        
        if (direction === 'left' && currentIndex < navTabs.length - 1) {
            nextIndex = currentIndex + 1;
        } else if (direction === 'right' && currentIndex > 0) {
            nextIndex = currentIndex - 1;
        }
        
        if (nextIndex !== undefined) {
            navTabs[nextIndex].click();
            navTabs[nextIndex].scrollIntoView({ behavior: 'smooth', inline: 'center' });
        }
    }

    optimizeForTouch() {
        // Add viewport meta tag if not present
        if (!document.querySelector('meta[name="viewport"]')) {
            const viewport = document.createElement('meta');
            viewport.name = 'viewport';
            viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
            document.head.appendChild(viewport);
        }
        
        // Disable text selection on interactive elements
        const style = document.createElement('style');
        style.textContent = `
            .odds-button,
            .btn,
            .nav-tab,
            .betslip-item {
                -webkit-user-select: none;
                -moz-user-select: none;
                -ms-user-select: none;
                user-select: none;
                -webkit-tap-highlight-color: transparent;
            }
            
            /* Improve scrolling on iOS */
            .content-area,
            .betslip,
            .sidebar {
                -webkit-overflow-scrolling: touch;
            }
            
            /* Better button styling for touch */
            .odds-button {
                border: 2px solid transparent;
                transition: all 0.2s ease;
            }
            
            .odds-button:hover,
            .odds-button:focus {
                border-color: var(--accent-green);
                outline: none;
            }
        `;
        document.head.appendChild(style);
    }

    // Utility methods
    vibrate(pattern = [100]) {
        if ('vibrate' in navigator) {
            navigator.vibrate(pattern);
        }
    }

    showToast(message, duration = 3000) {
        const toast = document.createElement('div');
        toast.className = 'mobile-toast';
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 12px 24px;
            border-radius: 24px;
            z-index: 10000;
            font-size: 14px;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        
        document.body.appendChild(toast);
        
        // Animate in
        setTimeout(() => {
            toast.style.opacity = '1';
        }, 10);
        
        // Remove after duration
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, duration);
    }

    // Event handlers for betslip updates
    onBetslipUpdate() {
        this.updateBetslipBadge();
        
        // Haptic feedback
        this.vibrate([50]);
        
        // Show toast
        const betslipItems = document.querySelectorAll('.betslip-item');
        if (betslipItems.length === 1) {
            this.showToast('Selection added to betslip');
        }
    }
}

// Initialize touch interface when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.touchInterface = new TouchInterface();
    
    // Listen for betslip updates
    document.addEventListener('betslipUpdated', () => {
        if (window.touchInterface) {
            window.touchInterface.onBetslipUpdate();
        }
    });
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TouchInterface;
}
