{% extends 'base.html' %}
{% load static %}

{% block title %}Register - Betika Clone{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="text-center">Create Your Account</h3>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}
                        
                        <div class="form-group mb-3">
                            <label for="{{ form.phone_number.id_for_label }}" class="form-label">
                                Phone Number *
                            </label>
                            {{ form.phone_number }}
                            {% if form.phone_number.errors %}
                                <div class="text-danger small">
                                    {{ form.phone_number.errors }}
                                </div>
                            {% endif %}
                            <div class="form-text">{{ form.phone_number.help_text }}</div>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">
                                Email Address *
                            </label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="text-danger small">
                                    {{ form.email.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.first_name.id_for_label }}" class="form-label">
                                        First Name *
                                    </label>
                                    {{ form.first_name }}
                                    {% if form.first_name.errors %}
                                        <div class="text-danger small">
                                            {{ form.first_name.errors }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.last_name.id_for_label }}" class="form-label">
                                        Last Name *
                                    </label>
                                    {{ form.last_name }}
                                    {% if form.last_name.errors %}
                                        <div class="text-danger small">
                                            {{ form.last_name.errors }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="{{ form.date_of_birth.id_for_label }}" class="form-label">
                                Date of Birth *
                            </label>
                            {{ form.date_of_birth }}
                            {% if form.date_of_birth.errors %}
                                <div class="text-danger small">
                                    {{ form.date_of_birth.errors }}
                                </div>
                            {% endif %}
                            <div class="form-text">{{ form.date_of_birth.help_text }}</div>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="{{ form.password1.id_for_label }}" class="form-label">
                                Password *
                            </label>
                            {{ form.password1 }}
                            {% if form.password1.errors %}
                                <div class="text-danger small">
                                    {{ form.password1.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="{{ form.password2.id_for_label }}" class="form-label">
                                Confirm Password *
                            </label>
                            {{ form.password2 }}
                            {% if form.password2.errors %}
                                <div class="text-danger small">
                                    {{ form.password2.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="terms" required>
                                <label class="form-check-label" for="terms">
                                    I agree to the <a href="/terms/" target="_blank">Terms and Conditions</a> 
                                    and <a href="/privacy/" target="_blank">Privacy Policy</a>
                                </label>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100">
                            Create Account
                        </button>
                    </form>
                    
                    <div class="text-center mt-3">
                        <p>Already have an account? <a href="{% url 'accounts:login' %}">Login here</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.justify-content-center {
    justify-content: center;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
}

@media (max-width: 768px) {
    .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

.form-check-input {
    margin-right: 0.5rem;
}

.form-text {
    font-size: 0.875em;
    color: var(--text-muted);
}

.text-danger {
    color: var(--danger-color) !important;
}

.small {
    font-size: 0.875em;
}
</style>
{% endblock %}