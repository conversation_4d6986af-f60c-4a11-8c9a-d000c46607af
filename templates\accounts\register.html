{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Betzide!</title>
    <link rel="stylesheet" href="{% static 'css/base.css' %}">
    <link rel="stylesheet" href="{% static 'css/auth.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="auth-body">
    <!-- Back to Home -->
    <div class="back-to-home">
        <a href="{% url 'home' %}" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Back to home
        </a>
    </div>

    <!-- Main Auth Container -->
    <div class="auth-container">
        <div class="auth-card register-card">
            <!-- Left Side - Illustration -->
            <div class="auth-illustration">
                <div class="illustration-content">
                    <div class="colorful-shapes">
                        <div class="shape shape-1"></div>
                        <div class="shape shape-2"></div>
                        <div class="shape shape-3"></div>
                        <div class="shape shape-4"></div>
                        <div class="shape shape-5"></div>
                        <div class="shape shape-6"></div>
                    </div>
                    <div class="character register-character">
                        <div class="character-body"></div>
                        <div class="character-head"></div>
                        <div class="character-arm-left"></div>
                        <div class="character-arm-right"></div>
                        <div class="character-trophy"></div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Register Form -->
            <div class="auth-form-section register-form-section">
                <!-- Logo -->
                <div class="auth-logo">
                    <span class="logo-text">B!</span>
                </div>

                <!-- Form Header -->
                <div class="auth-header">
                    <h2>Create Account</h2>
                    <p>Join Betzide and start winning today</p>
                </div>

                <!-- Register Form -->
                <form method="post" class="auth-form register-form">
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                        <div class="error-message">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}

                    <!-- Phone Number Field -->
                    <div class="form-group">
                        <label for="{{ form.phone_number.id_for_label }}">Phone Number *</label>
                        <div class="phone-input-group">
                            <div class="country-code">
                                <span class="flag-emoji">🇰🇪</span>
                                <span>+254</span>
                            </div>
                            {{ form.phone_number }}
                        </div>
                        {% if form.phone_number.errors %}
                            <div class="field-error">
                                {{ form.phone_number.errors }}
                            </div>
                        {% endif %}
                        {% if form.phone_number.help_text %}
                            <div class="field-help">{{ form.phone_number.help_text }}</div>
                        {% endif %}
                    </div>

                    <!-- Email Field -->
                    <div class="form-group">
                        <label for="{{ form.email.id_for_label }}">Email Address *</label>
                        {{ form.email }}
                        {% if form.email.errors %}
                            <div class="field-error">
                                {{ form.email.errors }}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Name Fields -->
                    <div class="form-row">
                        <div class="form-group half-width">
                            <label for="{{ form.first_name.id_for_label }}">First Name *</label>
                            {{ form.first_name }}
                            {% if form.first_name.errors %}
                                <div class="field-error">
                                    {{ form.first_name.errors }}
                                </div>
                            {% endif %}
                        </div>
                        <div class="form-group half-width">
                            <label for="{{ form.last_name.id_for_label }}">Last Name *</label>
                            {{ form.last_name }}
                            {% if form.last_name.errors %}
                                <div class="field-error">
                                    {{ form.last_name.errors }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <!-- Date of Birth Field -->
                    <div class="form-group">
                        <label for="{{ form.date_of_birth.id_for_label }}">Date of Birth *</label>
                        {{ form.date_of_birth }}
                        {% if form.date_of_birth.errors %}
                            <div class="field-error">
                                {{ form.date_of_birth.errors }}
                            </div>
                        {% endif %}
                        {% if form.date_of_birth.help_text %}
                            <div class="field-help">{{ form.date_of_birth.help_text }}</div>
                        {% endif %}
                    </div>

                    <!-- Password Fields -->
                    <div class="form-row">
                        <div class="form-group half-width">
                            <label for="{{ form.password1.id_for_label }}">Password *</label>
                            <div class="password-input-group">
                                {{ form.password1 }}
                                <button type="button" class="password-toggle" onclick="togglePassword('{{ form.password1.id_for_label }}', 'password1-eye')">
                                    <i class="fas fa-eye" id="password1-eye"></i>
                                </button>
                            </div>
                            {% if form.password1.errors %}
                                <div class="field-error">
                                    {{ form.password1.errors }}
                                </div>
                            {% endif %}
                        </div>
                        <div class="form-group half-width">
                            <label for="{{ form.password2.id_for_label }}">Confirm Password *</label>
                            <div class="password-input-group">
                                {{ form.password2 }}
                                <button type="button" class="password-toggle" onclick="togglePassword('{{ form.password2.id_for_label }}', 'password2-eye')">
                                    <i class="fas fa-eye" id="password2-eye"></i>
                                </button>
                            </div>
                            {% if form.password2.errors %}
                                <div class="field-error">
                                    {{ form.password2.errors }}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="terms" name="terms" required class="custom-checkbox">
                            <label for="terms" class="checkbox-label">
                                I agree to the <a href="/terms/" target="_blank">Terms and Conditions</a>
                                and <a href="/privacy/" target="_blank">Privacy Policy</a>
                            </label>
                        </div>
                    </div>

                    <!-- Register Button -->
                    <button type="submit" class="auth-btn auth-btn-primary">
                        Create Account
                    </button>
                </form>

                <!-- Additional Links -->
                <div class="auth-links">
                    <div class="login-link">
                        <span>Already have an account?</span>
                        <a href="{% url 'accounts:login' %}">Login</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        function togglePassword(fieldId, eyeId) {
            const passwordField = document.getElementById(fieldId);
            const eyeIcon = document.getElementById(eyeId);

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                eyeIcon.classList.remove('fa-eye');
                eyeIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                eyeIcon.classList.remove('fa-eye-slash');
                eyeIcon.classList.add('fa-eye');
            }
        }

        // Form validation and enhancement
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('.auth-form');
            const phoneInput = document.getElementById('{{ form.phone_number.id_for_label }}');

            // Format phone number input
            if (phoneInput) {
                phoneInput.addEventListener('input', function(e) {
                    let value = e.target.value.replace(/\D/g, '');
                    if (value.startsWith('254')) {
                        value = value.substring(3);
                    }
                    if (value.startsWith('0')) {
                        value = value.substring(1);
                    }
                    e.target.value = value;
                });
            }

            // Password strength indicator
            const password1 = document.getElementById('{{ form.password1.id_for_label }}');
            if (password1) {
                password1.addEventListener('input', function(e) {
                    const strength = checkPasswordStrength(e.target.value);
                    // You can add visual feedback here
                });
            }
        });

        function checkPasswordStrength(password) {
            let strength = 0;
            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;
            return strength;
        }
    </script>
</body>
</html>