/**
 * Multi-Bet JavaScript functionality
 * Handles dynamic odds calculation and bet slip management for multi-bets
 */

class MultiBetManager {
    constructor() {
        this.selections = new Map(); // Map of selection_id -> selection_data
        this.betSlip = document.getElementById('bet-slip');
        this.totalOddsElement = document.getElementById('total-odds');
        this.potentialWinningsElement = document.getElementById('potential-winnings');
        this.stakeInput = document.getElementById('stake-input');
        this.betTypeSelect = document.getElementById('bet-type-select');
        this.placeBetButton = document.getElementById('place-bet-button');
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.updateBetSlip();
    }
    
    setupEventListeners() {
        // Stake input change
        if (this.stakeInput) {
            this.stakeInput.addEventListener('input', () => {
                this.updatePotentialWinnings();
            });
        }
        
        // Bet type change
        if (this.betTypeSelect) {
            this.betTypeSelect.addEventListener('change', () => {
                this.updateOddsCalculation();
            });
        }
        
        // Place bet button
        if (this.placeBetButton) {
            this.placeBetButton.addEventListener('click', () => {
                this.placeBet();
            });
        }
        
        // Listen for odds selection clicks
        document.addEventListener('click', (event) => {
            if (event.target.classList.contains('odds-button')) {
                this.handleOddsSelection(event.target);
            }
        });
        
        // Listen for selection removal
        document.addEventListener('click', (event) => {
            if (event.target.classList.contains('remove-selection')) {
                const selectionId = event.target.dataset.selectionId;
                this.removeSelection(selectionId);
            }
        });
    }
    
    handleOddsSelection(oddsButton) {
        const oddsId = oddsButton.dataset.oddsId;
        const marketId = oddsButton.dataset.marketId;
        const eventId = oddsButton.dataset.eventId;
        const oddsValue = parseFloat(oddsButton.dataset.oddsValue);
        const oddsName = oddsButton.textContent.trim();
        const eventName = oddsButton.dataset.eventName || 'Unknown Event';
        const marketName = oddsButton.dataset.marketName || 'Unknown Market';
        
        // Check if this market is already selected
        const existingSelection = this.findSelectionByMarket(marketId);
        if (existingSelection) {
            // Replace existing selection from same market
            this.removeSelection(existingSelection.id);
        }
        
        // Add new selection
        const selectionId = `${marketId}_${oddsId}`;
        const selection = {
            id: selectionId,
            oddsId: oddsId,
            marketId: marketId,
            eventId: eventId,
            oddsValue: oddsValue,
            oddsName: oddsName,
            eventName: eventName,
            marketName: marketName
        };
        
        this.addSelection(selection);
        
        // Update button state
        this.updateOddsButtonStates();
    }
    
    addSelection(selection) {
        // Validate selection limits
        if (this.selections.size >= 20) {
            this.showNotification('Maximum 20 selections allowed', 'error');
            return;
        }
        
        // Check for incompatible selections
        if (!this.isSelectionCompatible(selection)) {
            this.showNotification('This selection is not compatible with existing selections', 'error');
            return;
        }
        
        this.selections.set(selection.id, selection);
        this.updateBetSlip();
        this.updateOddsCalculation();
        
        this.showNotification(`Added: ${selection.eventName} - ${selection.oddsName}`, 'success');
    }
    
    removeSelection(selectionId) {
        if (this.selections.has(selectionId)) {
            const selection = this.selections.get(selectionId);
            this.selections.delete(selectionId);
            this.updateBetSlip();
            this.updateOddsCalculation();
            this.updateOddsButtonStates();
            
            this.showNotification(`Removed: ${selection.eventName} - ${selection.oddsName}`, 'info');
        }
    }
    
    findSelectionByMarket(marketId) {
        for (const [id, selection] of this.selections) {
            if (selection.marketId === marketId) {
                return selection;
            }
        }
        return null;
    }
    
    isSelectionCompatible(newSelection) {
        // Check for duplicate markets
        if (this.findSelectionByMarket(newSelection.marketId)) {
            return false;
        }
        
        // Check for incompatible selections from same event
        for (const [id, selection] of this.selections) {
            if (selection.eventId === newSelection.eventId) {
                // For now, allow multiple markets from same event
                // In practice, you'd have more complex compatibility rules
                continue;
            }
        }
        
        return true;
    }
    
    updateBetSlip() {
        if (!this.betSlip) return;
        
        if (this.selections.size === 0) {
            this.betSlip.innerHTML = `
                <div class="empty-bet-slip">
                    <p>Select odds to build your multi-bet</p>
                </div>
            `;
            return;
        }
        
        let html = '<div class="bet-slip-header"><h3>Bet Slip</h3></div>';
        html += '<div class="selections-list">';
        
        for (const [id, selection] of this.selections) {
            html += `
                <div class="selection-item" data-selection-id="${id}">
                    <div class="selection-details">
                        <div class="event-name">${selection.eventName}</div>
                        <div class="market-name">${selection.marketName}</div>
                        <div class="odds-name">${selection.oddsName}</div>
                    </div>
                    <div class="selection-odds">
                        <span class="odds-value">${selection.oddsValue.toFixed(2)}</span>
                        <button class="remove-selection" data-selection-id="${id}">×</button>
                    </div>
                </div>
            `;
        }
        
        html += '</div>';
        
        // Add bet controls
        html += this.generateBetControls();
        
        this.betSlip.innerHTML = html;
        
        // Re-attach event listeners for new elements
        this.attachBetSlipListeners();
    }
    
    generateBetControls() {
        const selectionsCount = this.selections.size;
        const minSelectionsForMulti = 2;
        const minSelectionsForSystem = 3;
        
        let betTypeOptions = '<option value="single">Single Bet</option>';
        
        if (selectionsCount >= minSelectionsForMulti) {
            betTypeOptions += '<option value="multi">Multi Bet</option>';
            betTypeOptions += '<option value="accumulator">Accumulator</option>';
        }
        
        if (selectionsCount >= minSelectionsForSystem) {
            betTypeOptions += '<option value="system">System Bet</option>';
        }
        
        return `
            <div class="bet-controls">
                <div class="bet-type-selection">
                    <label for="bet-type-select">Bet Type:</label>
                    <select id="bet-type-select">
                        ${betTypeOptions}
                    </select>
                </div>
                
                <div class="system-options" id="system-options" style="display: none;">
                    <label for="system-type">System Type:</label>
                    <select id="system-type">
                        ${this.generateSystemOptions()}
                    </select>
                </div>
                
                <div class="stake-input-group">
                    <label for="stake-input">Stake (KES):</label>
                    <input type="number" id="stake-input" min="1" step="0.01" value="10.00">
                </div>
                
                <div class="odds-summary">
                    <div class="total-odds">
                        Total Odds: <span id="total-odds">1.00</span>
                    </div>
                    <div class="potential-winnings">
                        Potential Winnings: KES <span id="potential-winnings">0.00</span>
                    </div>
                </div>
                
                <button id="place-bet-button" class="place-bet-btn" ${selectionsCount === 0 ? 'disabled' : ''}>
                    Place Bet
                </button>
            </div>
        `;
    }
    
    generateSystemOptions() {
        const count = this.selections.size;
        let options = '';
        
        for (let i = 2; i < count; i++) {
            options += `<option value="${i}/${count}">${i}/${count}</option>`;
        }
        
        return options;
    }
    
    attachBetSlipListeners() {
        // Re-attach listeners for dynamically created elements
        const stakeInput = document.getElementById('stake-input');
        const betTypeSelect = document.getElementById('bet-type-select');
        const placeBetButton = document.getElementById('place-bet-button');
        const systemTypeSelect = document.getElementById('system-type');
        
        if (stakeInput) {
            stakeInput.addEventListener('input', () => this.updatePotentialWinnings());
        }
        
        if (betTypeSelect) {
            betTypeSelect.addEventListener('change', () => {
                this.handleBetTypeChange();
                this.updateOddsCalculation();
            });
        }
        
        if (systemTypeSelect) {
            systemTypeSelect.addEventListener('change', () => this.updateOddsCalculation());
        }
        
        if (placeBetButton) {
            placeBetButton.addEventListener('click', () => this.placeBet());
        }
    }
    
    handleBetTypeChange() {
        const betType = document.getElementById('bet-type-select').value;
        const systemOptions = document.getElementById('system-options');
        
        if (systemOptions) {
            systemOptions.style.display = betType === 'system' ? 'block' : 'none';
        }
    }
    
    updateOddsCalculation() {
        const betType = this.getBetType();
        let totalOdds = 1.00;
        
        if (this.selections.size === 0) {
            totalOdds = 1.00;
        } else if (betType === 'single') {
            // For single bet, use first selection odds
            const firstSelection = this.selections.values().next().value;
            totalOdds = firstSelection ? firstSelection.oddsValue : 1.00;
        } else if (betType === 'multi' || betType === 'accumulator') {
            // Multiply all odds
            totalOdds = this.calculateAccumulatorOdds();
        } else if (betType === 'system') {
            // System bet calculation
            totalOdds = this.calculateSystemOdds();
        }
        
        this.updateOddsDisplay(totalOdds);
        this.updatePotentialWinnings();
    }
    
    calculateAccumulatorOdds() {
        let totalOdds = 1.00;
        for (const [id, selection] of this.selections) {
            totalOdds *= selection.oddsValue;
        }
        return totalOdds;
    }
    
    calculateSystemOdds() {
        const systemType = this.getSystemType();
        if (!systemType) return 1.00;
        
        const [minWins, totalSelections] = systemType.split('/').map(Number);
        const baseOdds = this.calculateAccumulatorOdds();
        
        // Simplified system calculation
        const reductionFactor = minWins / totalSelections;
        return baseOdds * reductionFactor;
    }
    
    updateOddsDisplay(totalOdds) {
        if (this.totalOddsElement) {
            this.totalOddsElement.textContent = totalOdds.toFixed(2);
        }
        
        const totalOddsInSlip = document.getElementById('total-odds');
        if (totalOddsInSlip) {
            totalOddsInSlip.textContent = totalOdds.toFixed(2);
        }
    }
    
    updatePotentialWinnings() {
        const stake = this.getStake();
        const totalOdds = this.getTotalOdds();
        const potentialWinnings = stake * totalOdds;
        
        if (this.potentialWinningsElement) {
            this.potentialWinningsElement.textContent = potentialWinnings.toFixed(2);
        }
        
        const potentialWinningsInSlip = document.getElementById('potential-winnings');
        if (potentialWinningsInSlip) {
            potentialWinningsInSlip.textContent = potentialWinnings.toFixed(2);
        }
    }
    
    updateOddsButtonStates() {
        // Update visual state of odds buttons
        document.querySelectorAll('.odds-button').forEach(button => {
            const marketId = button.dataset.marketId;
            const isSelected = this.findSelectionByMarket(marketId) !== null;
            
            button.classList.toggle('selected', isSelected);
        });
    }
    
    getBetType() {
        const betTypeSelect = document.getElementById('bet-type-select');
        return betTypeSelect ? betTypeSelect.value : 'single';
    }
    
    getSystemType() {
        const systemTypeSelect = document.getElementById('system-type');
        return systemTypeSelect ? systemTypeSelect.value : null;
    }
    
    getStake() {
        const stakeInput = document.getElementById('stake-input');
        return stakeInput ? parseFloat(stakeInput.value) || 0 : 0;
    }
    
    getTotalOdds() {
        const totalOddsElement = document.getElementById('total-odds');
        return totalOddsElement ? parseFloat(totalOddsElement.textContent) || 1.00 : 1.00;
    }
    
    async placeBet() {
        if (this.selections.size === 0) {
            this.showNotification('Please select at least one outcome', 'error');
            return;
        }
        
        const betType = this.getBetType();
        const stake = this.getStake();
        
        if (stake < 1) {
            this.showNotification('Minimum stake is KES 1.00', 'error');
            return;
        }
        
        // Validate bet type requirements
        if ((betType === 'multi' || betType === 'accumulator') && this.selections.size < 2) {
            this.showNotification('Multi-bet requires at least 2 selections', 'error');
            return;
        }
        
        if (betType === 'system' && this.selections.size < 3) {
            this.showNotification('System bet requires at least 3 selections', 'error');
            return;
        }
        
        // Prepare bet data
        const betData = {
            bet_type: betType,
            stake: stake,
            selections: Array.from(this.selections.values()).map(selection => ({
                market_id: selection.marketId,
                odds_id: selection.oddsId,
                odds_value: selection.oddsValue
            }))
        };
        
        if (betType === 'system') {
            betData.system_type = this.getSystemType();
        }
        
        // Disable button during processing
        const placeBetButton = document.getElementById('place-bet-button');
        if (placeBetButton) {
            placeBetButton.disabled = true;
            placeBetButton.textContent = 'Placing Bet...';
        }
        
        try {
            const response = await fetch('/betting/place/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify(betData)
            });
            
            const result = await response.json();
            
            if (response.ok && result.success) {
                this.showNotification('Bet placed successfully!', 'success');
                this.clearBetSlip();
                
                // Redirect to bet details or refresh page
                if (result.bet_id) {
                    setTimeout(() => {
                        window.location.href = `/betting/bet/${result.bet_id}/`;
                    }, 2000);
                }
            } else {
                this.showNotification(result.error || 'Failed to place bet', 'error');
            }
            
        } catch (error) {
            console.error('Error placing bet:', error);
            this.showNotification('Network error. Please try again.', 'error');
        } finally {
            // Re-enable button
            if (placeBetButton) {
                placeBetButton.disabled = false;
                placeBetButton.textContent = 'Place Bet';
            }
        }
    }
    
    clearBetSlip() {
        this.selections.clear();
        this.updateBetSlip();
        this.updateOddsCalculation();
        this.updateOddsButtonStates();
    }
    
    getCSRFToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }
    
    showNotification(message, type = 'info') {
        // Create or update notification element
        let notification = document.getElementById('multi-bet-notification');
        if (!notification) {
            notification = document.createElement('div');
            notification.id = 'multi-bet-notification';
            notification.className = 'multi-bet-notification';
            document.body.appendChild(notification);
        }
        
        notification.className = `multi-bet-notification ${type}`;
        notification.textContent = message;
        notification.style.display = 'block';
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            notification.style.display = 'none';
        }, 5000);
    }
}

// Initialize multi-bet manager when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.multiBetManager = new MultiBetManager();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MultiBetManager;
}
