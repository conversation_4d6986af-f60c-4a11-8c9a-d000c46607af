/**
 * Responsive Design for Betika Clone
 * Mobile-first approach with progressive enhancement
 */

/* Base mobile styles (320px and up) */
@media screen and (max-width: 767px) {
    /* Header optimizations */
    .header-nav {
        padding: 0.5rem;
        position: sticky;
        top: 0;
        z-index: 1000;
        background: var(--primary-dark);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .header-container {
        flex-direction: column;
        gap: 0.5rem;
    }

    .logo {
        text-align: center;
    }

    .logo-text {
        font-size: 1.5rem;
    }

    /* Mobile navigation */
    .main-nav {
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .nav-tabs {
        display: flex;
        white-space: nowrap;
        padding: 0 0.5rem;
        gap: 0.5rem;
        min-width: max-content;
    }

    .nav-tab {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        border-radius: 20px;
        background: rgba(255,255,255,0.1);
        color: white;
        text-decoration: none;
        transition: all 0.3s ease;
        flex-shrink: 0;
    }

    .nav-tab:hover,
    .nav-tab.active {
        background: var(--accent-green);
        color: white;
    }

    /* User controls mobile */
    .user-controls {
        flex-direction: column;
        gap: 0.5rem;
        width: 100%;
    }

    .balance-display,
    .auth-buttons {
        width: 100%;
        justify-content: center;
    }

    /* Main layout mobile */
    .main-container {
        flex-direction: column;
        padding: 0;
        gap: 0;
    }

    /* Sidebar mobile */
    .sidebar {
        position: fixed;
        top: 0;
        left: -280px;
        width: 280px;
        height: 100vh;
        background: var(--primary-dark);
        transition: left 0.3s ease;
        z-index: 1001;
        overflow-y: auto;
    }

    .sidebar.open {
        left: 0;
    }

    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .sidebar-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    /* Mobile menu toggle */
    .mobile-menu-toggle {
        display: block;
        position: fixed;
        top: 1rem;
        left: 1rem;
        z-index: 1002;
        background: var(--accent-green);
        color: white;
        border: none;
        padding: 0.75rem;
        border-radius: 50%;
        font-size: 1.2rem;
        cursor: pointer;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }

    /* Content area mobile */
    .content-area {
        width: 100%;
        padding: 1rem;
        margin-top: 0;
    }

    /* Event cards mobile */
    .event-card {
        margin-bottom: 1rem;
        border-radius: 8px;
        overflow: hidden;
    }

    .event-header {
        padding: 1rem;
        font-size: 0.9rem;
    }

    .event-teams {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .team-name {
        font-size: 1rem;
        font-weight: 600;
    }

    .vs-separator {
        font-size: 0.8rem;
        color: var(--text-muted);
    }

    /* Odds mobile */
    .odds-container {
        padding: 1rem;
        gap: 0.5rem;
    }

    .market-odds {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
        gap: 0.5rem;
    }

    .odds-button {
        padding: 0.75rem 0.5rem;
        font-size: 0.875rem;
        min-height: 60px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 0.25rem;
    }

    .selection {
        font-size: 0.75rem;
        text-align: center;
        line-height: 1.2;
    }

    .odds-value {
        font-size: 1rem;
        font-weight: 700;
    }

    /* Betslip mobile */
    .betslip {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        border-top: 2px solid var(--accent-green);
        transform: translateY(100%);
        transition: transform 0.3s ease;
        z-index: 1000;
        max-height: 70vh;
        overflow-y: auto;
    }

    .betslip.open {
        transform: translateY(0);
    }

    .betslip-header {
        padding: 1rem;
        background: var(--primary-dark);
        color: white;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: sticky;
        top: 0;
    }

    .mobile-betslip-toggle {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        background: var(--accent-green);
        color: white;
        border: none;
        padding: 1rem;
        border-radius: 50%;
        font-size: 1.5rem;
        cursor: pointer;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 999;
        transition: all 0.3s ease;
    }

    .mobile-betslip-toggle:hover {
        transform: scale(1.1);
    }

    .mobile-betslip-toggle .badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background: red;
        color: white;
        border-radius: 50%;
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        min-width: 20px;
        text-align: center;
    }

    /* Forms mobile */
    .form-group {
        margin-bottom: 1rem;
    }

    .form-control {
        width: 100%;
        padding: 0.75rem;
        font-size: 1rem;
        border-radius: 8px;
        border: 1px solid #ddd;
    }

    .btn {
        width: 100%;
        padding: 0.75rem;
        font-size: 1rem;
        border-radius: 8px;
        margin-bottom: 0.5rem;
    }

    /* Tables mobile */
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .table {
        min-width: 600px;
        font-size: 0.875rem;
    }

    /* Cards mobile */
    .card {
        margin-bottom: 1rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .card-header {
        padding: 1rem;
        font-size: 1.1rem;
        font-weight: 600;
    }

    .card-body {
        padding: 1rem;
    }

    /* Loading states mobile */
    .loading-spinner {
        text-align: center;
        padding: 2rem;
        font-size: 1.5rem;
        color: var(--accent-green);
    }

    /* Error states mobile */
    .error-message {
        text-align: center;
        padding: 1rem;
        color: #dc3545;
        background: #f8d7da;
        border-radius: 8px;
        margin: 1rem 0;
    }
}

/* Tablet styles (768px to 1023px) */
@media screen and (min-width: 768px) and (max-width: 1023px) {
    .main-container {
        padding: 1rem;
    }

    .sidebar {
        width: 250px;
    }

    .content-area {
        padding: 1.5rem;
    }

    .event-card {
        margin-bottom: 1.5rem;
    }

    .market-odds {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }

    .betslip {
        position: fixed;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        width: 320px;
        max-height: 80vh;
        border-radius: 12px;
        box-shadow: 0 8px 24px rgba(0,0,0,0.2);
    }
}

/* Desktop styles (1024px and up) */
@media screen and (min-width: 1024px) {
    .mobile-menu-toggle,
    .mobile-betslip-toggle {
        display: none;
    }

    .sidebar {
        position: static;
        width: 280px;
        height: auto;
    }

    .betslip {
        position: sticky;
        top: 2rem;
        width: 320px;
        max-height: calc(100vh - 4rem);
        transform: none;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
}

/* High DPI displays */
@media screen and (-webkit-min-device-pixel-ratio: 2),
       screen and (min-resolution: 192dpi) {
    .logo img,
    .team-logo {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #1a1a1a;
        --bg-secondary: #2d2d2d;
        --text-primary: #ffffff;
        --text-secondary: #cccccc;
        --border-color: #404040;
    }
}

/* Print styles */
@media print {
    .sidebar,
    .betslip,
    .mobile-menu-toggle,
    .mobile-betslip-toggle,
    .nav-tabs {
        display: none !important;
    }

    .content-area {
        width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .event-card {
        break-inside: avoid;
        margin-bottom: 1rem;
        border: 1px solid #ddd;
    }
}
