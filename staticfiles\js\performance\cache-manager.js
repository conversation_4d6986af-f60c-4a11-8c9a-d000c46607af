/**
 * Client-side Cache Manager for Betika Clone
 * Manages browser caching, localStorage, and sessionStorage for better performance
 */

class CacheManager {
    constructor() {
        this.localStorage = window.localStorage;
        this.sessionStorage = window.sessionStorage;
        this.memoryCache = new Map();
        this.cachePrefix = 'betika_';
        this.defaultTTL = 5 * 60 * 1000; // 5 minutes
        
        this.init();
    }

    init() {
        // Clean up expired cache entries on initialization
        this.cleanupExpiredEntries();
        
        // Set up periodic cleanup
        setInterval(() => {
            this.cleanupExpiredEntries();
        }, 60000); // Clean up every minute
    }

    // Generate cache key with prefix
    getCacheKey(key) {
        return `${this.cachePrefix}${key}`;
    }

    // Set item in cache with TTL
    set(key, value, ttl = this.defaultTTL, storage = 'memory') {
        const cacheKey = this.getCacheKey(key);
        const expiresAt = Date.now() + ttl;
        
        const cacheItem = {
            value: value,
            expiresAt: expiresAt,
            timestamp: Date.now()
        };

        try {
            switch (storage) {
                case 'localStorage':
                    this.localStorage.setItem(cacheKey, JSON.stringify(cacheItem));
                    break;
                case 'sessionStorage':
                    this.sessionStorage.setItem(cacheKey, JSON.stringify(cacheItem));
                    break;
                case 'memory':
                default:
                    this.memoryCache.set(cacheKey, cacheItem);
                    break;
            }
            return true;
        } catch (error) {
            console.warn('Cache set failed:', error);
            return false;
        }
    }

    // Get item from cache
    get(key, storage = 'memory') {
        const cacheKey = this.getCacheKey(key);
        let cacheItem = null;

        try {
            switch (storage) {
                case 'localStorage':
                    const localItem = this.localStorage.getItem(cacheKey);
                    cacheItem = localItem ? JSON.parse(localItem) : null;
                    break;
                case 'sessionStorage':
                    const sessionItem = this.sessionStorage.getItem(cacheKey);
                    cacheItem = sessionItem ? JSON.parse(sessionItem) : null;
                    break;
                case 'memory':
                default:
                    cacheItem = this.memoryCache.get(cacheKey);
                    break;
            }

            if (cacheItem) {
                // Check if item has expired
                if (Date.now() > cacheItem.expiresAt) {
                    this.delete(key, storage);
                    return null;
                }
                return cacheItem.value;
            }
        } catch (error) {
            console.warn('Cache get failed:', error);
        }

        return null;
    }

    // Delete item from cache
    delete(key, storage = 'memory') {
        const cacheKey = this.getCacheKey(key);

        try {
            switch (storage) {
                case 'localStorage':
                    this.localStorage.removeItem(cacheKey);
                    break;
                case 'sessionStorage':
                    this.sessionStorage.removeItem(cacheKey);
                    break;
                case 'memory':
                default:
                    this.memoryCache.delete(cacheKey);
                    break;
            }
            return true;
        } catch (error) {
            console.warn('Cache delete failed:', error);
            return false;
        }
    }

    // Check if item exists in cache and is not expired
    has(key, storage = 'memory') {
        return this.get(key, storage) !== null;
    }

    // Clear all cache entries
    clear(storage = 'memory') {
        try {
            switch (storage) {
                case 'localStorage':
                    Object.keys(this.localStorage).forEach(key => {
                        if (key.startsWith(this.cachePrefix)) {
                            this.localStorage.removeItem(key);
                        }
                    });
                    break;
                case 'sessionStorage':
                    Object.keys(this.sessionStorage).forEach(key => {
                        if (key.startsWith(this.cachePrefix)) {
                            this.sessionStorage.removeItem(key);
                        }
                    });
                    break;
                case 'memory':
                default:
                    this.memoryCache.clear();
                    break;
                case 'all':
                    this.clear('memory');
                    this.clear('localStorage');
                    this.clear('sessionStorage');
                    break;
            }
            return true;
        } catch (error) {
            console.warn('Cache clear failed:', error);
            return false;
        }
    }

    // Clean up expired entries
    cleanupExpiredEntries() {
        const now = Date.now();

        // Clean memory cache
        for (const [key, item] of this.memoryCache.entries()) {
            if (now > item.expiresAt) {
                this.memoryCache.delete(key);
            }
        }

        // Clean localStorage
        try {
            Object.keys(this.localStorage).forEach(key => {
                if (key.startsWith(this.cachePrefix)) {
                    const item = JSON.parse(this.localStorage.getItem(key));
                    if (item && now > item.expiresAt) {
                        this.localStorage.removeItem(key);
                    }
                }
            });
        } catch (error) {
            console.warn('localStorage cleanup failed:', error);
        }

        // Clean sessionStorage
        try {
            Object.keys(this.sessionStorage).forEach(key => {
                if (key.startsWith(this.cachePrefix)) {
                    const item = JSON.parse(this.sessionStorage.getItem(key));
                    if (item && now > item.expiresAt) {
                        this.sessionStorage.removeItem(key);
                    }
                }
            });
        } catch (error) {
            console.warn('sessionStorage cleanup failed:', error);
        }
    }

    // Cache API responses
    async cacheApiResponse(url, options = {}, ttl = this.defaultTTL) {
        const cacheKey = `api_${url}_${JSON.stringify(options)}`;
        
        // Check if we have cached response
        const cachedResponse = this.get(cacheKey, 'memory');
        if (cachedResponse) {
            return cachedResponse;
        }

        try {
            const response = await fetch(url, options);
            const data = await response.json();
            
            // Cache successful responses
            if (response.ok) {
                this.set(cacheKey, data, ttl, 'memory');
            }
            
            return data;
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }

    // Cache sports data with specific TTL
    cacheSportsData(key, data, ttl = 10 * 60 * 1000) { // 10 minutes
        return this.set(`sports_${key}`, data, ttl, 'memory');
    }

    getSportsData(key) {
        return this.get(`sports_${key}`, 'memory');
    }

    // Cache odds data with short TTL
    cacheOddsData(eventId, data, ttl = 30 * 1000) { // 30 seconds
        return this.set(`odds_${eventId}`, data, ttl, 'memory');
    }

    getOddsData(eventId) {
        return this.get(`odds_${eventId}`, 'memory');
    }

    // Cache user data in sessionStorage
    cacheUserData(key, data, ttl = 30 * 60 * 1000) { // 30 minutes
        return this.set(`user_${key}`, data, ttl, 'sessionStorage');
    }

    getUserData(key) {
        return this.get(`user_${key}`, 'sessionStorage');
    }

    // Get cache statistics
    getStats() {
        const memorySize = this.memoryCache.size;
        let localStorageSize = 0;
        let sessionStorageSize = 0;

        try {
            Object.keys(this.localStorage).forEach(key => {
                if (key.startsWith(this.cachePrefix)) {
                    localStorageSize++;
                }
            });

            Object.keys(this.sessionStorage).forEach(key => {
                if (key.startsWith(this.cachePrefix)) {
                    sessionStorageSize++;
                }
            });
        } catch (error) {
            console.warn('Error getting cache stats:', error);
        }

        return {
            memory: memorySize,
            localStorage: localStorageSize,
            sessionStorage: sessionStorageSize,
            total: memorySize + localStorageSize + sessionStorageSize
        };
    }

    // Preload critical data
    async preloadCriticalData() {
        try {
            // Preload sports list
            const sportsData = await this.cacheApiResponse('/api/v1/sports/');
            console.log('Preloaded sports data:', sportsData.count, 'sports');

            // Preload live events
            const liveEvents = await this.cacheApiResponse('/api/v1/sports/events/?status=live');
            console.log('Preloaded live events:', liveEvents.count, 'events');

        } catch (error) {
            console.warn('Failed to preload critical data:', error);
        }
    }
}

// Initialize cache manager
document.addEventListener('DOMContentLoaded', () => {
    window.cacheManager = new CacheManager();
    
    // Preload critical data
    window.cacheManager.preloadCriticalData();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CacheManager;
}
