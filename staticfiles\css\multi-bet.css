/* Multi-Bet Styles - Maintaining Project Template Consistency */

/* Bet Slip Container */
.bet-slip-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    position: sticky;
    top: 20px;
    max-height: calc(100vh - 40px);
    overflow-y: auto;
}

.bet-slip-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
}

.bet-slip-header h3 {
    margin: 0;
    color: #1f2937;
    font-size: 1.125rem;
    font-weight: 600;
}

.clear-all-btn {
    background: none;
    border: none;
    color: #6b7280;
    font-size: 0.875rem;
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.clear-all-btn:hover {
    color: #dc2626;
    background-color: #fef2f2;
}

/* Empty Bet Slip */
.empty-bet-slip {
    text-align: center;
    padding: 2rem 1rem;
    color: #6b7280;
}

.empty-bet-slip p {
    margin: 0;
    font-size: 0.875rem;
}

/* Selections List */
.selections-list {
    margin-bottom: 1.5rem;
}

.selection-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.selection-item:hover {
    background-color: #f1f5f9;
    border-color: #cbd5e1;
}

.selection-details {
    flex: 1;
    min-width: 0;
}

.event-name {
    font-weight: 600;
    color: #1f2937;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.market-name {
    font-size: 0.75rem;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.125rem;
}

.odds-name {
    font-size: 0.875rem;
    color: #374151;
    font-weight: 500;
}

.selection-odds {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-shrink: 0;
}

.odds-value {
    background-color: #3b82f6;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.875rem;
    min-width: 3rem;
    text-align: center;
}

.remove-selection {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    font-size: 1.125rem;
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.remove-selection:hover {
    color: #dc2626;
    background-color: #fef2f2;
}

/* Bet Controls */
.bet-controls {
    border-top: 1px solid #e5e7eb;
    padding-top: 1rem;
}

.bet-type-selection {
    margin-bottom: 1rem;
}

.bet-type-selection label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.bet-type-selection select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 0.875rem;
    background-color: white;
    color: #1f2937;
}

.bet-type-selection select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* System Options */
.system-options {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background-color: #f8fafc;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.system-options label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.system-options select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 0.875rem;
    background-color: white;
}

/* Stake Input */
.stake-input-group {
    margin-bottom: 1rem;
}

.stake-input-group label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.stake-input-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 1rem;
    background-color: white;
    color: #1f2937;
}

.stake-input-group input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Odds Summary */
.odds-summary {
    background-color: #f8fafc;
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1rem;
    border: 1px solid #e2e8f0;
}

.total-odds, .potential-winnings {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.total-odds:last-child, .potential-winnings:last-child {
    margin-bottom: 0;
}

.total-odds {
    color: #374151;
    font-weight: 500;
}

.potential-winnings {
    color: #059669;
    font-weight: 600;
    font-size: 1rem;
    border-top: 1px solid #e2e8f0;
    padding-top: 0.5rem;
    margin-top: 0.5rem;
}

/* Place Bet Button */
.place-bet-btn {
    width: 100%;
    background-color: #059669;
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.place-bet-btn:hover:not(:disabled) {
    background-color: #047857;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
}

.place-bet-btn:disabled {
    background-color: #9ca3af;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Odds Buttons */
.odds-button {
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    color: #1f2937;
    padding: 0.5rem 0.75rem;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    min-width: 3.5rem;
    text-align: center;
}

.odds-button:hover {
    background-color: #e2e8f0;
    border-color: #cbd5e1;
    transform: translateY(-1px);
}

.odds-button.selected {
    background-color: #3b82f6;
    color: white;
    border-color: #2563eb;
}

.odds-button:disabled {
    background-color: #f3f4f6;
    color: #9ca3af;
    cursor: not-allowed;
    transform: none;
}

/* Multi-Bet Notifications */
.multi-bet-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    display: none;
    max-width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: slideIn 0.3s ease-out;
}

.multi-bet-notification.success {
    background-color: #059669;
}

.multi-bet-notification.error {
    background-color: #dc2626;
}

.multi-bet-notification.info {
    background-color: #3b82f6;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .bet-slip-container {
        position: static;
        margin-bottom: 1rem;
        max-height: none;
    }
    
    .selection-item {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }
    
    .selection-odds {
        justify-content: space-between;
    }
    
    .multi-bet-notification {
        left: 1rem;
        right: 1rem;
        max-width: none;
    }
}

/* Loading States */
.loading-odds {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading-odds::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 1rem;
    height: 1rem;
    margin: -0.5rem 0 0 -0.5rem;
    border: 2px solid #e5e7eb;
    border-top-color: #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Bet Type Indicators */
.bet-type-indicator {
    display: inline-block;
    padding: 0.125rem 0.5rem;
    background-color: #eff6ff;
    color: #2563eb;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bet-type-indicator.multi {
    background-color: #f0fdf4;
    color: #16a34a;
}

.bet-type-indicator.system {
    background-color: #fef3c7;
    color: #d97706;
}
