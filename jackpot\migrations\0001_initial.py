# Generated by Django 4.2.7 on 2025-07-22 11:14

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("sports", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Jackpot",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                ("description", models.TextField(blank=True)),
                (
                    "jackpot_type",
                    models.CharField(
                        choices=[
                            ("weekly", "Weekly Jackpot"),
                            ("daily", "Daily Jackpot"),
                            ("special", "Special Jackpot"),
                            ("mega", "Mega Jackpot"),
                        ],
                        default="weekly",
                        max_length=20,
                    ),
                ),
                (
                    "base_prize_pool",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("10000.00"),
                        max_digits=12,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01"))
                        ],
                    ),
                ),
                (
                    "current_prize_pool",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        max_digits=12,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00"))
                        ],
                    ),
                ),
                (
                    "entry_fee",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("50.00"),
                        max_digits=8,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("1.00"))
                        ],
                    ),
                ),
                (
                    "first_prize_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("70.00"),
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00")),
                            django.core.validators.MaxValueValidator(Decimal("100.00")),
                        ],
                    ),
                ),
                (
                    "second_prize_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("20.00"),
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00")),
                            django.core.validators.MaxValueValidator(Decimal("100.00")),
                        ],
                    ),
                ),
                (
                    "third_prize_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("10.00"),
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00")),
                            django.core.validators.MaxValueValidator(Decimal("100.00")),
                        ],
                    ),
                ),
                ("total_games", models.PositiveIntegerField(default=13)),
                (
                    "minimum_correct_predictions",
                    models.PositiveIntegerField(default=10),
                ),
                ("start_time", models.DateTimeField()),
                ("end_time", models.DateTimeField()),
                ("settlement_time", models.DateTimeField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("upcoming", "Upcoming"),
                            ("active", "Active"),
                            ("closed", "Closed"),
                            ("settled", "Settled"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="upcoming",
                        max_length=20,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("total_entries", models.PositiveIntegerField(default=0)),
                ("total_participants", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="JackpotEntry",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "stake_amount",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=8,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01"))
                        ],
                    ),
                ),
                ("correct_predictions", models.PositiveIntegerField(default=0)),
                ("total_predictions", models.PositiveIntegerField(default=0)),
                (
                    "prize_won",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00"))
                        ],
                    ),
                ),
                ("prize_tier", models.CharField(blank=True, max_length=20)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("settled", "Settled"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                ("is_winner", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("settled_at", models.DateTimeField(blank=True, null=True)),
                (
                    "jackpot",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="entries",
                        to="jackpot.jackpot",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="jackpot_entries",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="JackpotGame",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("game_number", models.PositiveIntegerField()),
                ("is_settled", models.BooleanField(default=False)),
                ("settled_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "event",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="sports.event"
                    ),
                ),
                (
                    "jackpot",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="games",
                        to="jackpot.jackpot",
                    ),
                ),
                (
                    "market",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="sports.market"
                    ),
                ),
                (
                    "winning_odds",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="jackpot_wins",
                        to="sports.odds",
                    ),
                ),
            ],
            options={
                "ordering": ["game_number"],
            },
        ),
        migrations.CreateModel(
            name="JackpotWinner",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "prize_tier",
                    models.CharField(
                        choices=[
                            ("first", "First Prize"),
                            ("second", "Second Prize"),
                            ("third", "Third Prize"),
                            ("consolation", "Consolation Prize"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "prize_amount",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=12,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01"))
                        ],
                    ),
                ),
                ("is_paid", models.BooleanField(default=False)),
                ("paid_at", models.DateTimeField(blank=True, null=True)),
                ("payment_reference", models.CharField(blank=True, max_length=100)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "entry",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="jackpot.jackpotentry",
                    ),
                ),
                (
                    "jackpot",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="winners",
                        to="jackpot.jackpot",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["prize_tier", "-prize_amount"],
            },
        ),
        migrations.CreateModel(
            name="JackpotSelection",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "selected_outcome",
                    models.CharField(
                        choices=[("1", "Home Win"), ("X", "Draw"), ("2", "Away Win")],
                        max_length=1,
                    ),
                ),
                ("is_correct", models.BooleanField(blank=True, null=True)),
                (
                    "actual_outcome",
                    models.CharField(
                        blank=True,
                        choices=[("1", "Home Win"), ("X", "Draw"), ("2", "Away Win")],
                        max_length=1,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("settled_at", models.DateTimeField(blank=True, null=True)),
                (
                    "entry",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="selections",
                        to="jackpot.jackpotentry",
                    ),
                ),
                (
                    "game",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="selections",
                        to="jackpot.jackpotgame",
                    ),
                ),
            ],
            options={
                "ordering": ["game__game_number"],
            },
        ),
        migrations.CreateModel(
            name="JackpotPrediction",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("is_correct", models.BooleanField(default=False)),
                ("is_settled", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("settled_at", models.DateTimeField(blank=True, null=True)),
                (
                    "entry",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="predictions",
                        to="jackpot.jackpotentry",
                    ),
                ),
                (
                    "game",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="jackpot.jackpotgame",
                    ),
                ),
                (
                    "predicted_odds",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="sports.odds"
                    ),
                ),
            ],
            options={
                "ordering": ["game__game_number"],
            },
        ),
        migrations.AddIndex(
            model_name="jackpot",
            index=models.Index(
                fields=["status", "is_active"], name="jackpot_jac_status_82e574_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="jackpot",
            index=models.Index(
                fields=["start_time", "end_time"], name="jackpot_jac_start_t_9ba326_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="jackpot",
            index=models.Index(
                fields=["jackpot_type"], name="jackpot_jac_jackpot_f80ff4_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="jackpotwinner",
            index=models.Index(
                fields=["jackpot", "prize_tier"], name="jackpot_jac_jackpot_406a4f_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="jackpotwinner",
            index=models.Index(
                fields=["user", "is_paid"], name="jackpot_jac_user_id_dc8532_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="jackpotwinner",
            unique_together={("jackpot", "entry")},
        ),
        migrations.AddIndex(
            model_name="jackpotselection",
            index=models.Index(
                fields=["entry", "game"], name="jackpot_jac_entry_i_674f6a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="jackpotselection",
            index=models.Index(
                fields=["is_correct"], name="jackpot_jac_is_corr_80f757_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="jackpotselection",
            unique_together={("entry", "game")},
        ),
        migrations.AddIndex(
            model_name="jackpotprediction",
            index=models.Index(
                fields=["entry", "game"], name="jackpot_jac_entry_i_f85025_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="jackpotprediction",
            index=models.Index(
                fields=["is_correct", "is_settled"],
                name="jackpot_jac_is_corr_58e92b_idx",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="jackpotprediction",
            unique_together={("entry", "game")},
        ),
        migrations.AddIndex(
            model_name="jackpotgame",
            index=models.Index(
                fields=["jackpot", "game_number"], name="jackpot_jac_jackpot_8f9e44_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="jackpotgame",
            index=models.Index(
                fields=["event", "is_settled"], name="jackpot_jac_event_i_f1cc33_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="jackpotgame",
            unique_together={("jackpot", "game_number")},
        ),
        migrations.AddIndex(
            model_name="jackpotentry",
            index=models.Index(
                fields=["jackpot", "user"], name="jackpot_jac_jackpot_bd65ab_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="jackpotentry",
            index=models.Index(
                fields=["status", "is_winner"], name="jackpot_jac_status_c116cb_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="jackpotentry",
            index=models.Index(
                fields=["correct_predictions"], name="jackpot_jac_correct_e6bd90_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="jackpotentry",
            unique_together={("jackpot", "user")},
        ),
    ]
