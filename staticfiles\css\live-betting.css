/* Live Betting Styles */

/* Connection Status */
.connection-status {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.connection-status.connected {
    background-color: #10b981;
    color: white;
}

.connection-status.connected::before {
    content: "●";
    margin-right: 4px;
    animation: pulse 2s infinite;
}

.connection-status.disconnected {
    background-color: #ef4444;
    color: white;
}

.connection-status.disconnected::before {
    content: "●";
    margin-right: 4px;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Live Betting Notifications */
.live-betting-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 16px;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    display: none;
    max-width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.live-betting-notification.success {
    background-color: #10b981;
}

.live-betting-notification.error {
    background-color: #ef4444;
}

.live-betting-notification.info {
    background-color: #3b82f6;
}

/* Odds Display */
.odds-container {
    position: relative;
    display: inline-block;
}

.odds-value {
    padding: 8px 12px;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.odds-value:hover {
    background-color: #e2e8f0;
    border-color: #cbd5e1;
}

.odds-value.selected {
    background-color: #3b82f6;
    color: white;
    border-color: #2563eb;
}

/* Odds Change Animations */
.odds-value.odds-changing {
    animation: oddsFlash 0.3s ease-in-out;
}

.odds-value.odds-increased {
    background-color: #dcfce7;
    border-color: #16a34a;
    color: #15803d;
}

.odds-value.odds-decreased {
    background-color: #fef2f2;
    border-color: #dc2626;
    color: #dc2626;
}

@keyframes oddsFlash {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Odds Change Indicator */
.odds-change-indicator {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.3s ease;
    pointer-events: none;
}

.odds-change-indicator.increase {
    background-color: #16a34a;
}

.odds-change-indicator.decrease {
    background-color: #dc2626;
}

.change-arrow.up {
    color: #22c55e;
}

.change-arrow.down {
    color: #ef4444;
}

/* Event Status Display */
.event-status {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px 16px;
    background-color: #f8fafc;
    border-radius: 8px;
    margin-bottom: 16px;
}

.event-timer {
    font-weight: 700;
    color: #dc2626;
    font-size: 18px;
}

.event-score {
    font-weight: 700;
    font-size: 20px;
    color: #1f2937;
    transition: all 0.3s ease;
}

.event-score.score-updated {
    color: #16a34a;
    transform: scale(1.1);
}

.event-status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.event-status-badge.live {
    background-color: #dc2626;
    color: white;
}

.event-status-badge.finished {
    background-color: #6b7280;
    color: white;
}

/* Live Betting Interface */
.live-betting-container {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 24px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.events-panel {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.bet-slip-panel {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 20px;
    height: fit-content;
    position: sticky;
    top: 20px;
}

/* Markets and Odds Layout */
.market-section {
    border-bottom: 1px solid #e5e7eb;
    padding: 16px;
}

.market-section:last-child {
    border-bottom: none;
}

.market-title {
    font-weight: 600;
    font-size: 16px;
    color: #1f2937;
    margin-bottom: 12px;
}

.odds-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 8px;
}

.odds-option {
    text-align: center;
    padding: 12px 8px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: white;
}

.odds-option:hover {
    border-color: #3b82f6;
    background-color: #f8fafc;
}

.odds-option.selected {
    border-color: #3b82f6;
    background-color: #eff6ff;
}

.odds-name {
    font-size: 14px;
    color: #6b7280;
    margin-bottom: 4px;
}

.odds-value {
    font-weight: 700;
    font-size: 16px;
    color: #1f2937;
}

/* Bet Slip */
.bet-slip-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e5e7eb;
}

.bet-slip-title {
    font-weight: 700;
    font-size: 18px;
    color: #1f2937;
}

.clear-bet-slip {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    font-size: 14px;
}

.clear-bet-slip:hover {
    color: #dc2626;
}

.bet-slip-entry {
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 12px;
    position: relative;
}

.bet-details {
    margin-bottom: 8px;
}

.market-name {
    font-size: 12px;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.odds-name {
    font-weight: 600;
    color: #1f2937;
    margin: 4px 0;
}

.stake-input {
    margin-bottom: 8px;
}

.stake-input input {
    width: 100%;
    padding: 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 14px;
}

.potential-winnings {
    font-size: 14px;
    color: #16a34a;
    font-weight: 600;
}

.remove-bet {
    position: absolute;
    top: 8px;
    right: 8px;
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    font-size: 18px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-bet:hover {
    color: #dc2626;
    background-color: #fef2f2;
    border-radius: 50%;
}

.bet-slip-summary {
    border-top: 1px solid #e5e7eb;
    padding-top: 16px;
    margin-top: 16px;
}

.total-stake, .total-potential {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-weight: 600;
}

.place-bet-button {
    width: 100%;
    background-color: #16a34a;
    color: white;
    border: none;
    padding: 12px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.place-bet-button:hover {
    background-color: #15803d;
}

.place-bet-button:disabled {
    background-color: #9ca3af;
    cursor: not-allowed;
}

/* Last Update Time */
.last-update {
    font-size: 12px;
    color: #6b7280;
    text-align: center;
    margin-top: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .live-betting-container {
        grid-template-columns: 1fr;
        gap: 16px;
        padding: 16px;
    }
    
    .bet-slip-panel {
        position: static;
        order: -1;
    }
    
    .odds-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .live-betting-notification {
        left: 16px;
        right: 16px;
        max-width: none;
    }
}

/* Loading States */
.loading-odds {
    opacity: 0.6;
    pointer-events: none;
}

.loading-odds::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid #e5e7eb;
    border-top-color: #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
