django_redis-6.0.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
django_redis-6.0.0.dist-info/METADATA,sha256=8BBa5M_rWd_WR6ZlQsysNaJHhYOk055f_Qu9iB-T_eU,32610
django_redis-6.0.0.dist-info/RECORD,,
django_redis-6.0.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_redis-6.0.0.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
django_redis-6.0.0.dist-info/licenses/AUTHORS.rst,sha256=Js5DnuUmpXUShaAUaDK7_45lhpN8cfZ8vxtBon1DVHg,845
django_redis-6.0.0.dist-info/licenses/LICENSE,sha256=0TXvxJe3VG9zgwWaAzfrWy9Lx7DWApArsKnhRqEM9GE,1444
django_redis-6.0.0.dist-info/top_level.txt,sha256=MsE2XCUWbWGc5FoAboqUBB6Y_KvKC5FMGyI2VlKqqAo,13
django_redis/__init__.py,sha256=VKtakiolvkovA9ZbpXW-df5HF-41okkDDz9EJyyPaek,547
django_redis/__pycache__/__init__.cpython-312.pyc,,
django_redis/__pycache__/cache.cpython-312.pyc,,
django_redis/__pycache__/exceptions.cpython-312.pyc,,
django_redis/__pycache__/hash_ring.cpython-312.pyc,,
django_redis/__pycache__/pool.cpython-312.pyc,,
django_redis/__pycache__/util.cpython-312.pyc,,
django_redis/cache.py,sha256=TOAB_BcjxjqkVOIlF5FQHZSLMLT6947PSvhcwmKiLS8,8122
django_redis/client/__init__.py,sha256=C4fPgcAn05-yzH3AYBJWrJtsKm1CgujCcmuKH7ekDWM,286
django_redis/client/__pycache__/__init__.cpython-312.pyc,,
django_redis/client/__pycache__/default.cpython-312.pyc,,
django_redis/client/__pycache__/herd.cpython-312.pyc,,
django_redis/client/__pycache__/sentinel.cpython-312.pyc,,
django_redis/client/__pycache__/sharded.cpython-312.pyc,,
django_redis/client/default.py,sha256=BnceGoGWOuLRSTGs9lzG8VaxDT4AALdj3tZ9_TQyWAg,36139
django_redis/client/herd.py,sha256=FGZPDvaGz1QStLCqAFTebctI9CLGbbCl6tqEsyHidVs,4800
django_redis/client/sentinel.py,sha256=5YRtVoS1cxv1qHPtwMT1mU69CwhUQNcuPWyoefiF_3E,1606
django_redis/client/sharded.py,sha256=pThYA1dE676xqqQCzpYhRZrwCwQHXkYeQlui9_YQhjk,15897
django_redis/compressors/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_redis/compressors/__pycache__/__init__.cpython-312.pyc,,
django_redis/compressors/__pycache__/base.cpython-312.pyc,,
django_redis/compressors/__pycache__/gzip.cpython-312.pyc,,
django_redis/compressors/__pycache__/identity.cpython-312.pyc,,
django_redis/compressors/__pycache__/lz4.cpython-312.pyc,,
django_redis/compressors/__pycache__/lzma.cpython-312.pyc,,
django_redis/compressors/__pycache__/zlib.cpython-312.pyc,,
django_redis/compressors/__pycache__/zstd.cpython-312.pyc,,
django_redis/compressors/base.py,sha256=Gc7XcGBj9xHIqf_BBXFk0tVcBsUMPKyO6PNn_L1gCk8,253
django_redis/compressors/gzip.py,sha256=gafZOT3Jbz7dZnwZGrmv9qbvPlSgKW3xb94XF0I6600,516
django_redis/compressors/identity.py,sha256=i3Njv0BdqQzHuOle0n4mnfKjnDI5v3QlWBiTzhCJRZM,240
django_redis/compressors/lz4.py,sha256=_2ormjjaDuvsI_OfANLWkVbsvjZSoZtTQBauojSdly0,580
django_redis/compressors/lzma.py,sha256=SmS7wWsgemdDzP5EcatNJauu0bgdzuySDuYAwh_Cfvc,550
django_redis/compressors/zlib.py,sha256=JcnEEue4jDPPw1hbOBCoju5RGycjOeCvBZXMmCQB9FM,538
django_redis/compressors/zstd.py,sha256=EoTp9j5w_iAEc-Vgx10Gu1SIOvE2xnoULj1savipgUQ,522
django_redis/exceptions.py,sha256=-zgA6cuxCXZGc3CU0ER-bWwDyLgybWYSjAZ2LQtvaro,343
django_redis/hash_ring.py,sha256=pVeYOr-KWXqH8Wf5xSGnf0PSWhTGfRX40V9uGb_NOBo,1825
django_redis/pool.py,sha256=oG7fjkSXknP0-1ZwPEdKPnzp7xXIiDzkYl4hM27KVcU,7107
django_redis/serializers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_redis/serializers/__pycache__/__init__.cpython-312.pyc,,
django_redis/serializers/__pycache__/base.cpython-312.pyc,,
django_redis/serializers/__pycache__/json.cpython-312.pyc,,
django_redis/serializers/__pycache__/msgpack.cpython-312.pyc,,
django_redis/serializers/__pycache__/pickle.cpython-312.pyc,,
django_redis/serializers/base.py,sha256=ZNgaoY9cOQQ0lYryiPBqB0DRfLW4z9QD_JusQHCy_4U,247
django_redis/serializers/json.py,sha256=eZmSRSQO0HO6HD83GJQzx_FhGFnoZ0-M70COKLQErZA,425
django_redis/serializers/msgpack.py,sha256=nfaWp9Wn1l814MZVgxAYBsypnwr3g-z-2GBBrBIlntg,308
django_redis/serializers/pickle.py,sha256=DpYYEPelDRBKRiEdxVAaaqFf5DO0W_hsTU5_d7O2Wng,1254
django_redis/util.py,sha256=oPGnTBzniEwWFjviUntfzgI_Q5xaqRtILmSMh6Zvp8c,266
