{% extends 'base.html' %}
{% load static %}

{% block title %}Payments Dashboard - Betika!{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/pages/home.css' %}">
<link rel="stylesheet" href="{% static 'css/payments.css' %}">
{% endblock %}

{% block content %}
<!-- Promotional Banner -->
<div class="promo-banner">
    <div class="promo-content">
        <div class="promo-image">
            <i class="fas fa-wallet"></i>
        </div>
        <div class="promo-text">
            <h2>Payments Dashboard</h2>
            <p>Manage your deposits, withdrawals and <strong>track your balance</strong> securely!</p>
        </div>
        <div class="promo-action">
            <a href="{% url 'payments:deposit' %}" class="btn btn-outline">Make Deposit</a>
            <button class="promo-close">&times;</button>
        </div>
    </div>
</div>

<!-- Event Navigation Tabs -->
<div class="event-tabs">
    <div class="tab-list">
        <button class="tab-btn active" data-tab="overview">Overview</button>
        <button class="tab-btn" data-tab="deposit">Deposit</button>
        <button class="tab-btn" data-tab="withdraw">Withdraw</button>
        <button class="tab-btn" data-tab="history">History</button>
    </div>

    <div class="event-filters">
        <div class="filter-group">
            <div class="balance-display">
                <span class="balance-label">Current Balance:</span>
                <span class="balance-amount">KES {{ user_balance|floatformat:2 }}</span>
            </div>
        </div>
        <div class="filter-group">
            <select class="filter-select">
                <option>All Transactions</option>
                <option>Deposits</option>
                <option>Withdrawals</option>
                <option>Completed</option>
            </select>
        </div>
    </div>
</div>

<!-- Events Content -->
<div class="events-content">
    <div class="tab-content active" id="overview">
        <div class="payments-dashboard">

    <div class="dashboard-content">
        <div class="dashboard-grid">
            <!-- Quick Actions -->
            <div class="quick-actions-card">
                <h3>Quick Actions</h3>
                <div class="action-buttons">
                    <a href="{% url 'payments:deposit' %}" class="action-btn">
                        <i class="fas fa-wallet"></i>
                        <span>Make Deposit</span>
                    </a>
                    <a href="{% url 'payments:withdraw' %}" class="action-btn">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>Withdraw Funds</span>
                    </a>
                    <a href="{% url 'payments:history' %}" class="action-btn">
                        <i class="fas fa-history"></i>
                        <span>Transaction History</span>
                    </a>
                    <a href="{% url 'payments:methods' %}" class="action-btn">
                        <i class="fas fa-credit-card"></i>
                        <span>Payment Methods</span>
                    </a>
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="recent-transactions-card">
                <div class="card-header">
                    <h3>Recent Transactions</h3>
                    <a href="{% url 'payments:history' %}" class="view-all-link">View All</a>
                </div>
                <div class="transactions-list">
                    {% if recent_transactions %}
                        {% for transaction in recent_transactions %}
                        <div class="transaction-item">
                            <div class="transaction-icon">
                                {% if transaction.transaction_type == 'deposit' %}
                                    <i class="fas fa-arrow-down text-success"></i>
                                {% elif transaction.transaction_type == 'withdrawal' %}
                                    <i class="fas fa-arrow-up text-danger"></i>
                                {% elif transaction.transaction_type == 'bet_stake' %}
                                    <i class="fas fa-ticket-alt text-warning"></i>
                                {% elif transaction.transaction_type == 'bet_winnings' %}
                                    <i class="fas fa-trophy text-success"></i>
                                {% else %}
                                    <i class="fas fa-exchange-alt"></i>
                                {% endif %}
                            </div>
                            <div class="transaction-details">
                                <div class="transaction-desc">{{ transaction.description }}</div>
                                <div class="transaction-date">{{ transaction.created_at|date:"M d, Y H:i" }}</div>
                            </div>
                            <div class="transaction-amount">
                                <span class="amount {% if transaction.is_credit %}positive{% else %}negative{% endif %}">
                                    {% if transaction.is_credit %}+{% else %}-{% endif %}KES {{ transaction.amount|floatformat:2 }}
                                </span>
                                <span class="status status-{{ transaction.status }}">{{ transaction.get_status_display }}</span>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-receipt"></i>
                            <p>No transactions yet</p>
                            <small>Your transaction history will appear here</small>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Payment Methods -->
            <div class="payment-methods-card">
                <div class="card-header">
                    <h3>Saved Payment Methods</h3>
                    <a href="{% url 'payments:add_method' %}" class="add-method-link">
                        <i class="fas fa-plus"></i> Add New
                    </a>
                </div>
                <div class="methods-list">
                    {% if payment_methods %}
                        {% for method in payment_methods %}
                        <div class="method-item">
                            <div class="method-icon">
                                {% if method.payment_type == 'mpesa' %}
                                    <i class="fas fa-mobile-alt text-success"></i>
                                {% elif method.payment_type == 'card' %}
                                    <i class="fas fa-credit-card text-primary"></i>
                                {% elif method.payment_type == 'bank_account' %}
                                    <i class="fas fa-university text-info"></i>
                                {% endif %}
                            </div>
                            <div class="method-details">
                                <div class="method-name">{{ method.get_payment_type_display }}</div>
                                <div class="method-info">
                                    {% if method.payment_type == 'mpesa' %}
                                        {{ method.mpesa_phone_number }}
                                    {% elif method.payment_type == 'card' %}
                                        {{ method.card_brand }} ****{{ method.card_last_four }}
                                    {% elif method.payment_type == 'bank_account' %}
                                        {{ method.bank_name }}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="method-status">
                                {% if method.is_verified %}
                                    <span class="verified"><i class="fas fa-check-circle"></i> Verified</span>
                                {% else %}
                                    <span class="unverified"><i class="fas fa-clock"></i> Pending</span>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-credit-card"></i>
                            <p>No payment methods saved</p>
                            <a href="{% url 'payments:add_method' %}" class="btn btn-sm btn-primary">Add Payment Method</a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
        </div>
    </div>

    <div class="tab-content" id="deposit">
        <div class="empty-state">
            <i class="fas fa-plus"></i>
            <h3>Make Deposit</h3>
            <p>Deposit funds to your account</p>
            <a href="{% url 'payments:deposit' %}" class="btn btn-primary">Make Deposit</a>
        </div>
    </div>

    <div class="tab-content" id="withdraw">
        <div class="empty-state">
            <i class="fas fa-minus"></i>
            <h3>Withdraw Funds</h3>
            <p>Withdraw your winnings</p>
            <a href="{% url 'payments:withdraw' %}" class="btn btn-primary">Withdraw</a>
        </div>
    </div>

    <div class="tab-content" id="history">
        <div class="empty-state">
            <i class="fas fa-history"></i>
            <h3>Transaction History</h3>
            <p>View all your transactions</p>
        </div>
    </div>
</div>

<!-- Betslip Component -->
<div class="betslip-container" id="betslip-container">
    <div class="betslip" id="betslip">
        <div class="betslip-header">
            <h3>Betslip (<span id="betslip-count">0</span>)</h3>
            <button class="betslip-clear" id="clear-betslip">Clear All</button>
        </div>

        <div class="betslip-content" id="betslip-content">
            <div class="betslip-empty" id="betslip-empty">
                <i class="fas fa-ticket-alt"></i>
                <p>Your betslip is empty</p>
                <small>Click on odds to add selections</small>
            </div>

            <div class="betslip-selections" id="betslip-selections">
                <!-- Dynamic selections will be added here -->
            </div>
        </div>

        <div class="betslip-footer" id="betslip-footer" style="display: none;">
            <div class="betslip-totals">
                <div class="total-odds">
                    <span>Total Odds:</span>
                    <span id="total-odds">0.00</span>
                </div>
                <div class="potential-win">
                    <span>Potential Win:</span>
                    <span id="potential-win">KES 0.00</span>
                </div>
            </div>

            <div class="stake-input">
                <label for="stake-amount">Stake Amount (KES)</label>
                <input type="number" id="stake-amount" placeholder="100" min="1" step="1">
            </div>

            {% if user.is_authenticated %}
            <div class="user-balance">
                <span>Balance: KES {{ user.balance|default:"0.00" }}</span>
                {% if user.balance < 100 %}
                <a href="{% url 'payments:deposit' %}" class="deposit-link">Deposit</a>
                {% endif %}
            </div>

            <button class="place-bet-btn" id="place-bet-btn">
                <i class="fas fa-check"></i>
                Place Bet
            </button>
            {% else %}
            <div class="login-prompt">
                <p>Login to place bets</p>
                <a href="{% url 'accounts:login' %}" class="btn btn-primary">Login</a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/pages/home.js' %}"></script>
<script src="{% static 'js/payments.js' %}"></script>
{% endblock %}