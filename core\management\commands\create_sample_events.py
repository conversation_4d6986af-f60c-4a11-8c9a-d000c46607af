"""
Management command to create sample events for testing
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from decimal import Decimal

from sports.models import Sport, Event, Market, Odds


class Command(BaseCommand):
    help = 'Create sample events for testing the betting functionality'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample events...')

        # Create or get sports
        football, _ = Sport.objects.get_or_create(
            name='Football',
            defaults={
                'slug': 'football',
                'display_order': 1,
                'is_active': True
            }
        )

        basketball, _ = Sport.objects.get_or_create(
            name='Basketball',
            defaults={
                'slug': 'basketball',
                'display_order': 2,
                'is_active': True
            }
        )

        tennis, _ = Sport.objects.get_or_create(
            name='Tennis',
            defaults={
                'slug': 'tennis',
                'display_order': 3,
                'is_active': True
            }
        )

        # Create sample events
        events_data = [
            {
                'sport': football,
                'home_team': 'Manchester United',
                'away_team': 'Liverpool FC',
                'league': 'Premier League',
                'start_time': timezone.now() + timedelta(hours=2),
                'status': 'upcoming',
                'is_featured': True
            },
            {
                'sport': football,
                'home_team': 'Real Madrid',
                'away_team': 'Barcelona',
                'league': 'La Liga',
                'start_time': timezone.now() + timedelta(hours=4),
                'status': 'upcoming',
                'is_featured': True
            },
            {
                'sport': football,
                'home_team': 'Bayern Munich',
                'away_team': 'Borussia Dortmund',
                'league': 'Bundesliga',
                'start_time': timezone.now() + timedelta(hours=6),
                'status': 'upcoming',
                'is_featured': False
            },
            {
                'sport': basketball,
                'home_team': 'Los Angeles Lakers',
                'away_team': 'Golden State Warriors',
                'league': 'NBA',
                'start_time': timezone.now() + timedelta(hours=8),
                'status': 'upcoming',
                'is_featured': True
            },
            {
                'sport': tennis,
                'home_team': 'Novak Djokovic',
                'away_team': 'Rafael Nadal',
                'league': 'ATP Masters',
                'start_time': timezone.now() + timedelta(hours=1),
                'status': 'live',
                'is_featured': True
            },
        ]

        for i, event_data in enumerate(events_data):
            # Add unique external_id to avoid conflicts
            event_data['external_id'] = f'sample_event_{i+1}'

            event, created = Event.objects.get_or_create(
                home_team=event_data['home_team'],
                away_team=event_data['away_team'],
                defaults=event_data
            )

            if created:
                self.stdout.write(f'Created event: {event}')

                # Create Match Result market
                market, _ = Market.objects.get_or_create(
                    event=event,
                    name='Match Result',
                    market_type='Match Result',
                    defaults={'is_active': True}
                )

                # Create odds for the market
                if event_data['sport'] == tennis:
                    # Tennis has only 2 outcomes
                    odds_data = [
                        {'selection': event_data['home_team'], 'odds_value': Decimal('1.85')},
                        {'selection': event_data['away_team'], 'odds_value': Decimal('1.95')},
                    ]
                else:
                    # Football/Basketball has 3 outcomes
                    odds_data = [
                        {'selection': '1', 'odds_value': Decimal('2.10')},
                        {'selection': 'X', 'odds_value': Decimal('3.40')},
                        {'selection': '2', 'odds_value': Decimal('3.20')},
                    ]

                for i, odds_info in enumerate(odds_data):
                    Odds.objects.get_or_create(
                        market=market,
                        selection=odds_info['selection'],
                        defaults={
                            'odds_value': odds_info['odds_value'],
                            'is_active': True,
                            'display_order': i
                        }
                    )

                # Create Over/Under market for football and basketball
                if event_data['sport'] in [football, basketball]:
                    ou_market, _ = Market.objects.get_or_create(
                        event=event,
                        name='Total Goals' if event_data['sport'] == football else 'Total Points',
                        market_type='Over/Under',
                        defaults={'is_active': True}
                    )

                    ou_odds = [
                        {'selection': 'Over 2.5', 'odds_value': Decimal('1.75')},
                        {'selection': 'Under 2.5', 'odds_value': Decimal('2.05')},
                    ]

                    for i, odds_info in enumerate(ou_odds):
                        Odds.objects.get_or_create(
                            market=ou_market,
                            selection=odds_info['selection'],
                            defaults={
                                'odds_value': odds_info['odds_value'],
                                'is_active': True,
                                'display_order': i
                            }
                        )

        self.stdout.write(
            self.style.SUCCESS('Successfully created sample events!')
        )
