"""
CSS optimization utilities for the Betika clone platform
"""

import os
import re
import gzip
from pathlib import Path
from django.conf import settings
from django.core.management.base import BaseCommand
from django.template.loader import get_template
from django.test import RequestFactory
import logging

logger = logging.getLogger(__name__)


class CSSOptimizer:
    """
    CSS optimization and critical CSS extraction
    """
    
    def __init__(self):
        self.static_root = Path(settings.STATIC_ROOT) if settings.STATIC_ROOT else Path(settings.BASE_DIR) / 'static'
        self.critical_css_patterns = [
            # Above-the-fold content patterns
            r'\.header-nav.*?{[^}]+}',
            r'\.logo.*?{[^}]+}',
            r'\.main-nav.*?{[^}]+}',
            r'\.nav-tab.*?{[^}]+}',
            r'\.user-controls.*?{[^}]+}',
            r'\.loading-spinner.*?{[^}]+}',
            r'\.error-message.*?{[^}]+}',
            
            # Critical layout patterns
            r'body.*?{[^}]+}',
            r'html.*?{[^}]+}',
            r'\.container.*?{[^}]+}',
            r'\.main-container.*?{[^}]+}',
            r'\.content-area.*?{[^}]+}',
            
            # Mobile-first critical styles
            r'@media.*?max-width:\s*767px.*?{[^}]+}',
            
            # Font and typography
            r'@font-face.*?{[^}]+}',
            r'h[1-6].*?{[^}]+}',
            r'p.*?{[^}]+}',
            r'a.*?{[^}]+}',
        ]
    
    def extract_critical_css(self, css_content):
        """
        Extract critical CSS that should be inlined
        """
        critical_css = []
        
        for pattern in self.critical_css_patterns:
            matches = re.findall(pattern, css_content, re.DOTALL | re.IGNORECASE)
            critical_css.extend(matches)
        
        return '\n'.join(critical_css)
    
    def minify_css(self, css_content):
        """
        Minify CSS content
        """
        # Remove comments
        css_content = re.sub(r'/\*.*?\*/', '', css_content, flags=re.DOTALL)
        
        # Remove unnecessary whitespace
        css_content = re.sub(r'\s+', ' ', css_content)
        css_content = re.sub(r';\s*}', '}', css_content)
        css_content = re.sub(r'{\s*', '{', css_content)
        css_content = re.sub(r'}\s*', '}', css_content)
        css_content = re.sub(r':\s*', ':', css_content)
        css_content = re.sub(r';\s*', ';', css_content)
        css_content = re.sub(r',\s*', ',', css_content)
        
        # Remove trailing semicolons
        css_content = re.sub(r';(?=\s*})', '', css_content)
        
        return css_content.strip()
    
    def remove_unused_css(self, css_content, used_selectors):
        """
        Remove unused CSS selectors (basic implementation)
        """
        # This is a simplified version - in production, you'd use tools like PurgeCSS
        lines = css_content.split('\n')
        filtered_lines = []
        
        for line in lines:
            # Check if line contains a selector that's being used
            if any(selector in line for selector in used_selectors):
                filtered_lines.append(line)
            elif line.strip().startswith('@') or line.strip().startswith('}') or not line.strip():
                # Keep media queries, closing braces, and empty lines
                filtered_lines.append(line)
        
        return '\n'.join(filtered_lines)
    
    def optimize_css_file(self, file_path, output_path=None):
        """
        Optimize a CSS file
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                css_content = f.read()
            
            # Minify CSS
            minified_css = self.minify_css(css_content)
            
            # Write optimized CSS
            output_path = output_path or file_path
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(minified_css)
            
            # Create gzipped version
            gzip_path = f"{output_path}.gz"
            with gzip.open(gzip_path, 'wt', encoding='utf-8') as f:
                f.write(minified_css)
            
            original_size = len(css_content)
            minified_size = len(minified_css)
            compression_ratio = (1 - minified_size / original_size) * 100
            
            logger.info(f"Optimized {file_path}: {original_size} -> {minified_size} bytes ({compression_ratio:.1f}% reduction)")
            
            return {
                'original_size': original_size,
                'minified_size': minified_size,
                'compression_ratio': compression_ratio,
                'gzip_path': gzip_path
            }
            
        except Exception as e:
            logger.error(f"Error optimizing CSS file {file_path}: {e}")
            return None
    
    def generate_critical_css_file(self, template_names=None):
        """
        Generate critical CSS file for above-the-fold content
        """
        if not template_names:
            template_names = ['base.html', 'home.html', 'sports/sports_list.html']
        
        all_css_content = ""
        
        # Collect all CSS files
        css_files = [
            'css/base.css',
            'css/theme/betika-theme.css',
            'css/responsive.css',
            'css/components/betslip.css'
        ]
        
        for css_file in css_files:
            css_path = self.static_root / css_file
            if css_path.exists():
                with open(css_path, 'r', encoding='utf-8') as f:
                    all_css_content += f.read() + '\n'
        
        # Extract critical CSS
        critical_css = self.extract_critical_css(all_css_content)
        
        # Minify critical CSS
        critical_css = self.minify_css(critical_css)
        
        # Write critical CSS file
        critical_css_path = self.static_root / 'css' / 'critical.css'
        critical_css_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(critical_css_path, 'w', encoding='utf-8') as f:
            f.write(critical_css)
        
        logger.info(f"Generated critical CSS: {len(critical_css)} bytes")
        
        return critical_css_path
    
    def optimize_all_css_files(self):
        """
        Optimize all CSS files in the static directory
        """
        css_files = list(self.static_root.rglob('*.css'))
        results = []
        
        for css_file in css_files:
            if 'compressed' not in str(css_file) and 'critical' not in str(css_file):
                result = self.optimize_css_file(css_file)
                if result:
                    results.append({
                        'file': str(css_file),
                        **result
                    })
        
        return results
    
    def create_css_sprite(self, image_dir, output_path):
        """
        Create CSS sprite from images (basic implementation)
        """
        # This would typically use PIL or similar library
        # For now, just return a placeholder
        sprite_css = """
        .sprite {
            background-image: url('sprite.png');
            background-repeat: no-repeat;
        }
        
        .sprite-icon-1 { background-position: 0 0; width: 16px; height: 16px; }
        .sprite-icon-2 { background-position: -16px 0; width: 16px; height: 16px; }
        """
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(sprite_css)
        
        return output_path


class CSSAnalyzer:
    """
    Analyze CSS usage and performance
    """
    
    def __init__(self):
        self.static_root = Path(settings.STATIC_ROOT) if settings.STATIC_ROOT else Path(settings.BASE_DIR) / 'static'
    
    def analyze_css_file(self, file_path):
        """
        Analyze a CSS file for performance metrics
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Count selectors
            selectors = re.findall(r'[^{}]+(?=\s*{)', content)
            selector_count = len(selectors)
            
            # Count rules
            rules = re.findall(r'{[^}]+}', content)
            rule_count = len(rules)
            
            # Count media queries
            media_queries = re.findall(r'@media[^{]+{', content)
            media_query_count = len(media_queries)
            
            # Calculate complexity score
            complexity_score = selector_count + (rule_count * 0.5) + (media_query_count * 2)
            
            # File size
            file_size = len(content)
            
            return {
                'file_path': str(file_path),
                'file_size': file_size,
                'selector_count': selector_count,
                'rule_count': rule_count,
                'media_query_count': media_query_count,
                'complexity_score': complexity_score,
                'estimated_parse_time': complexity_score * 0.01  # ms
            }
            
        except Exception as e:
            logger.error(f"Error analyzing CSS file {file_path}: {e}")
            return None
    
    def analyze_all_css_files(self):
        """
        Analyze all CSS files
        """
        css_files = list(self.static_root.rglob('*.css'))
        results = []
        
        for css_file in css_files:
            result = self.analyze_css_file(css_file)
            if result:
                results.append(result)
        
        return results
    
    def generate_performance_report(self):
        """
        Generate CSS performance report
        """
        analysis_results = self.analyze_all_css_files()
        
        total_size = sum(result['file_size'] for result in analysis_results)
        total_selectors = sum(result['selector_count'] for result in analysis_results)
        total_complexity = sum(result['complexity_score'] for result in analysis_results)
        
        report = {
            'total_files': len(analysis_results),
            'total_size': total_size,
            'total_selectors': total_selectors,
            'total_complexity': total_complexity,
            'average_file_size': total_size / len(analysis_results) if analysis_results else 0,
            'files': analysis_results
        }
        
        return report


def inline_critical_css(template_content, critical_css_path):
    """
    Inline critical CSS into template
    """
    try:
        with open(critical_css_path, 'r', encoding='utf-8') as f:
            critical_css = f.read()
        
        # Find the head section and inject critical CSS
        head_pattern = r'(<head[^>]*>)'
        critical_css_tag = f'\\1\n<style>{critical_css}</style>'
        
        return re.sub(head_pattern, critical_css_tag, template_content, flags=re.IGNORECASE)
        
    except Exception as e:
        logger.error(f"Error inlining critical CSS: {e}")
        return template_content
