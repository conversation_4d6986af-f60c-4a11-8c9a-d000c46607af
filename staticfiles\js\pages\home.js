// Home Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tab switching
    initializeTabs();
    
    // Initialize promotional banner close button
    initializePromoBanner();
    
    // Initialize welcome modal for new users
    initializeWelcomeModal();
    
    // Initialize odds buttons
    initializeOddsButtons();
});

// Tab Switching Functionality
function initializeTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons and contents
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Add active class to clicked button
            this.classList.add('active');
            
            // Show corresponding content
            const tabId = this.dataset.tab;
            document.getElementById(tabId).classList.add('active');
        });
    });
}

// Promotional Banner
function initializePromoBanner() {
    const closeButton = document.querySelector('.promo-close');
    const promoBanner = document.querySelector('.promo-banner');
    
    if (closeButton && promoBanner) {
        closeButton.addEventListener('click', function() {
            promoBanner.style.display = 'none';
            
            // Store in session storage to keep it closed during session
            sessionStorage.setItem('promoBannerClosed', 'true');
        });
        
        // Check if banner was previously closed
        if (sessionStorage.getItem('promoBannerClosed') === 'true') {
            promoBanner.style.display = 'none';
        }
    }
}

// Welcome Modal for New Users
function initializeWelcomeModal() {
    const welcomeOverlay = document.querySelector('.welcome-overlay');
    const closeButton = document.querySelector('.welcome-close');
    
    if (welcomeOverlay && closeButton) {
        closeButton.addEventListener('click', function() {
            welcomeOverlay.style.display = 'none';
            
            // Store in local storage to not show again
            localStorage.setItem('welcomeModalShown', 'true');
        });
        
        // Check if modal was previously shown
        if (localStorage.getItem('welcomeModalShown') === 'true') {
            welcomeOverlay.style.display = 'none';
        }
    }
}

// Odds Button Functionality
function initializeOddsButtons() {
    const oddsButtons = document.querySelectorAll('.odds-btn');
    
    oddsButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Toggle selection
            this.classList.toggle('selected');
            
            // Get selection data
            const selection = this.dataset.selection;
            const odds = this.dataset.odds;
            const eventId = this.dataset.eventId;
            const marketType = this.dataset.marketType || 'Match Result';
            
            // If selected, add to betslip
            if (this.classList.contains('selected')) {
                addToBetslip(selection, odds, this);
            } else {
                removeFromBetslip({
                    selection: selection,
                    eventId: eventId
                });
            }
        });
    });
}

// Add selection to betslip
function addToBetslip(selection, odds, button) {
    // Get event information from the button's parent elements
    const eventCard = button ? button.closest('.event-card') : null;
    let eventName = 'Unknown Event';
    let eventId = generateEventId();
    let marketType = 'Match Result';
    let oddsId = button ? button.dataset.oddsId : `odds_${Date.now()}`;
    
    if (eventCard) {
        const teamsElement = eventCard.querySelector('.teams');
        if (teamsElement) {
            eventName = teamsElement.textContent.replace(/\s+vs\s+/, ' vs ').trim();
        }
        
        // Try to get event ID from data attribute or generate one
        if (eventCard.dataset.eventId) {
            eventId = eventCard.dataset.eventId;
        } else if (button && button.dataset.eventId) {
            eventId = button.dataset.eventId;
        }
    }
    
    // Get market type from button if available
    if (button && button.dataset.marketType) {
        marketType = button.dataset.marketType;
    }
    
    // Create selection object
    const selectionData = {
        selection: selection,
        oddsValue: parseFloat(odds),
        eventId: eventId,
        eventName: eventName,
        marketType: marketType,
        oddsId: oddsId
    };
    
    // Check if betslip functionality is available
    if (window.betSlipModule && window.betSlipModule.addToBetSlip) {
        // Use the betslip module
        window.betSlipModule.addToBetSlip(selectionData);
    } else {
        // Fallback notification
        showNotification(`Added ${selection} @ ${odds} to betslip`);
    }
}

// Generate unique event ID
function generateEventId() {
    return 'event_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
}

// Remove selection from betslip
function removeFromBetslip(selection) {
    // Check if betslip functionality is available from betslip.js
    if (window.betSlipModule && window.betSlipModule.removeFromBetSlip) {
        // Use the betslip module
        window.betSlipModule.removeFromBetSlip(selection);
    } else if (window.sportsModule && window.sportsModule.removeFromBetSlip) {
        // Use the sports module as fallback
        window.sportsModule.removeFromBetSlip(selection);
    } else {
        // Simple notification if betslip module not loaded
        showNotification(`Removed ${selection.selection || selection} from betslip`);
    }
}

// Simple notification function
function showNotification(message) {
    // Check if Utils is available from main.js
    if (window.Utils && window.Utils.showNotification) {
        window.Utils.showNotification(message);
        return;
    }
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    
    // Style the notification
    Object.assign(notification.style, {
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        backgroundColor: '#4CAF50',
        color: 'white',
        padding: '12px 20px',
        borderRadius: '4px',
        boxShadow: '0 2px 5px rgba(0,0,0,0.2)',
        zIndex: '9999',
        opacity: '0',
        transition: 'opacity 0.3s ease'
    });
    
    // Add to page
    document.body.appendChild(notification);
    
    // Fade in
    setTimeout(() => {
        notification.style.opacity = '1';
    }, 10);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Market expansion functionality
document.addEventListener('DOMContentLoaded', function() {
    const expandButtons = document.querySelectorAll('.expand-btn');
    
    expandButtons.forEach(button => {
        button.addEventListener('click', function() {
            const eventId = this.dataset.eventId;
            
            // Show loading state
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
            this.disabled = true;
            
            // This would typically load additional markets via AJAX
            // For now, simulate loading with a timeout
            setTimeout(() => {
                // Reset button
                this.innerHTML = '<i class="fas fa-plus"></i> More Markets';
                this.disabled = false;
                
                // Show notification
                showNotification('Additional markets loading feature coming soon');
            }, 1000);
        });
    });
});