# Generated database optimization migration

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0001_initial'),
        ('betting', '0001_initial'),
        ('sports', '0001_initial'),
        ('payments', '0001_initial'),
        ('live_betting', '0001_initial'),
        ('jackpot', '0001_initial'),
        ('support', '0001_initial'),
        ('admin_panel', '0001_initial'),
        ('analytics', '0001_initial'),
    ]

    operations = [
        # CustomUser model optimizations
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customuser_phone_verified ON accounts_customuser(phone_number, is_verified);",
            reverse_sql="DROP INDEX IF EXISTS idx_customuser_phone_verified;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customuser_balance_active ON accounts_customuser(balance) WHERE NOT is_suspended;",
            reverse_sql="DROP INDEX IF EXISTS idx_customuser_balance_active;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customuser_last_login ON accounts_customuser(last_login) WHERE last_login IS NOT NULL;",
            reverse_sql="DROP INDEX IF EXISTS idx_customuser_last_login;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customuser_date_joined ON accounts_customuser(date_joined);",
            reverse_sql="DROP INDEX IF EXISTS idx_customuser_date_joined;"
        ),

        # UserProfile model optimizations
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_userprofile_kyc_status ON accounts_userprofile(kyc_status);",
            reverse_sql="DROP INDEX IF EXISTS idx_userprofile_kyc_status;"
        ),

        # Transaction model optimizations
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transaction_user_type_status ON payments_transaction(user_id, transaction_type, status);",
            reverse_sql="DROP INDEX IF EXISTS idx_transaction_user_type_status;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transaction_created_status ON payments_transaction(created_at, status);",
            reverse_sql="DROP INDEX IF EXISTS idx_transaction_created_status;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transaction_external_id ON payments_transaction(external_transaction_id) WHERE external_transaction_id IS NOT NULL;",
            reverse_sql="DROP INDEX IF EXISTS idx_transaction_external_id;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transaction_amount_type ON payments_transaction(amount, transaction_type);",
            reverse_sql="DROP INDEX IF EXISTS idx_transaction_amount_type;"
        ),

        # Bet model optimizations
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bet_user_placed_at ON betting_bet(user_id, placed_at);",
            reverse_sql="DROP INDEX IF EXISTS idx_bet_user_placed_at;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bet_status_settled_at ON betting_bet(status, settled_at);",
            reverse_sql="DROP INDEX IF EXISTS idx_bet_status_settled_at;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bet_stake_amount ON betting_bet(stake) WHERE status = 'pending';",
            reverse_sql="DROP INDEX IF EXISTS idx_bet_stake_amount;"
        ),

        # BetSelection model optimizations
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_betselection_odds_status ON betting_betselection(odds_id, status);",
            reverse_sql="DROP INDEX IF EXISTS idx_betselection_odds_status;"
        ),

        # Event model optimizations
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_event_sport_start_time ON sports_event(sport_id, start_time);",
            reverse_sql="DROP INDEX IF EXISTS idx_event_sport_start_time;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_event_featured_status ON sports_event(is_featured, status) WHERE is_featured = true;",
            reverse_sql="DROP INDEX IF EXISTS idx_event_featured_status;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_event_league_status ON sports_event(league, status);",
            reverse_sql="DROP INDEX IF EXISTS idx_event_league_status;"
        ),

        # Market model optimizations
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_market_event_type_active ON sports_market(event_id, market_type, is_active);",
            reverse_sql="DROP INDEX IF EXISTS idx_market_event_type_active;"
        ),

        # Odds model optimizations
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_odds_value_updated ON sports_odds(odds_value, last_updated) WHERE is_active = true;",
            reverse_sql="DROP INDEX IF EXISTS idx_odds_value_updated;"
        ),

        # Analytics model optimizations
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_useractivity_user_date ON analytics_useractivity(user_id, activity_date);",
            reverse_sql="DROP INDEX IF EXISTS idx_useractivity_user_date;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_useractivity_type_date ON analytics_useractivity(activity_type, activity_date);",
            reverse_sql="DROP INDEX IF EXISTS idx_useractivity_type_date;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_dailymetrics_date ON analytics_dailymetrics(date);",
            reverse_sql="DROP INDEX IF EXISTS idx_dailymetrics_date;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bettingpattern_user_updated ON analytics_bettingpattern(user_id, last_updated);",
            reverse_sql="DROP INDEX IF EXISTS idx_bettingpattern_user_updated;"
        ),

        # Support model optimizations (additional to existing)
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ticket_priority_created ON support_ticket(priority, created_at);",
            reverse_sql="DROP INDEX IF EXISTS idx_ticket_priority_created;"
        ),

        # Jackpot model optimizations (additional to existing)
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_jackpot_status_start_time ON jackpot_jackpot(status, start_time);",
            reverse_sql="DROP INDEX IF EXISTS idx_jackpot_status_start_time;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_jackpotentry_user_created ON jackpot_jackpotentry(user_id, created_at);",
            reverse_sql="DROP INDEX IF EXISTS idx_jackpotentry_user_created;"
        ),

        # Admin panel optimizations
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_auditlog_user_timestamp ON admin_panel_auditlog(user_id, timestamp);",
            reverse_sql="DROP INDEX IF EXISTS idx_auditlog_user_timestamp;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_auditlog_action_timestamp ON admin_panel_auditlog(action, timestamp);",
            reverse_sql="DROP INDEX IF EXISTS idx_auditlog_action_timestamp;"
        ),

        # Composite indexes for common query patterns
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bet_user_status_placed ON betting_bet(user_id, status, placed_at);",
            reverse_sql="DROP INDEX IF EXISTS idx_bet_user_status_placed;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_event_sport_status_start ON sports_event(sport_id, status, start_time);",
            reverse_sql="DROP INDEX IF EXISTS idx_event_sport_status_start;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transaction_user_status_created ON payments_transaction(user_id, status, created_at);",
            reverse_sql="DROP INDEX IF EXISTS idx_transaction_user_status_created;"
        ),
    ]
