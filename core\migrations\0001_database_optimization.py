# Generated database optimization migration

from django.db import migrations


def create_indexes_forward(apps, schema_editor):
    """Create basic database indexes that work with SQLite"""
    
    # Only create indexes for tables that definitely exist
    # We'll skip conditional indexes for SQLite compatibility
    
    try:
        # Basic index on accounts_customuser if it exists
        schema_editor.execute(
            "CREATE INDEX IF NOT EXISTS idx_customuser_date_joined ON accounts_customuser(date_joined)"
        )
        print("Created index: idx_customuser_date_joined")
    except Exception as e:
        print(f"Could not create user date index: {e}")
    
    print("Database optimization migration completed successfully")


def drop_indexes_reverse(apps, schema_editor):
    """Drop database indexes"""
    
    try:
        schema_editor.execute("DROP INDEX IF EXISTS idx_customuser_date_joined")
        print("Dropped index: idx_customuser_date_joined")
    except Exception as e:
        print(f"Could not drop user date index: {e}")


class Migration(migrations.Migration):

    dependencies = [
        # No dependencies - this migration creates indexes independently
    ]

    operations = [
        migrations.RunPython(
            create_indexes_forward,
            drop_indexes_reverse,
        ),
    ]
