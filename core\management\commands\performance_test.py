"""
Performance testing and monitoring command
"""

from django.core.management.base import BaseCommand
from django.test import Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db import connection
from django.core.cache import cache
import time
import json
import statistics

from core.database_router import get_database_performance_metrics
from core.css_optimizer import CSSAnalyzer

User = get_user_model()


class Command(BaseCommand):
    help = 'Run performance tests and generate reports'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--test-type',
            type=str,
            choices=['all', 'database', 'cache', 'frontend', 'api'],
            default='all',
            help='Type of performance test to run'
        )
        
        parser.add_argument(
            '--iterations',
            type=int,
            default=10,
            help='Number of test iterations'
        )
        
        parser.add_argument(
            '--output',
            type=str,
            help='Output file for results'
        )
    
    def handle(self, *args, **options):
        test_type = options['test_type']
        iterations = options['iterations']
        
        self.stdout.write(
            self.style.SUCCESS(f'Starting performance tests ({test_type})...')
        )
        
        results = {}
        
        if test_type in ['all', 'database']:
            results['database'] = self.test_database_performance(iterations)
        
        if test_type in ['all', 'cache']:
            results['cache'] = self.test_cache_performance(iterations)
        
        if test_type in ['all', 'frontend']:
            results['frontend'] = self.test_frontend_performance()
        
        if test_type in ['all', 'api']:
            results['api'] = self.test_api_performance(iterations)
        
        # Generate report
        self.generate_report(results)
        
        # Save to file if specified
        if options['output']:
            with open(options['output'], 'w') as f:
                json.dump(results, f, indent=2, default=str)
            self.stdout.write(f"Results saved to {options['output']}")
    
    def test_database_performance(self, iterations):
        """Test database query performance"""
        self.stdout.write('Testing database performance...')
        
        results = {
            'query_times': [],
            'connection_stats': {},
            'slow_queries': []
        }
        
        # Test common queries
        test_queries = [
            ('User count', lambda: User.objects.count()),
            ('Active users', lambda: User.objects.filter(is_active=True).count()),
        ]
        
        for query_name, query_func in test_queries:
            times = []
            
            for _ in range(iterations):
                # Reset query log
                connection.queries_log.clear()
                
                start_time = time.time()
                result = query_func()
                end_time = time.time()
                
                query_time = (end_time - start_time) * 1000  # Convert to ms
                times.append(query_time)
                
                # Check for slow queries
                for query in connection.queries:
                    if float(query['time']) > 0.1:  # 100ms threshold
                        results['slow_queries'].append({
                            'sql': query['sql'][:200],
                            'time': float(query['time'])
                        })
            
            results['query_times'].append({
                'query': query_name,
                'avg_time': statistics.mean(times),
                'min_time': min(times),
                'max_time': max(times),
                'median_time': statistics.median(times)
            })
        
        # Get database connection stats
        results['connection_stats'] = get_database_performance_metrics()
        
        return results
    
    def test_cache_performance(self, iterations):
        """Test cache performance"""
        self.stdout.write('Testing cache performance...')
        
        results = {
            'set_times': [],
            'get_times': [],
            'hit_rate': 0
        }
        
        # Test cache set performance
        set_times = []
        for i in range(iterations):
            start_time = time.time()
            cache.set(f'test_key_{i}', f'test_value_{i}', 300)
            end_time = time.time()
            set_times.append((end_time - start_time) * 1000)
        
        results['set_times'] = {
            'avg': statistics.mean(set_times),
            'min': min(set_times),
            'max': max(set_times)
        }
        
        # Test cache get performance
        get_times = []
        hits = 0
        for i in range(iterations):
            start_time = time.time()
            value = cache.get(f'test_key_{i}')
            end_time = time.time()
            get_times.append((end_time - start_time) * 1000)
            if value is not None:
                hits += 1
        
        results['get_times'] = {
            'avg': statistics.mean(get_times),
            'min': min(get_times),
            'max': max(get_times)
        }
        
        results['hit_rate'] = (hits / iterations) * 100
        
        # Clean up test keys
        for i in range(iterations):
            cache.delete(f'test_key_{i}')
        
        return results
    
    def test_frontend_performance(self):
        """Test frontend performance"""
        self.stdout.write('Testing frontend performance...')
        
        analyzer = CSSAnalyzer()
        css_report = analyzer.generate_performance_report()
        
        # Test static file sizes
        import os
        from django.conf import settings
        
        static_files = {}
        if hasattr(settings, 'STATIC_ROOT') and settings.STATIC_ROOT:
            static_root = settings.STATIC_ROOT
            for root, dirs, files in os.walk(static_root):
                for file in files:
                    if file.endswith(('.css', '.js')):
                        file_path = os.path.join(root, file)
                        file_size = os.path.getsize(file_path)
                        relative_path = os.path.relpath(file_path, static_root)
                        static_files[relative_path] = file_size
        
        return {
            'css_analysis': css_report,
            'static_files': static_files,
            'recommendations': self.get_frontend_recommendations(css_report, static_files)
        }
    
    def test_api_performance(self, iterations):
        """Test API endpoint performance"""
        self.stdout.write('Testing API performance...')
        
        client = Client()
        results = {}
        
        # Test endpoints
        endpoints = [
            ('Sports List', '/api/v1/sports/'),
            ('Events List', '/api/v1/sports/events/'),
        ]
        
        for endpoint_name, url in endpoints:
            times = []
            status_codes = []
            
            for _ in range(iterations):
                start_time = time.time()
                try:
                    response = client.get(url)
                    end_time = time.time()
                    
                    response_time = (end_time - start_time) * 1000
                    times.append(response_time)
                    status_codes.append(response.status_code)
                except Exception as e:
                    self.stdout.write(f"Error testing {url}: {e}")
                    continue
            
            if times:
                results[endpoint_name] = {
                    'avg_response_time': statistics.mean(times),
                    'min_response_time': min(times),
                    'max_response_time': max(times),
                    'median_response_time': statistics.median(times),
                    'success_rate': (status_codes.count(200) / len(status_codes)) * 100,
                    'total_requests': len(times)
                }
        
        return results
    
    def get_frontend_recommendations(self, css_report, static_files):
        """Generate frontend performance recommendations"""
        recommendations = []
        
        # CSS recommendations
        if css_report['total_size'] > 100000:  # 100KB
            recommendations.append('Consider splitting large CSS files')
        
        if css_report['total_complexity'] > 1000:
            recommendations.append('High CSS complexity - consider simplification')
        
        # Static file recommendations
        large_files = [f for f, size in static_files.items() if size > 50000]  # 50KB
        if large_files:
            recommendations.append(f'{len(large_files)} large static files found')
        
        # General recommendations
        recommendations.extend([
            'Enable gzip compression',
            'Use CDN for static files',
            'Implement browser caching',
            'Minify CSS and JavaScript',
            'Optimize images',
            'Use lazy loading for images'
        ])
        
        return recommendations
    
    def generate_report(self, results):
        """Generate performance report"""
        self.stdout.write('\n' + '='*60)
        self.stdout.write(self.style.SUCCESS('PERFORMANCE TEST REPORT'))
        self.stdout.write('='*60)
        
        # Database results
        if 'database' in results:
            self.stdout.write('\nDATABASE PERFORMANCE:')
            self.stdout.write('-' * 30)
            
            for query_result in results['database']['query_times']:
                self.stdout.write(
                    f"{query_result['query']}: "
                    f"avg={query_result['avg_time']:.2f}ms, "
                    f"median={query_result['median_time']:.2f}ms"
                )
            
            slow_queries = results['database']['slow_queries']
            if slow_queries:
                self.stdout.write(f"\nSlow queries found: {len(slow_queries)}")
                for query in slow_queries[:3]:  # Show first 3
                    self.stdout.write(f"  {query['time']:.3f}s: {query['sql']}")
        
        # Cache results
        if 'cache' in results:
            self.stdout.write('\nCACHE PERFORMANCE:')
            self.stdout.write('-' * 30)
            
            cache_results = results['cache']
            self.stdout.write(f"Set operations: avg={cache_results['set_times']['avg']:.2f}ms")
            self.stdout.write(f"Get operations: avg={cache_results['get_times']['avg']:.2f}ms")
            self.stdout.write(f"Hit rate: {cache_results['hit_rate']:.1f}%")
        
        # API results
        if 'api' in results:
            self.stdout.write('\nAPI PERFORMANCE:')
            self.stdout.write('-' * 30)
            
            for endpoint, metrics in results['api'].items():
                self.stdout.write(
                    f"{endpoint}: "
                    f"avg={metrics['avg_response_time']:.2f}ms, "
                    f"success={metrics['success_rate']:.1f}%"
                )
        
        # Frontend results
        if 'frontend' in results:
            self.stdout.write('\nFRONTEND PERFORMANCE:')
            self.stdout.write('-' * 30)
            
            css_analysis = results['frontend']['css_analysis']
            self.stdout.write(f"Total CSS files: {css_analysis['total_files']}")
            self.stdout.write(f"Total CSS size: {css_analysis['total_size']:,} bytes")
            self.stdout.write(f"CSS complexity: {css_analysis['total_complexity']:.1f}")
            
            static_files = results['frontend']['static_files']
            total_static_size = sum(static_files.values())
            self.stdout.write(f"Total static files size: {total_static_size:,} bytes")
        
        # Recommendations
        if 'frontend' in results and 'recommendations' in results['frontend']:
            self.stdout.write('\nRECOMMENDATIONS:')
            self.stdout.write('-' * 30)
            for rec in results['frontend']['recommendations'][:5]:  # Show top 5
                self.stdout.write(f"• {rec}")
        
        self.stdout.write('\n' + '='*60)
