WARNING 2025-07-16 16:10:11,437 log 20164 16240 Forbidden: /api/v1/sports/events/1/markets/
WARNING 2025-07-16 16:10:12,406 log 20164 16240 Forbidden: /api/v1/sports/markets/1/odds/
WARNING 2025-07-16 16:10:13,393 log 20164 16240 Forbidden: /api/v1/sports/events/1/odds-changes/
WARNING 2025-07-16 16:10:14,531 log 20164 16240 Forbidden: /api/v1/sports/odds/update/
WARNING 2025-07-16 16:29:49,817 log 6340 22016 Forbidden: /api/v1/sports/odds/update/
WARNING 2025-07-16 16:32:15,371 log 21348 16268 Forbidden: /api/v1/sports/odds/update/
WARNING 2025-07-16 16:32:15,371 log 21348 16268 Forbidden: /api/v1/sports/odds/update/
WARNING 2025-07-16 16:33:55,451 log 10304 9936 Forbidden: /api/v1/sports/odds/update/
WARNING 2025-07-16 16:33:55,457 log 10304 9936 Forbidden: /api/v1/sports/odds/update/
WARNING 2025-07-16 16:35:25,383 log 19764 18912 Forbidden: /api/v1/sports/odds/update/
WARNING 2025-07-16 16:35:25,383 log 19764 18912 Forbidden: /api/v1/sports/odds/update/
WARNING 2025-07-16 16:38:14,598 log 19272 21692 Forbidden: /api/v1/sports/odds/update/
WARNING 2025-07-16 16:38:14,615 log 19272 21692 Forbidden: /api/v1/sports/odds/update/
INFO 2025-07-16 16:58:15,493 autoreload 19300 10208 Watching for file changes with StatReloader
INFO 2025-07-16 16:58:23,448 basehttp 19300 22204 "GET / HTTP/1.1" 200 4160
INFO 2025-07-16 16:58:23,750 basehttp 19300 21900 "GET /static/js/main.js HTTP/1.1" 200 7698
INFO 2025-07-16 16:58:23,754 basehttp 19300 22204 "GET /static/css/base.css HTTP/1.1" 200 3478
WARNING 2025-07-16 16:58:28,067 basehttp 19300 22204 "GET /static/images/favicon.ico HTTP/1.1" 404 1914
INFO 2025-07-16 16:58:47,617 basehttp 19300 21900 "GET /accounts/login/ HTTP/1.1" 200 4951
INFO 2025-07-16 16:58:54,705 basehttp 19300 21900 "GET /accounts/register/ HTTP/1.1" 200 7591
INFO 2025-07-16 16:58:59,822 basehttp 19300 21900 "GET /live/ HTTP/1.1" 200 36
WARNING 2025-07-16 16:59:01,148 log 19300 21900 Not Found: /favicon.ico
WARNING 2025-07-16 16:59:01,169 basehttp 19300 21900 "GET /favicon.ico HTTP/1.1" 404 3593
INFO 2025-07-16 16:59:10,531 basehttp 19300 21900 "GET /sports/ HTTP/1.1" 200 3483
INFO 2025-07-16 16:59:10,616 basehttp 19300 17000 "GET /static/js/sports.js HTTP/1.1" 200 25761
INFO 2025-07-16 16:59:10,663 basehttp 19300 21900 "GET /static/css/sports.css HTTP/1.1" 200 15621
INFO 2025-07-16 18:34:56,080 autoreload 11224 11248 Watching for file changes with StatReloader
INFO 2025-07-16 18:35:14,983 basehttp 11224 8868 "GET / HTTP/1.1" 200 16268
INFO 2025-07-16 18:35:15,585 basehttp 11224 8868 "GET /static/css/base.css HTTP/1.1" 200 3517
INFO 2025-07-16 18:35:15,662 basehttp 11224 8868 "GET /static/css/theme/betika-theme.css HTTP/1.1" 200 12107
INFO 2025-07-16 18:35:15,796 basehttp 11224 8868 "GET /static/js/main.js HTTP/1.1" 200 7698
WARNING 2025-07-16 18:35:15,907 basehttp 11224 18692 "GET /static/css/pages/home.css HTTP/1.1" 404 1914
WARNING 2025-07-16 18:35:15,908 basehttp 11224 10200 "GET /static/js/navigation/sidebar.js HTTP/1.1" 404 1932
WARNING 2025-07-16 18:35:15,917 basehttp 11224 18876 "GET /static/js/betting/betslip.js HTTP/1.1" 404 1923
WARNING 2025-07-16 18:35:15,920 basehttp 11224 21532 "GET /static/js/pages/home.js HTTP/1.1" 404 1908
WARNING 2025-07-16 18:35:18,108 basehttp 11224 8868 "GET /static/images/favicon.ico HTTP/1.1" 404 1914
INFO 2025-07-16 18:35:47,530 basehttp 11224 7412 "GET /sports/ HTTP/1.1" 200 11017
INFO 2025-07-16 18:35:47,643 basehttp 11224 10760 "GET /static/js/sports.js HTTP/1.1" 200 25761
INFO 2025-07-16 18:35:47,666 basehttp 11224 7412 "GET /static/css/sports.css HTTP/1.1" 200 15621
WARNING 2025-07-16 18:35:47,695 basehttp 11224 19328 "GET /static/js/navigation/sidebar.js HTTP/1.1" 404 1932
WARNING 2025-07-16 18:35:47,705 basehttp 11224 14288 "GET /static/js/betting/betslip.js HTTP/1.1" 404 1923
WARNING 2025-07-16 18:35:52,357 log 11224 7412 Not Found: /jackpots/
WARNING 2025-07-16 18:35:52,359 basehttp 11224 7412 "GET /jackpots/ HTTP/1.1" 404 3587
WARNING 2025-07-16 18:35:52,893 log 11224 7412 Not Found: /favicon.ico
WARNING 2025-07-16 18:35:52,903 basehttp 11224 7412 "GET /favicon.ico HTTP/1.1" 404 3593
WARNING 2025-07-16 18:35:57,383 log 11224 7412 Not Found: /shikisha/
WARNING 2025-07-16 18:35:57,388 basehttp 11224 7412 "GET /shikisha/ HTTP/1.1" 404 3587
INFO 2025-07-16 18:36:03,537 basehttp 11224 7412 "GET / HTTP/1.1" 200 16268
WARNING 2025-07-16 18:36:03,653 basehttp 11224 10760 "GET /static/js/betting/betslip.js HTTP/1.1" 404 1923
WARNING 2025-07-16 18:36:03,654 basehttp 11224 7412 "GET /static/css/pages/home.css HTTP/1.1" 404 1914
WARNING 2025-07-16 18:36:03,660 basehttp 11224 7032 "GET /static/js/navigation/sidebar.js HTTP/1.1" 404 1932
WARNING 2025-07-16 18:36:03,677 basehttp 11224 21600 "GET /static/js/pages/home.js HTTP/1.1" 404 1908
INFO 2025-07-16 18:38:37,675 basehttp 11224 324 "GET / HTTP/1.1" 200 16269
WARNING 2025-07-16 18:38:37,773 basehttp 11224 324 "GET /static/css/pages/home.css HTTP/1.1" 404 1914
WARNING 2025-07-16 18:38:37,777 basehttp 11224 22384 "GET /static/js/betting/betslip.js HTTP/1.1" 404 1923
WARNING 2025-07-16 18:38:37,788 basehttp 11224 14628 "GET /static/js/navigation/sidebar.js HTTP/1.1" 404 1932
WARNING 2025-07-16 18:38:37,790 basehttp 11224 9816 "GET /static/js/pages/home.js HTTP/1.1" 404 1908
INFO 2025-07-16 18:38:46,054 basehttp 11224 2388 "GET /accounts/login/ HTTP/1.1" 200 12486
WARNING 2025-07-16 18:38:46,192 basehttp 11224 7640 "GET /static/js/navigation/sidebar.js HTTP/1.1" 404 1932
WARNING 2025-07-16 18:38:46,192 basehttp 11224 2388 "GET /static/js/betting/betslip.js HTTP/1.1" 404 1923
INFO 2025-07-16 18:38:55,024 basehttp 11224 20092 "GET /accounts/register/ HTTP/1.1" 200 15126
WARNING 2025-07-16 18:38:55,137 basehttp 11224 20092 "GET /static/js/betting/betslip.js HTTP/1.1" 404 1923
WARNING 2025-07-16 18:38:55,137 basehttp 11224 19436 "GET /static/js/navigation/sidebar.js HTTP/1.1" 404 1932
INFO 2025-07-17 19:02:02,034 autoreload 3856 22140 Watching for file changes with StatReloader
INFO 2025-07-17 19:47:19,729 autoreload 14552 17940 Watching for file changes with StatReloader
INFO 2025-07-17 19:50:48,007 autoreload 15644 4044 Watching for file changes with StatReloader
INFO 2025-07-17 19:51:50,360 autoreload 15644 4044 C:\Users\<USER>\Desktop\projects\betzide\payments\api_views.py changed, reloading.
INFO 2025-07-17 19:51:50,450 autoreload 14552 17940 C:\Users\<USER>\Desktop\projects\betzide\payments\api_views.py changed, reloading.
INFO 2025-07-17 19:51:53,288 autoreload 14520 16432 Watching for file changes with StatReloader
INFO 2025-07-17 19:51:53,613 autoreload 20900 8512 Watching for file changes with StatReloader
INFO 2025-07-17 19:53:27,526 autoreload 20932 5040 Watching for file changes with StatReloader
INFO 2025-07-17 19:53:50,295 basehttp 14520 19504 "GET / HTTP/1.1" 200 19525
INFO 2025-07-17 19:53:52,782 basehttp 14520 5116 "GET /static/js/betting/betslip.js HTTP/1.1" 200 28025
INFO 2025-07-17 19:53:52,834 basehttp 14520 9900 "GET /static/css/pages/home.css HTTP/1.1" 200 7987
INFO 2025-07-17 19:53:52,846 basehttp 14520 4344 "GET /static/js/main.js HTTP/1.1" 200 7698
INFO 2025-07-17 19:53:52,853 basehttp 14520 5116 "GET /static/js/navigation/sidebar.js HTTP/1.1" 200 2540
INFO 2025-07-17 19:53:52,873 basehttp 14520 9900 "GET /static/js/pages/home.js HTTP/1.1" 200 8349
INFO 2025-07-17 19:53:52,915 basehttp 14520 15532 "GET /static/css/components/betslip.css HTTP/1.1" 200 9514
INFO 2025-07-17 19:53:52,922 basehttp 14520 3852 "GET /static/css/theme/betika-theme.css HTTP/1.1" 200 16115
INFO 2025-07-17 19:53:52,933 basehttp 14520 19504 "GET /static/css/base.css HTTP/1.1" 200 3517
WARNING 2025-07-17 19:53:53,481 basehttp 14520 19504 "GET /static/images/favicon.ico HTTP/1.1" 404 1914
INFO 2025-07-17 20:02:45,674 autoreload 14520 16432 C:\Users\<USER>\Desktop\projects\betzide\payments\services.py changed, reloading.
INFO 2025-07-17 20:02:47,794 autoreload 3792 11948 Watching for file changes with StatReloader
INFO 2025-07-17 20:04:46,350 autoreload 3792 11948 C:\Users\<USER>\Desktop\projects\betzide\payments\services.py changed, reloading.
INFO 2025-07-17 20:04:48,633 autoreload 20376 5288 Watching for file changes with StatReloader
INFO 2025-07-17 20:05:09,585 autoreload 20376 5288 C:\Users\<USER>\Desktop\projects\betzide\payments\api_views.py changed, reloading.
INFO 2025-07-17 20:05:11,596 autoreload 8680 18628 Watching for file changes with StatReloader
INFO 2025-07-17 20:05:24,056 autoreload 8680 18628 C:\Users\<USER>\Desktop\projects\betzide\betika_clone\settings.py changed, reloading.
INFO 2025-07-17 20:05:26,071 autoreload 17160 17980 Watching for file changes with StatReloader
INFO 2025-07-17 20:06:04,643 autoreload 17160 17980 C:\Users\<USER>\Desktop\projects\betzide\payments\services.py changed, reloading.
INFO 2025-07-17 20:06:06,770 autoreload 17700 1252 Watching for file changes with StatReloader
INFO 2025-07-17 20:06:51,379 basehttp 17700 760 "GET / HTTP/1.1" 200 19525
WARNING 2025-07-17 20:06:56,045 log 17700 760 Not Found: /sports/boxing/
WARNING 2025-07-17 20:06:56,060 basehttp 17700 760 "GET /sports/boxing/ HTTP/1.1" 404 2988
WARNING 2025-07-17 20:06:56,196 log 17700 760 Not Found: /favicon.ico
WARNING 2025-07-17 20:06:56,202 basehttp 17700 760 "GET /favicon.ico HTTP/1.1" 404 3593
INFO 2025-07-17 20:07:04,166 autoreload 17700 1252 C:\Users\<USER>\Desktop\projects\betzide\payments\services.py changed, reloading.
INFO 2025-07-17 20:07:06,166 autoreload 13684 20464 Watching for file changes with StatReloader
INFO 2025-07-17 20:08:12,286 autoreload 13684 20464 C:\Users\<USER>\Desktop\projects\betzide\payments\services.py changed, reloading.
INFO 2025-07-17 20:08:14,399 autoreload 6192 12768 Watching for file changes with StatReloader
INFO 2025-07-17 20:08:34,092 autoreload 6192 12768 C:\Users\<USER>\Desktop\projects\betzide\payments\api_views.py changed, reloading.
INFO 2025-07-17 20:08:37,922 autoreload 21512 20980 Watching for file changes with StatReloader
INFO 2025-07-17 20:09:11,419 autoreload 21512 20980 C:\Users\<USER>\Desktop\projects\betzide\payments\api_urls.py changed, reloading.
INFO 2025-07-17 20:09:13,486 autoreload 10280 7604 Watching for file changes with StatReloader
INFO 2025-07-17 20:09:54,660 autoreload 10280 7604 C:\Users\<USER>\Desktop\projects\betzide\betika_clone\settings.py changed, reloading.
INFO 2025-07-17 20:09:56,679 autoreload 16140 19904 Watching for file changes with StatReloader
INFO 2025-07-17 20:13:32,280 autoreload 16140 19904 C:\Users\<USER>\Desktop\projects\betzide\payments\services.py changed, reloading.
INFO 2025-07-17 20:13:34,416 autoreload 1352 12252 Watching for file changes with StatReloader
INFO 2025-07-17 20:14:12,376 autoreload 1352 12252 C:\Users\<USER>\Desktop\projects\betzide\payments\services.py changed, reloading.
INFO 2025-07-17 20:14:14,848 autoreload 14536 20056 Watching for file changes with StatReloader
INFO 2025-07-17 20:15:06,513 autoreload 14536 20056 C:\Users\<USER>\Desktop\projects\betzide\payments\api_views.py changed, reloading.
INFO 2025-07-17 20:15:08,657 autoreload 19680 22264 Watching for file changes with StatReloader
INFO 2025-07-17 20:15:21,312 autoreload 19680 22264 C:\Users\<USER>\Desktop\projects\betzide\payments\api_urls.py changed, reloading.
INFO 2025-07-17 20:15:23,424 autoreload 14364 15896 Watching for file changes with StatReloader
INFO 2025-07-17 20:15:37,225 autoreload 14364 15896 C:\Users\<USER>\Desktop\projects\betzide\betika_clone\settings.py changed, reloading.
INFO 2025-07-17 20:15:39,423 autoreload 16056 17872 Watching for file changes with StatReloader
INFO 2025-07-17 21:12:00,891 basehttp 16056 15964 "GET / HTTP/1.1" 200 19525
INFO 2025-07-17 21:12:00,994 basehttp 16056 17812 "GET /static/css/components/betslip.css HTTP/1.1" 304 0
INFO 2025-07-17 21:12:00,994 basehttp 16056 15964 "GET /static/css/theme/betika-theme.css HTTP/1.1" 304 0
INFO 2025-07-17 21:12:00,996 basehttp 16056 4492 "GET /static/js/betting/betslip.js HTTP/1.1" 304 0
INFO 2025-07-17 21:12:00,998 basehttp 16056 16340 "GET /static/css/pages/home.css HTTP/1.1" 304 0
INFO 2025-07-17 21:12:01,003 basehttp 16056 15196 "GET /static/js/navigation/sidebar.js HTTP/1.1" 304 0
INFO 2025-07-17 21:12:01,003 basehttp 16056 12392 "GET /static/js/pages/home.js HTTP/1.1" 304 0
INFO 2025-07-17 21:12:42,713 basehttp 16056 12392 "GET / HTTP/1.1" 200 19525
INFO 2025-07-17 22:18:43,401 autoreload 6632 13888 Watching for file changes with StatReloader
INFO 2025-07-17 22:23:13,654 basehttp 16056 16096 "GET / HTTP/1.1" 200 19525
INFO 2025-07-17 22:23:13,743 basehttp 16056 14852 "GET /static/css/pages/home.css HTTP/1.1" 304 0
INFO 2025-07-17 22:23:13,744 basehttp 16056 16096 "GET /static/css/theme/betika-theme.css HTTP/1.1" 304 0
INFO 2025-07-17 22:23:13,744 basehttp 16056 18900 "GET /static/css/components/betslip.css HTTP/1.1" 304 0
INFO 2025-07-17 22:23:13,749 basehttp 16056 9012 "GET /static/js/betting/betslip.js HTTP/1.1" 304 0
INFO 2025-07-17 22:23:13,751 basehttp 16056 20056 "GET /static/js/navigation/sidebar.js HTTP/1.1" 304 0
INFO 2025-07-17 22:23:13,753 basehttp 16056 12164 "GET /static/js/pages/home.js HTTP/1.1" 304 0
INFO 2025-07-17 22:23:15,067 autoreload 6632 13888 C:\Users\<USER>\Desktop\projects\betzide\live_betting\views.py changed, reloading.
INFO 2025-07-17 22:23:15,080 autoreload 16056 17872 C:\Users\<USER>\Desktop\projects\betzide\live_betting\views.py changed, reloading.
INFO 2025-07-17 22:23:17,208 autoreload 12136 6188 Watching for file changes with StatReloader
INFO 2025-07-17 22:23:17,731 autoreload 11708 17204 Watching for file changes with StatReloader
INFO 2025-07-17 22:23:45,689 autoreload 11708 17204 C:\Users\<USER>\Desktop\projects\betzide\live_betting\views.py changed, reloading.
INFO 2025-07-17 22:23:46,636 autoreload 12136 6188 C:\Users\<USER>\Desktop\projects\betzide\live_betting\views.py changed, reloading.
INFO 2025-07-17 22:23:55,914 autoreload 21320 18692 Watching for file changes with StatReloader
INFO 2025-07-17 22:23:55,922 autoreload 7352 9680 Watching for file changes with StatReloader
INFO 2025-07-17 22:24:24,198 autoreload 7352 9680 C:\Users\<USER>\Desktop\projects\betzide\live_betting\urls.py changed, reloading.
INFO 2025-07-17 22:24:24,198 autoreload 21320 18692 C:\Users\<USER>\Desktop\projects\betzide\live_betting\urls.py changed, reloading.
INFO 2025-07-17 22:24:25,285 autoreload 23000 23316 Watching for file changes with StatReloader
INFO 2025-07-17 22:24:25,293 autoreload 12336 5124 Watching for file changes with StatReloader
INFO 2025-07-18 17:01:55,218 autoreload 16168 20832 Watching for file changes with StatReloader
INFO 2025-07-18 17:06:40,718 autoreload 4508 14896 Watching for file changes with StatReloader
INFO 2025-07-18 17:07:11,076 autoreload 16168 20832 C:\Users\<USER>\Desktop\projects\betzide\admin_panel\services.py changed, reloading.
INFO 2025-07-18 17:07:11,772 autoreload 4508 14896 C:\Users\<USER>\Desktop\projects\betzide\admin_panel\services.py changed, reloading.
INFO 2025-07-18 17:07:14,544 autoreload 17156 5400 Watching for file changes with StatReloader
INFO 2025-07-18 17:07:14,826 autoreload 16372 9316 Watching for file changes with StatReloader
INFO 2025-07-18 17:07:28,144 autoreload 17156 5400 C:\Users\<USER>\Desktop\projects\betzide\admin_panel\services.py changed, reloading.
INFO 2025-07-18 17:07:28,424 autoreload 16372 9316 C:\Users\<USER>\Desktop\projects\betzide\admin_panel\services.py changed, reloading.
INFO 2025-07-18 17:07:30,308 autoreload 11868 14172 Watching for file changes with StatReloader
INFO 2025-07-18 17:07:30,463 autoreload 16404 16468 Watching for file changes with StatReloader
INFO 2025-07-18 17:07:40,471 autoreload 11868 14172 C:\Users\<USER>\Desktop\projects\betzide\admin_panel\services.py changed, reloading.
INFO 2025-07-18 17:07:40,546 autoreload 16404 16468 C:\Users\<USER>\Desktop\projects\betzide\admin_panel\services.py changed, reloading.
INFO 2025-07-18 17:07:42,784 autoreload 7880 18920 Watching for file changes with StatReloader
INFO 2025-07-18 17:07:42,818 autoreload 11676 9580 Watching for file changes with StatReloader
INFO 2025-07-18 17:07:58,171 autoreload 11676 9580 C:\Users\<USER>\Desktop\projects\betzide\admin_panel\services.py changed, reloading.
INFO 2025-07-18 17:07:58,241 autoreload 7880 18920 C:\Users\<USER>\Desktop\projects\betzide\admin_panel\services.py changed, reloading.
INFO 2025-07-18 17:08:00,307 autoreload 18936 16968 Watching for file changes with StatReloader
INFO 2025-07-18 17:13:19,434 autoreload 18936 16968 C:\Users\<USER>\Desktop\projects\betzide\admin_panel\models.py changed, reloading.
INFO 2025-07-18 17:13:19,486 autoreload 20080 7268 C:\Users\<USER>\Desktop\projects\betzide\admin_panel\models.py changed, reloading.
INFO 2025-07-18 17:13:21,597 autoreload 13304 12692 Watching for file changes with StatReloader
INFO 2025-07-18 17:13:21,676 autoreload 15188 6256 Watching for file changes with StatReloader
INFO 2025-07-18 17:15:10,147 basehttp 13304 11184 "GET /admin-panel/ HTTP/1.1" 302 0
INFO 2025-07-18 17:15:11,381 basehttp 13304 11184 "GET /admin/login/?next=/admin-panel/ HTTP/1.1" 200 4203
INFO 2025-07-18 17:15:12,203 basehttp 13304 17300 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2929
INFO 2025-07-18 17:15:12,209 basehttp 13304 5472 "GET /static/admin/css/login.css HTTP/1.1" 200 958
INFO 2025-07-18 17:15:12,231 basehttp 13304 13804 "GET /static/admin/css/responsive.css HTTP/1.1" 200 18533
INFO 2025-07-18 17:15:12,244 basehttp 13304 11184 "GET /static/admin/css/base.css HTTP/1.1" 200 21207
INFO 2025-07-18 17:15:12,266 basehttp 13304 1760 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2694
INFO 2025-07-18 17:15:12,510 basehttp 13304 11184 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
INFO 2025-07-18 17:15:12,942 basehttp 13304 23328 "GET /static/admin/js/theme.js HTTP/1.1" 200 1943
WARNING 2025-07-18 17:15:13,746 log 13304 23328 Not Found: /favicon.ico
WARNING 2025-07-18 17:15:13,787 basehttp 13304 23328 "GET /favicon.ico HTTP/1.1" 404 3921
INFO 2025-07-18 17:15:35,568 basehttp 13304 23328 "POST /admin/login/?next=/admin-panel/ HTTP/1.1" 200 4367
INFO 2025-07-18 17:15:46,989 basehttp 13304 23328 "GET /admin-panel/ HTTP/1.1" 302 0
INFO 2025-07-18 17:15:47,007 basehttp 13304 23328 "GET /admin/login/?next=/admin-panel/ HTTP/1.1" 200 4203
INFO 2025-07-18 17:16:29,691 basehttp 13304 23328 "GET /admin-panel/ HTTP/1.1" 302 0
INFO 2025-07-18 17:16:29,711 basehttp 13304 23328 "GET /admin/login/?next=/admin-panel/ HTTP/1.1" 200 4203
INFO 2025-07-18 17:22:00,646 basehttp 13304 8700 "GET /admin-panel/ HTTP/1.1" 302 0
INFO 2025-07-18 17:22:00,689 basehttp 13304 8700 "GET /admin/login/?next=/admin-panel/ HTTP/1.1" 200 4203
INFO 2025-07-18 17:22:44,891 basehttp 13304 8700 "POST /admin/login/?next=/admin-panel/ HTTP/1.1" 200 4375
INFO 2025-07-18 17:23:30,545 basehttp 13304 8700 "POST /admin/login/?next=/admin-panel/ HTTP/1.1" 302 0
ERROR 2025-07-18 17:23:31,274 log 13304 8700 Internal Server Error: /admin-panel/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\admin_panel\views.py", line 73, in admin_dashboard
    return render(request, 'admin_panel/dashboard.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: admin_panel/dashboard.html

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\admin_panel\views.py", line 77, in admin_dashboard
    return render(request, 'admin_panel/dashboard.html', {'error': str(e)})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: admin_panel/dashboard.html
ERROR 2025-07-18 17:23:31,291 basehttp 13304 8700 "GET /admin-panel/ HTTP/1.1" 500 124830
INFO 2025-07-18 17:33:49,317 basehttp 13304 20840 "GET /admin-panel/ HTTP/1.1" 200 16929
INFO 2025-07-18 17:33:49,924 basehttp 13304 20840 "GET /static/css/base.css HTTP/1.1" 304 0
INFO 2025-07-18 17:33:49,927 basehttp 13304 13244 "GET /static/css/theme/betika-theme.css HTTP/1.1" 304 0
INFO 2025-07-18 17:34:18,068 basehttp 13304 13244 "GET /admin-panel/users/ HTTP/1.1" 200 23469
ERROR 2025-07-18 17:34:24,632 log 13304 13244 Internal Server Error: /admin-panel/analytics/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\admin_panel\views.py", line 210, in betting_analytics
    return render(request, 'admin_panel/betting_analytics.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
                    ^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 484, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 600, in compile_filter
    return FilterExpression(token, self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'replace'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\admin_panel\views.py", line 214, in betting_analytics
    return render(request, 'admin_panel/betting_analytics.html', {'error': str(e)})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
                    ^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 484, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 600, in compile_filter
    return FilterExpression(token, self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'replace'
ERROR 2025-07-18 17:34:24,693 basehttp 13304 13244 "GET /admin-panel/analytics/ HTTP/1.1" **********
INFO 2025-07-18 17:36:47,624 basehttp 13304 13244 "GET /admin-panel/analytics/ HTTP/1.1" 200 15498
INFO 2025-07-18 17:36:55,135 basehttp 13304 13244 "GET /admin-panel/users/ HTTP/1.1" 200 23469
INFO 2025-07-18 17:37:00,488 basehttp 13304 13244 "GET /admin-panel/compliance/ HTTP/1.1" 200 18511
INFO 2025-07-18 17:37:01,877 basehttp 13304 13244 "GET /admin-panel/compliance/ HTTP/1.1" 200 18511
INFO 2025-07-18 17:37:07,582 basehttp 13304 13244 "GET /admin-panel/monitoring/ HTTP/1.1" 200 16269
INFO 2025-07-18 17:37:12,837 basehttp 13304 13244 "GET /admin-panel/monitoring/?export=metrics&format=csv HTTP/1.1" 200 16269
INFO 2025-07-18 17:37:14,907 basehttp 13304 13244 "GET /admin-panel/monitoring/?export=metrics&format=csv HTTP/1.1" 200 16269
INFO 2025-07-18 17:37:15,972 basehttp 13304 13244 "GET /admin-panel/monitoring/?export=metrics&format=csv HTTP/1.1" 200 16269
INFO 2025-07-18 17:37:17,682 basehttp 13304 13244 "GET /admin-panel/audit/?action_type=system_event HTTP/1.1" 200 17874
INFO 2025-07-18 17:37:23,458 basehttp 13304 13244 "GET /admin-panel/settings/ HTTP/1.1" 200 20799
INFO 2025-07-18 17:37:33,286 basehttp 13304 13244 "GET /admin/ HTTP/1.1" 200 20288
INFO 2025-07-18 17:37:33,384 basehttp 13304 13244 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
INFO 2025-07-18 17:37:33,432 basehttp 13304 20840 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
INFO 2025-07-18 17:37:33,442 basehttp 13304 13244 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
INFO 2025-07-18 17:37:59,363 basehttp 13304 13244 "GET /admin-panel/ HTTP/1.1" 200 16640
INFO 2025-07-18 17:38:00,530 basehttp 13304 13244 "GET /admin-panel/users/ HTTP/1.1" 200 23469
INFO 2025-07-18 17:38:02,919 basehttp 13304 13244 "GET /admin-panel/analytics/ HTTP/1.1" 200 15192
INFO 2025-07-18 17:38:04,856 basehttp 13304 13244 "GET /admin-panel/compliance/ HTTP/1.1" 200 18511
INFO 2025-07-18 17:38:06,618 basehttp 13304 13244 "GET /admin-panel/monitoring/ HTTP/1.1" 200 16269
INFO 2025-07-18 17:38:07,886 basehttp 13304 13244 "GET /admin-panel/audit/ HTTP/1.1" 200 18987
WARNING 2025-07-18 17:38:17,593 log 13304 13244 Method Not Allowed (GET): /accounts/logout/
WARNING 2025-07-18 17:38:17,601 basehttp 13304 13244 "GET /accounts/logout/ HTTP/1.1" 405 0
INFO 2025-07-18 17:39:08,199 basehttp 13304 13244 "GET /admin-panel/monitoring/ HTTP/1.1" 200 16269
INFO 2025-07-18 17:39:13,193 basehttp 13304 13244 "GET /admin-panel/monitoring/?export=metrics&format=csv HTTP/1.1" 200 16269
INFO 2025-07-18 17:39:15,192 basehttp 13304 13244 "GET /admin-panel/monitoring/?export=metrics&format=csv HTTP/1.1" 200 16269
INFO 2025-07-18 17:41:09,219 basehttp 13304 13244 "GET /admin-panel/monitoring/ HTTP/1.1" 200 16269
INFO 2025-07-18 17:41:14,191 basehttp 13304 13244 "GET /admin-panel/monitoring/?export=metrics&format=csv HTTP/1.1" 200 16269
INFO 2025-07-18 17:41:16,514 basehttp 13304 13244 "GET /admin-panel/monitoring/?export=metrics&format=csv HTTP/1.1" 200 16269
INFO 2025-07-18 17:42:02,260 autoreload 15188 6256 C:\Users\<USER>\Desktop\projects\betzide\accounts\views.py changed, reloading.
INFO 2025-07-18 17:42:02,644 autoreload 13304 12692 C:\Users\<USER>\Desktop\projects\betzide\accounts\views.py changed, reloading.
INFO 2025-07-18 17:42:07,734 autoreload 25176 17196 Watching for file changes with StatReloader
INFO 2025-07-18 17:42:07,734 autoreload 24376 19512 Watching for file changes with StatReloader
INFO 2025-07-18 17:43:06,557 basehttp 25176 9772 "GET /admin-panel/ HTTP/1.1" 200 17080
INFO 2025-07-18 17:43:11,958 basehttp 25176 9772 "GET /accounts/logout/ HTTP/1.1" 302 0
INFO 2025-07-18 17:43:12,045 basehttp 25176 9772 "GET / HTTP/1.1" 200 19944
INFO 2025-07-18 17:43:12,189 basehttp 25176 24768 "GET /static/css/pages/home.css HTTP/1.1" 304 0
INFO 2025-07-18 17:43:12,195 basehttp 25176 9772 "GET /static/css/components/betslip.css HTTP/1.1" 304 0
INFO 2025-07-18 17:43:12,211 basehttp 25176 18288 "GET /static/js/navigation/sidebar.js HTTP/1.1" 304 0
INFO 2025-07-18 17:43:12,247 basehttp 25176 548 "GET /static/js/pages/home.js HTTP/1.1" 304 0
INFO 2025-07-18 17:43:12,287 basehttp 25176 19332 "GET /static/js/betting/betslip.js HTTP/1.1" 304 0
INFO 2025-07-18 17:43:12,306 basehttp 25176 3932 "GET /static/js/main.js HTTP/1.1" 304 0
WARNING 2025-07-18 17:43:13,421 basehttp 25176 3932 "GET /static/images/favicon.ico HTTP/1.1" 404 1914
INFO 2025-07-18 17:43:14,289 basehttp 25176 19332 "GET /admin-panel/monitoring/ HTTP/1.1" 302 0
INFO 2025-07-18 17:43:14,614 basehttp 25176 19332 "GET /admin/login/?next=/admin-panel/monitoring/ HTTP/1.1" 200 4225
INFO 2025-07-18 17:43:15,247 basehttp 25176 19332 "GET /admin-panel/monitoring/?export=metrics&format=csv HTTP/1.1" 302 0
INFO 2025-07-18 17:43:15,274 basehttp 25176 19332 "GET /admin/login/?next=/admin-panel/monitoring/%3Fexport%3Dmetrics%26format%3Dcsv HTTP/1.1" 200 4289
INFO 2025-07-18 17:43:20,130 basehttp 25176 19332 "GET /admin-panel/monitoring/?export=metrics&format=csv HTTP/1.1" 302 0
INFO 2025-07-18 17:43:20,160 basehttp 25176 19332 "GET /admin/login/?next=/admin-panel/monitoring/%3Fexport%3Dmetrics%26format%3Dcsv HTTP/1.1" 200 4289
INFO 2025-07-18 17:48:08,182 basehttp 25176 19332 "GET /admin-panel/ HTTP/1.1" 302 0
INFO 2025-07-18 17:48:08,419 basehttp 25176 19332 "GET /admin/login/?next=/admin-panel/ HTTP/1.1" 200 4203
INFO 2025-07-18 17:48:31,379 autoreload 25176 17196 C:\Users\<USER>\Desktop\projects\betzide\betting\urls.py changed, reloading.
INFO 2025-07-18 17:48:32,659 autoreload 24376 19512 C:\Users\<USER>\Desktop\projects\betzide\betting\urls.py changed, reloading.
INFO 2025-07-18 17:48:39,578 autoreload 14548 24716 Watching for file changes with StatReloader
INFO 2025-07-18 17:48:39,676 autoreload 13636 11884 Watching for file changes with StatReloader
INFO 2025-07-18 17:49:17,444 autoreload 13636 11884 C:\Users\<USER>\Desktop\projects\betzide\betting\views.py changed, reloading.
INFO 2025-07-18 17:49:17,473 autoreload 14548 24716 C:\Users\<USER>\Desktop\projects\betzide\betting\views.py changed, reloading.
INFO 2025-07-18 17:49:22,275 autoreload 15784 6208 Watching for file changes with StatReloader
INFO 2025-07-18 17:49:22,686 autoreload 15408 11156 Watching for file changes with StatReloader
INFO 2025-07-18 17:49:36,443 autoreload 15784 6208 C:\Users\<USER>\Desktop\projects\betzide\betting\views.py changed, reloading.
INFO 2025-07-18 17:49:36,862 autoreload 15408 11156 C:\Users\<USER>\Desktop\projects\betzide\betting\views.py changed, reloading.
INFO 2025-07-18 17:49:42,097 autoreload 25176 16052 Watching for file changes with StatReloader
INFO 2025-07-18 17:49:42,825 autoreload 19512 24140 Watching for file changes with StatReloader
INFO 2025-07-18 18:00:12,306 basehttp 25176 10856 "GET /accounts/login/ HTTP/1.1" 200 14188
INFO 2025-07-18 18:00:15,763 basehttp 25176 10856 "GET /accounts/register/ HTTP/1.1" 200 16828
INFO 2025-07-18 18:00:28,291 basehttp 25176 10856 "GET /sports/ HTTP/1.1" 200 16964
INFO 2025-07-18 18:00:28,525 basehttp 25176 10856 "GET /static/css/sports.css HTTP/1.1" 200 19146
INFO 2025-07-18 18:00:28,549 basehttp 25176 25340 "GET /static/js/sports.js HTTP/1.1" 200 25761
WARNING 2025-07-18 18:00:32,831 log 25176 25340 Not Found: /sports/search/
WARNING 2025-07-18 18:00:32,831 basehttp 25176 25340 "GET /sports/search/?status=live HTTP/1.1" 404 3000
WARNING 2025-07-18 18:00:49,698 log 25176 25340 Not Found: /jackpots/
WARNING 2025-07-18 18:00:49,700 basehttp 25176 25340 "GET /jackpots/ HTTP/1.1" 404 3915
WARNING 2025-07-18 18:00:52,632 log 25176 25340 Not Found: /shikisha/
WARNING 2025-07-18 18:00:52,634 basehttp 25176 25340 "GET /shikisha/ HTTP/1.1" 404 3915
WARNING 2025-07-18 18:00:56,152 log 25176 25340 Not Found: /aviator/
WARNING 2025-07-18 18:00:56,154 basehttp 25176 25340 "GET /aviator/ HTTP/1.1" 404 3912
WARNING 2025-07-18 18:00:59,286 log 25176 25340 Not Found: /ligi-bora/
WARNING 2025-07-18 18:00:59,286 basehttp 25176 25340 "GET /ligi-bora/ HTTP/1.1" 404 3918
INFO 2025-07-18 18:01:05,592 basehttp 25176 25340 "GET / HTTP/1.1" 200 19525
WARNING 2025-07-18 18:01:21,618 log 25176 25340 Not Found: /sports/basketball/
WARNING 2025-07-18 18:01:21,620 basehttp 25176 25340 "GET /sports/basketball/ HTTP/1.1" 404 3000
INFO 2025-07-18 18:16:26,813 autoreload 25176 16052 C:\Users\<USER>\Desktop\projects\betzide\admin_panel\views.py changed, reloading.
INFO 2025-07-18 18:16:27,128 autoreload 19512 24140 C:\Users\<USER>\Desktop\projects\betzide\admin_panel\views.py changed, reloading.
INFO 2025-07-18 18:16:29,902 autoreload 24688 25128 Watching for file changes with StatReloader
INFO 2025-07-18 18:16:29,904 autoreload 22808 18612 Watching for file changes with StatReloader
INFO 2025-07-18 18:56:00,488 autoreload 24688 25128 C:\Users\<USER>\Desktop\projects\betzide\admin_panel\signals.py changed, reloading.
INFO 2025-07-18 18:56:00,498 autoreload 22808 18612 C:\Users\<USER>\Desktop\projects\betzide\admin_panel\signals.py changed, reloading.
INFO 2025-07-18 18:56:03,020 autoreload 25320 15388 Watching for file changes with StatReloader
INFO 2025-07-18 18:56:03,026 autoreload 12200 13644 Watching for file changes with StatReloader
INFO 2025-07-18 19:07:41,207 autoreload 25320 15388 C:\Users\<USER>\Desktop\projects\betzide\accounts\models.py changed, reloading.
INFO 2025-07-18 19:07:41,213 autoreload 12200 13644 C:\Users\<USER>\Desktop\projects\betzide\accounts\models.py changed, reloading.
INFO 2025-07-18 19:07:45,146 autoreload 7132 25492 Watching for file changes with StatReloader
INFO 2025-07-18 19:07:45,147 autoreload 12380 16240 Watching for file changes with StatReloader
INFO 2025-07-18 19:12:14,116 autoreload 7132 25492 C:\Users\<USER>\Desktop\projects\betzide\admin_panel\signals.py changed, reloading.
INFO 2025-07-18 19:12:14,154 autoreload 12380 16240 C:\Users\<USER>\Desktop\projects\betzide\admin_panel\signals.py changed, reloading.
INFO 2025-07-18 19:12:15,536 autoreload 4352 24180 Watching for file changes with StatReloader

INFO 2025-07-18 19:14:18,392 autoreload 17380 24336 C:\Users\<USER>\Desktop\projects\betzide\admin_panel\signals.py changed, reloading.
INFO 2025-07-18 19:14:18,409 autoreload 4352 24180 C:\Users\<USER>\Desktop\projects\betzide\admin_panel\signals.py changed, reloading.
INFO 2025-07-18 19:14:19,970 autoreload 18772 20700 Watching for file changes with StatReloader
INFO 2025-07-18 19:14:19,972 autoreload 23416 8176 Watching for file changes with StatReloader
INFO 2025-07-18 19:27:10,023 basehttp 18772 1504 "GET /admin HTTP/1.1" 301 0
INFO 2025-07-18 19:27:10,106 basehttp 18772 16024 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-07-18 19:27:10,285 basehttp 18772 16024 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4191
WARNING 2025-07-21 13:11:59,971 log 21940 14244 Forbidden: /api/v1/sports/odds/update/
WARNING 2025-07-21 13:11:59,989 log 21940 14244 Forbidden: /api/v1/sports/odds/update/
WARNING 2025-07-21 13:14:20,327 log 19908 1356 Forbidden: /api/v1/sports/odds/update/
WARNING 2025-07-21 13:14:20,344 log 19908 1356 Forbidden: /api/v1/sports/odds/update/
INFO 2025-07-21 19:04:29,457 autoreload 1456 18948 Watching for file changes with StatReloader
INFO 2025-07-21 19:05:00,401 autoreload 17172 11004 Watching for file changes with StatReloader
INFO 2025-07-21 19:06:27,612 autoreload 19032 4548 Watching for file changes with StatReloader
ERROR 2025-07-21 19:06:39,574 log 19032 1456 Internal Server Error: /
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\middleware.py", line 55, in process_request
    if self._should_skip_rate_limiting(request):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\middleware.py", line 94, in _should_skip_rate_limiting
    if request.user.is_authenticated and request.user.is_superuser:
       ^^^^^^^^^^^^
AttributeError: 'WSGIRequest' object has no attribute 'user'
ERROR 2025-07-21 19:06:39,580 basehttp 19032 1456 "GET / HTTP/1.1" 500 93743
ERROR 2025-07-21 19:06:40,052 log 19032 22376 Internal Server Error: /favicon.ico
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\middleware.py", line 55, in process_request
    if self._should_skip_rate_limiting(request):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\middleware.py", line 94, in _should_skip_rate_limiting
    if request.user.is_authenticated and request.user.is_superuser:
       ^^^^^^^^^^^^
AttributeError: 'WSGIRequest' object has no attribute 'user'
ERROR 2025-07-21 19:06:40,058 basehttp 19032 22376 "GET /favicon.ico HTTP/1.1" 500 93564
INFO 2025-07-22 11:07:59,729 autoreload 1172 22852 Watching for file changes with StatReloader
ERROR 2025-07-22 11:40:27,568 log 1612 15364 Internal Server Error: /api/v1/sports/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\middleware.py", line 55, in process_request
    if self._should_skip_rate_limiting(request):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\middleware.py", line 94, in _should_skip_rate_limiting
    if request.user.is_authenticated and request.user.is_superuser:
       ^^^^^^^^^^^^
AttributeError: 'WSGIRequest' object has no attribute 'user'
ERROR 2025-07-22 11:40:27,679 log 1612 15364 Internal Server Error: /api/v1/sports/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\middleware.py", line 55, in process_request
    if self._should_skip_rate_limiting(request):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\middleware.py", line 94, in _should_skip_rate_limiting
    if request.user.is_authenticated and request.user.is_superuser:
       ^^^^^^^^^^^^
AttributeError: 'WSGIRequest' object has no attribute 'user'
ERROR 2025-07-22 11:40:27,744 log 1612 15364 Internal Server Error: /api/v1/sports/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\middleware.py", line 55, in process_request
    if self._should_skip_rate_limiting(request):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\middleware.py", line 94, in _should_skip_rate_limiting
    if request.user.is_authenticated and request.user.is_superuser:
       ^^^^^^^^^^^^
AttributeError: 'WSGIRequest' object has no attribute 'user'
ERROR 2025-07-22 11:40:27,788 log 1612 15364 Internal Server Error: /api/v1/sports/events/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\middleware.py", line 55, in process_request
    if self._should_skip_rate_limiting(request):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\middleware.py", line 94, in _should_skip_rate_limiting
    if request.user.is_authenticated and request.user.is_superuser:
       ^^^^^^^^^^^^
AttributeError: 'WSGIRequest' object has no attribute 'user'
ERROR 2025-07-22 11:40:27,838 log 1612 15364 Internal Server Error: /api/v1/sports/events/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\middleware.py", line 55, in process_request
    if self._should_skip_rate_limiting(request):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\middleware.py", line 94, in _should_skip_rate_limiting
    if request.user.is_authenticated and request.user.is_superuser:
       ^^^^^^^^^^^^
AttributeError: 'WSGIRequest' object has no attribute 'user'
ERROR 2025-07-22 11:40:27,884 log 1612 15364 Internal Server Error: /api/v1/sports/events/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\middleware.py", line 55, in process_request
    if self._should_skip_rate_limiting(request):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\middleware.py", line 94, in _should_skip_rate_limiting
    if request.user.is_authenticated and request.user.is_superuser:
       ^^^^^^^^^^^^
AttributeError: 'WSGIRequest' object has no attribute 'user'
ERROR 2025-07-22 11:43:12,174 log 18852 11780 Internal Server Error: /api/v1/sports/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\middleware.py", line 59, in process_request
    user_id = request.user.id if request.user.is_authenticated else None
                                 ^^^^^^^^^^^^
AttributeError: 'WSGIRequest' object has no attribute 'user'
ERROR 2025-07-22 11:43:12,238 log 18852 11780 Internal Server Error: /api/v1/sports/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\middleware.py", line 59, in process_request
    user_id = request.user.id if request.user.is_authenticated else None
                                 ^^^^^^^^^^^^
AttributeError: 'WSGIRequest' object has no attribute 'user'
ERROR 2025-07-22 11:43:12,293 log 18852 11780 Internal Server Error: /api/v1/sports/events/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\middleware.py", line 59, in process_request
    user_id = request.user.id if request.user.is_authenticated else None
                                 ^^^^^^^^^^^^
AttributeError: 'WSGIRequest' object has no attribute 'user'
ERROR 2025-07-22 11:43:12,346 log 18852 11780 Internal Server Error: /api/v1/sports/events/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\middleware.py", line 59, in process_request
    user_id = request.user.id if request.user.is_authenticated else None
                                 ^^^^^^^^^^^^
AttributeError: 'WSGIRequest' object has no attribute 'user'
ERROR 2025-07-22 11:45:46,152 exception 9176 20020 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\http\request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-22 11:45:46,387 log 9176 20020 Bad Request: /api/v1/sports/
ERROR 2025-07-22 11:45:46,387 exception 9176 20020 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\http\request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-22 11:45:46,442 log 9176 20020 Bad Request: /api/v1/sports/
ERROR 2025-07-22 11:45:46,447 exception 9176 20020 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\http\request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-22 11:45:46,513 log 9176 20020 Bad Request: /api/v1/sports/events/
ERROR 2025-07-22 11:45:46,515 exception 9176 20020 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\http\request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-22 11:45:46,587 log 9176 20020 Bad Request: /api/v1/sports/events/
WARNING 2025-07-22 11:48:05,496 log 24364 20336 Not Found: /api/v1/sports/events/
WARNING 2025-07-22 11:48:05,501 log 24364 20336 Not Found: /api/v1/sports/events/
INFO 2025-07-22 11:50:48,047 autoreload 18804 12504 Watching for file changes with StatReloader
ERROR 2025-07-22 11:50:55,995 log 18804 4332 Internal Server Error: /
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'compress'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader_tags.py", line 132, in render
    compiled_parent = self.get_parent(context)
                      ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader_tags.py", line 129, in get_parent
    return self.find_template(parent, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader_tags.py", line 107, in find_template
    template, origin = context.template.engine.find_template(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 568, in invalid_block_tag
    raise self.error(
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 13: 'compress'. Did you forget to register or load this tag?
ERROR 2025-07-22 11:50:56,006 basehttp 18804 4332 "GET / HTTP/1.1" **********
WARNING 2025-07-22 11:50:59,235 log 18804 20452 Not Found: /favicon.ico
WARNING 2025-07-22 11:50:59,235 basehttp 18804 20452 "GET /favicon.ico HTTP/1.1" 404 4031
INFO 2025-07-22 11:52:45,917 autoreload 5908 356 Watching for file changes with StatReloader
ERROR 2025-07-22 11:52:53,109 log 18804 23184 Internal Server Error: /
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\compressor\base.py", line 325, in precompile
    mod = import_module(mod_name)
          ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\importlib\__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_libsass'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\compressor\templatetags\compress.py", line 160, in render
    return self.render_compressed(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\compressor\templatetags\compress.py", line 131, in render_compressed
    rendered_output = compressor.output(mode, forced=forced, basename=file_basename)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\compressor\css.py", line 55, in output
    ret.append(subnode.output(*args, **kwargs))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\compressor\css.py", line 57, in output
    return super().output(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\compressor\base.py", line 367, in output
    output = "\n".join(self.filter_input(forced))
                       ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\compressor\base.py", line 294, in filter_input
    for hunk in self.hunks(forced):
                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\compressor\base.py", line 269, in hunks
    precompiled, value = self.precompile(value, **options)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\compressor\base.py", line 335, in precompile
    return True, filter.input(**kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\compressor\filters\base.py", line 256, in input
    return super().input(**kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\compressor\filters\base.py", line 224, in input
    raise FilterError(err)
compressor.exceptions.FilterError: 'django_libsass.SassCompiler' is not recognized as an internal or external command,

operable program or batch file.


ERROR 2025-07-22 11:52:53,119 basehttp 18804 23184 "GET / HTTP/1.1" 500 223110
INFO 2025-07-22 11:53:54,364 autoreload 5908 356 C:\Users\<USER>\Desktop\projects\betzide\betika_clone\settings.py changed, reloading.
INFO 2025-07-22 11:53:54,478 autoreload 18804 12504 C:\Users\<USER>\Desktop\projects\betzide\betika_clone\settings.py changed, reloading.
INFO 2025-07-22 11:53:56,674 autoreload 2268 21416 Watching for file changes with StatReloader
INFO 2025-07-22 11:53:56,709 autoreload 14440 2404 Watching for file changes with StatReloader
INFO 2025-07-22 11:54:07,767 basehttp 2268 20580 "GET / HTTP/1.1" 200 20153
INFO 2025-07-22 11:54:07,952 basehttp 2268 6256 "GET /static/css/base.css HTTP/1.1" 304 0
INFO 2025-07-22 11:54:08,206 basehttp 2268 2844 "GET /static/css/responsive.css HTTP/1.1" 200 8994
INFO 2025-07-22 11:54:08,216 basehttp 2268 6256 "GET /static/js/performance/lazy-loading.js HTTP/1.1" 200 8581
INFO 2025-07-22 11:54:08,218 basehttp 2268 20580 "GET /static/css/components/betslip.css HTTP/1.1" 200 9514
INFO 2025-07-22 11:54:08,218 basehttp 2268 22160 "GET /static/css/pages/home.css HTTP/1.1" 200 7987
INFO 2025-07-22 11:54:08,257 basehttp 2268 4896 "GET /static/js/performance/cache-manager.js HTTP/1.1" 200 9855
INFO 2025-07-22 11:54:08,263 basehttp 2268 2844 "GET /static/js/mobile/touch-interface.js HTTP/1.1" 200 12025
INFO 2025-07-22 11:54:08,272 basehttp 2268 20580 "GET /static/js/main.js HTTP/1.1" 304 0
INFO 2025-07-22 11:54:08,427 basehttp 2268 15780 "GET /static/css/theme/betika-theme.css HTTP/1.1" 200 16115
INFO 2025-07-22 11:54:08,433 basehttp 2268 6256 "GET /static/js/betting/betslip.js HTTP/1.1" 200 28025
INFO 2025-07-22 11:54:08,453 basehttp 2268 22160 "GET /static/js/navigation/sidebar.js HTTP/1.1" 200 2540
INFO 2025-07-22 11:54:08,494 basehttp 2268 20580 "GET /static/js/pages/home.js HTTP/1.1" 200 8349
INFO 2025-07-22 11:54:08,697 basehttp 2268 20580 "GET /api/v1/sports/ HTTP/1.1" 200 23
WARNING 2025-07-22 11:54:08,903 log 2268 20580 Not Found: /api/v1/sports/events/
WARNING 2025-07-22 11:54:08,919 basehttp 2268 6256 "GET /static/images/favicon.ico HTTP/1.1" 404 1914
WARNING 2025-07-22 11:54:08,933 basehttp 2268 20580 "GET /api/v1/sports/events/?status=live HTTP/1.1" 404 23
INFO 2025-07-22 11:54:21,825 basehttp 2268 20580 "GET /sports/ HTTP/1.1" 200 17592
INFO 2025-07-22 11:54:21,897 basehttp 2268 20580 "GET /static/css/sports.css HTTP/1.1" 200 19146
INFO 2025-07-22 11:54:21,911 basehttp 2268 15780 "GET /static/js/sports.js HTTP/1.1" 304 0
WARNING 2025-07-22 11:54:23,660 log 2268 15780 Not Found: /jackpots/
WARNING 2025-07-22 11:54:23,662 basehttp 2268 15780 "GET /jackpots/ HTTP/1.1" 404 4025
WARNING 2025-07-22 11:54:29,044 log 2268 15780 Not Found: /shikisha/
WARNING 2025-07-22 11:54:29,047 basehttp 2268 15780 "GET /shikisha/ HTTP/1.1" 404 4025
WARNING 2025-07-22 11:54:33,784 log 2268 15780 Not Found: /promotions/
WARNING 2025-07-22 11:54:33,786 basehttp 2268 15780 "GET /promotions/ HTTP/1.1" 404 4031
INFO 2025-07-22 11:54:44,412 basehttp 2268 15780 "GET / HTTP/1.1" 200 20153
INFO 2025-07-22 12:26:16,535 autoreload 14440 2404 C:\Users\<USER>\Desktop\projects\betzide\betika_clone\urls.py changed, reloading.
INFO 2025-07-22 12:26:16,702 autoreload 2268 21416 C:\Users\<USER>\Desktop\projects\betzide\betika_clone\urls.py changed, reloading.
INFO 2025-07-22 12:26:20,743 autoreload 9656 3044 Watching for file changes with StatReloader
INFO 2025-07-22 12:26:20,819 autoreload 6668 2280 Watching for file changes with StatReloader
INFO 2025-07-22 12:26:41,094 autoreload 9656 3044 C:\Users\<USER>\Desktop\projects\betzide\betika_clone\urls.py changed, reloading.
INFO 2025-07-22 12:26:41,153 autoreload 6668 2280 C:\Users\<USER>\Desktop\projects\betzide\betika_clone\urls.py changed, reloading.
INFO 2025-07-22 12:26:44,358 autoreload 24276 17968 Watching for file changes with StatReloader
INFO 2025-07-22 12:26:44,360 autoreload 10368 23964 Watching for file changes with StatReloader
INFO 2025-07-22 12:43:00,581 autoreload 23500 12360 Watching for file changes with StatReloader
ERROR 2025-07-22 12:43:08,335 log 24276 16212 Internal Server Error: /
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\views.py", line 105, in home_view
    return render(request, 'home.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\defaulttags.py", line 194, in render
    len_values = len(values)
                 ^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\db\models\query.py", line 380, in __len__
    self._fetch_all()
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\db\models\query.py", line 1883, in _fetch_all
    self._prefetch_related_objects()
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\db\models\query.py", line 1273, in _prefetch_related_objects
    prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\db\models\query.py", line 2296, in prefetch_related_objects
    raise AttributeError(
AttributeError: Cannot find 'market_set' on Event object, 'market_set' is an invalid parameter to prefetch_related()
ERROR 2025-07-22 12:43:08,373 basehttp 24276 16212 "GET / HTTP/1.1" 500 183887
WARNING 2025-07-22 12:43:09,306 log 24276 16212 Not Found: /favicon.ico
WARNING 2025-07-22 12:43:09,308 basehttp 24276 16212 "GET /favicon.ico HTTP/1.1" 404 4031
ERROR 2025-07-22 12:43:35,254 log 24276 16212 Internal Server Error: /
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\views.py", line 105, in home_view
    return render(request, 'home.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\defaulttags.py", line 194, in render
    len_values = len(values)
                 ^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\db\models\query.py", line 380, in __len__
    self._fetch_all()
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\db\models\query.py", line 1883, in _fetch_all
    self._prefetch_related_objects()
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\db\models\query.py", line 1273, in _prefetch_related_objects
    prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\db\models\query.py", line 2296, in prefetch_related_objects
    raise AttributeError(
AttributeError: Cannot find 'market_set' on Event object, 'market_set' is an invalid parameter to prefetch_related()
ERROR 2025-07-22 12:43:35,256 basehttp 24276 16212 "GET / HTTP/1.1" 500 184024
ERROR 2025-07-22 12:43:37,296 log 24276 16212 Internal Server Error: /
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\views.py", line 105, in home_view
    return render(request, 'home.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\defaulttags.py", line 194, in render
    len_values = len(values)
                 ^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\db\models\query.py", line 380, in __len__
    self._fetch_all()
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\db\models\query.py", line 1883, in _fetch_all
    self._prefetch_related_objects()
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\db\models\query.py", line 1273, in _prefetch_related_objects
    prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\db\models\query.py", line 2296, in prefetch_related_objects
    raise AttributeError(
AttributeError: Cannot find 'market_set' on Event object, 'market_set' is an invalid parameter to prefetch_related()
ERROR 2025-07-22 12:43:37,296 basehttp 24276 16212 "GET / HTTP/1.1" 500 184024
INFO 2025-07-22 12:44:10,629 autoreload 10508 4604 Watching for file changes with StatReloader
INFO 2025-07-22 12:44:40,675 autoreload 24276 17968 C:\Users\<USER>\Desktop\projects\betzide\core\views.py changed, reloading.
INFO 2025-07-22 12:44:40,684 autoreload 23500 12360 C:\Users\<USER>\Desktop\projects\betzide\core\views.py changed, reloading.
INFO 2025-07-22 12:44:40,761 autoreload 10508 4604 C:\Users\<USER>\Desktop\projects\betzide\core\views.py changed, reloading.
INFO 2025-07-22 12:44:42,574 autoreload 19376 4168 Watching for file changes with StatReloader
INFO 2025-07-22 12:44:42,626 autoreload 17584 11116 Watching for file changes with StatReloader
INFO 2025-07-22 12:44:42,717 autoreload 19292 18620 Watching for file changes with StatReloader
ERROR 2025-07-22 12:45:03,384 log 19376 21584 Internal Server Error: /
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\views.py", line 105, in home_view
    return render(request, 'home.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\defaulttags.py", line 194, in render
    len_values = len(values)
                 ^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\db\models\query.py", line 380, in __len__
    self._fetch_all()
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\db\models\query.py", line 1883, in _fetch_all
    self._prefetch_related_objects()
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\db\models\query.py", line 1273, in _prefetch_related_objects
    prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\db\models\query.py", line 2296, in prefetch_related_objects
    raise AttributeError(
AttributeError: Cannot find 'market_set' on Event object, 'market_set' is an invalid parameter to prefetch_related()
ERROR 2025-07-22 12:45:03,394 basehttp 19376 21584 "GET / HTTP/1.1" 500 183805
INFO 2025-07-22 12:45:06,820 autoreload 17584 11116 C:\Users\<USER>\Desktop\projects\betzide\core\views.py changed, reloading.
ERROR 2025-07-22 12:45:06,835 log 19376 21584 Internal Server Error: /
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\views.py", line 105, in home_view
    return render(request, 'home.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\defaulttags.py", line 194, in render
    len_values = len(values)
                 ^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\db\models\query.py", line 380, in __len__
    self._fetch_all()
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\db\models\query.py", line 1883, in _fetch_all
    self._prefetch_related_objects()
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\db\models\query.py", line 1273, in _prefetch_related_objects
    prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\db\models\query.py", line 2296, in prefetch_related_objects
    raise AttributeError(
AttributeError: Cannot find 'market_set' on Event object, 'market_set' is an invalid parameter to prefetch_related()
ERROR 2025-07-22 12:45:06,857 basehttp 19376 21584 "GET / HTTP/1.1" 500 183805
INFO 2025-07-22 12:45:06,926 autoreload 19292 18620 C:\Users\<USER>\Desktop\projects\betzide\core\views.py changed, reloading.
INFO 2025-07-22 12:45:07,305 autoreload 19376 4168 C:\Users\<USER>\Desktop\projects\betzide\core\views.py changed, reloading.
INFO 2025-07-22 12:45:14,089 autoreload 13388 5536 Watching for file changes with StatReloader
INFO 2025-07-22 12:45:15,171 autoreload 17808 21792 Watching for file changes with StatReloader
INFO 2025-07-22 12:45:18,410 autoreload 11444 15068 Watching for file changes with StatReloader
INFO 2025-07-22 12:45:32,408 autoreload 11444 15068 C:\Users\<USER>\Desktop\projects\betzide\core\views.py changed, reloading.
INFO 2025-07-22 12:45:32,467 autoreload 13388 5536 C:\Users\<USER>\Desktop\projects\betzide\core\views.py changed, reloading.
INFO 2025-07-22 12:45:32,990 autoreload 17808 21792 C:\Users\<USER>\Desktop\projects\betzide\core\views.py changed, reloading.
INFO 2025-07-22 12:45:34,820 autoreload 19424 20104 Watching for file changes with StatReloader
INFO 2025-07-22 12:45:34,820 autoreload 22852 2264 Watching for file changes with StatReloader
INFO 2025-07-22 12:45:37,614 autoreload 17440 7488 Watching for file changes with StatReloader
INFO 2025-07-22 12:48:22,141 autoreload 19424 20104 C:\Users\<USER>\Desktop\projects\betzide\core\views.py changed, reloading.
INFO 2025-07-22 12:48:22,164 autoreload 22852 2264 C:\Users\<USER>\Desktop\projects\betzide\core\views.py changed, reloading.
INFO 2025-07-22 12:48:22,293 autoreload 17440 7488 C:\Users\<USER>\Desktop\projects\betzide\core\views.py changed, reloading.
INFO 2025-07-22 12:48:24,444 autoreload 21524 19812 Watching for file changes with StatReloader
INFO 2025-07-22 12:48:39,125 autoreload 11624 10920 C:\Users\<USER>\Desktop\projects\betzide\core\views.py changed, reloading.
INFO 2025-07-22 12:48:39,154 autoreload 18896 720 C:\Users\<USER>\Desktop\projects\betzide\core\views.py changed, reloading.
INFO 2025-07-22 12:48:39,231 autoreload 21524 19812 C:\Users\<USER>\Desktop\projects\betzide\core\views.py changed, reloading.
INFO 2025-07-22 12:48:40,982 autoreload 23184 11956 Watching for file changes with StatReloader
INFO 2025-07-22 12:48:40,999 autoreload 7340 19476 Watching for file changes with StatReloader
INFO 2025-07-22 12:48:41,017 autoreload 21100 6624 Watching for file changes with StatReloader
INFO 2025-07-22 12:49:03,324 autoreload 21100 6624 C:\Users\<USER>\Desktop\projects\betzide\core\views.py changed, reloading.
INFO 2025-07-22 12:49:03,349 autoreload 7340 19476 C:\Users\<USER>\Desktop\projects\betzide\core\views.py changed, reloading.
INFO 2025-07-22 12:49:03,364 autoreload 23184 11956 C:\Users\<USER>\Desktop\projects\betzide\core\views.py changed, reloading.
INFO 2025-07-22 12:49:05,176 autoreload 24188 5716 Watching for file changes with StatReloader
INFO 2025-07-22 12:49:05,176 autoreload 5216 20856 Watching for file changes with StatReloader
INFO 2025-07-22 12:49:05,259 autoreload 18380 15096 Watching for file changes with StatReloader
INFO 2025-07-22 12:49:21,260 basehttp 24188 10368 "GET / HTTP/1.1" 200 45892
ERROR 2025-07-22 12:49:22,785 log 24188 10368 Internal Server Error: /api/v1/sports/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\sports\api_views.py", line 46, in get
    sports_data = self.get_sports_data()
                  ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\cache_utils.py", line 244, in wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\sports\api_views.py", line 40, in get_sports_data
    'url': sport.get_absolute_url()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\sports\models.py", line 41, in get_absolute_url
    return reverse('sports:sport_detail', kwargs={'sport_slug': self.slug})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'sport_detail' with keyword arguments '{'sport_slug': ''}' not found. 1 pattern(s) tried: ['sports/(?P<sport_slug>[-a-zA-Z0-9_]+)/\\Z']
ERROR 2025-07-22 12:49:22,804 basehttp 24188 10368 "GET /api/v1/sports/ HTTP/1.1" 500 144656
INFO 2025-07-22 12:51:16,239 basehttp 24188 10736 "GET /accounts/login/ HTTP/1.1" 200 14959
ERROR 2025-07-22 12:51:16,583 log 24188 10736 Internal Server Error: /api/v1/sports/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\sports\api_views.py", line 46, in get
    sports_data = self.get_sports_data()
                  ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\cache_utils.py", line 244, in wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\sports\api_views.py", line 40, in get_sports_data
    'url': sport.get_absolute_url()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\sports\models.py", line 41, in get_absolute_url
    return reverse('sports:sport_detail', kwargs={'sport_slug': self.slug})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'sport_detail' with keyword arguments '{'sport_slug': ''}' not found. 1 pattern(s) tried: ['sports/(?P<sport_slug>[-a-zA-Z0-9_]+)/\\Z']
ERROR 2025-07-22 12:51:16,587 basehttp 24188 10736 "GET /api/v1/sports/ HTTP/1.1" 500 144671
WARNING 2025-07-22 12:51:34,068 log 24188 10736 Not Found: /sports/boxing/
WARNING 2025-07-22 12:51:34,068 basehttp 24188 10736 "GET /sports/boxing/ HTTP/1.1" 404 2988
INFO 2025-07-22 12:51:47,732 autoreload 1996 8072 Watching for file changes with StatReloader
INFO 2025-07-22 12:52:49,466 basehttp 24188 17244 "GET / HTTP/1.1" 200 45892
INFO 2025-07-22 12:52:49,548 basehttp 24188 5564 "GET /static/css/base.css HTTP/1.1" 304 0
INFO 2025-07-22 12:52:49,550 basehttp 24188 21152 "GET /static/css/components/betslip.css HTTP/1.1" 304 0
INFO 2025-07-22 12:52:49,555 basehttp 24188 2132 "GET /static/css/theme/betika-theme.css HTTP/1.1" 304 0
INFO 2025-07-22 12:52:49,613 basehttp 24188 2132 "GET /static/js/betting/betslip.js HTTP/1.1" 304 0
INFO 2025-07-22 12:52:49,730 basehttp 24188 2132 "GET /static/js/main.js HTTP/1.1" 304 0
INFO 2025-07-22 12:52:49,749 basehttp 24188 2132 "GET /static/js/navigation/sidebar.js HTTP/1.1" 304 0
INFO 2025-07-22 12:52:50,096 basehttp 24188 8432 "GET /static/css/pages/home.css HTTP/1.1" 200 12811
INFO 2025-07-22 12:52:50,142 basehttp 24188 17244 "GET /static/css/responsive.css HTTP/1.1" 200 8994
INFO 2025-07-22 12:52:50,166 basehttp 24188 5564 "GET /static/js/performance/cache-manager.js HTTP/1.1" 200 9855
INFO 2025-07-22 12:52:50,244 basehttp 24188 21152 "GET /static/js/mobile/touch-interface.js HTTP/1.1" 200 12025
INFO 2025-07-22 12:52:50,477 basehttp 24188 2132 "GET /static/js/pages/home.js HTTP/1.1" 200 16425
INFO 2025-07-22 12:52:50,598 basehttp 24188 14876 "GET /static/js/performance/lazy-loading.js HTTP/1.1" 200 8581
WARNING 2025-07-22 12:52:50,816 basehttp 24188 14876 "GET /static/images/favicon.ico HTTP/1.1" 404 1914
ERROR 2025-07-22 12:52:51,033 log 24188 2132 Internal Server Error: /api/v1/sports/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\sports\api_views.py", line 46, in get
    sports_data = self.get_sports_data()
                  ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\cache_utils.py", line 244, in wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\sports\api_views.py", line 40, in get_sports_data
    'url': sport.get_absolute_url()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\sports\models.py", line 41, in get_absolute_url
    return reverse('sports:sport_detail', kwargs={'sport_slug': self.slug})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'sport_detail' with keyword arguments '{'sport_slug': ''}' not found. 1 pattern(s) tried: ['sports/(?P<sport_slug>[-a-zA-Z0-9_]+)/\\Z']
ERROR 2025-07-22 12:52:51,040 basehttp 24188 2132 "GET /api/v1/sports/ HTTP/1.1" 500 144490
WARNING 2025-07-22 12:54:00,124 log 24188 2132 Not Found: /sports/boxing/
WARNING 2025-07-22 12:54:00,124 basehttp 24188 2132 "GET /sports/boxing/ HTTP/1.1" 404 2988
WARNING 2025-07-22 13:13:29,538 log 24188 2300 Not Found: /sports/boxing/
WARNING 2025-07-22 13:13:29,546 basehttp 24188 2300 "GET /sports/boxing/ HTTP/1.1" 404 2988
WARNING 2025-07-22 13:13:31,498 log 24188 2300 Not Found: /sports/boxing/
WARNING 2025-07-22 13:13:31,499 basehttp 24188 2300 "GET /sports/boxing/ HTTP/1.1" 404 2988
WARNING 2025-07-22 13:13:32,465 log 24188 2300 Not Found: /sports/boxing/
WARNING 2025-07-22 13:13:32,467 basehttp 24188 2300 "GET /sports/boxing/ HTTP/1.1" 404 2988
INFO 2025-07-22 13:13:38,200 basehttp 24188 2300 "GET /accounts/register/ HTTP/1.1" 200 17599
ERROR 2025-07-22 13:13:38,356 log 24188 2300 Internal Server Error: /api/v1/sports/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\sports\api_views.py", line 46, in get
    sports_data = self.get_sports_data()
                  ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\cache_utils.py", line 244, in wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\sports\api_views.py", line 40, in get_sports_data
    'url': sport.get_absolute_url()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\sports\models.py", line 41, in get_absolute_url
    return reverse('sports:sport_detail', kwargs={'sport_slug': self.slug})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'sport_detail' with keyword arguments '{'sport_slug': ''}' not found. 1 pattern(s) tried: ['sports/(?P<sport_slug>[-a-zA-Z0-9_]+)/\\Z']
ERROR 2025-07-22 13:13:38,365 basehttp 24188 2300 "GET /api/v1/sports/ HTTP/1.1" 500 144508
WARNING 2025-07-22 13:13:45,092 log 24188 2300 Not Found: /sports/rugby/
WARNING 2025-07-22 13:13:45,095 basehttp 24188 2300 "GET /sports/rugby/ HTTP/1.1" 404 2985
WARNING 2025-07-22 13:13:47,891 log 24188 2300 Not Found: /sports/aussie-rules/
WARNING 2025-07-22 13:13:47,892 basehttp 24188 2300 "GET /sports/aussie-rules/ HTTP/1.1" 404 3006
ERROR 2025-07-22 13:17:24,810 log 24188 2300 Internal Server Error: /sports/boxing/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\defaulttags.py", line 1026, in find_library
    return parser.libraries[name]
           ~~~~~~~~~~~~~~~~^^^^^^
KeyError: 'sports_tags'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\sports\views.py", line 63, in sport_detail_view
    return render(request, 'sports/sport_detail.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader_tags.py", line 132, in render
    compiled_parent = self.get_parent(context)
                      ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader_tags.py", line 129, in get_parent
    return self.find_template(parent, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader_tags.py", line 107, in find_template
    template, origin = context.template.engine.find_template(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\defaulttags.py", line 1088, in load
    lib = find_library(parser, name)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\defaulttags.py", line 1028, in find_library
    raise TemplateSyntaxError(
django.template.exceptions.TemplateSyntaxError: 'sports_tags' is not a registered tag library. Must be one of:
admin_list
admin_modify
admin_urls
cache
compress
i18n
l10n
log
performance_tags
rest_framework
static
tz
ERROR 2025-07-22 13:17:24,844 basehttp 24188 2300 "GET /sports/boxing/ HTTP/1.1" **********
ERROR 2025-07-22 13:17:38,897 log 24188 2300 Internal Server Error: /
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\defaulttags.py", line 1026, in find_library
    return parser.libraries[name]
           ~~~~~~~~~~~~~~~~^^^^^^
KeyError: 'sports_tags'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\core\views.py", line 105, in home_view
    return render(request, 'home.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader_tags.py", line 132, in render
    compiled_parent = self.get_parent(context)
                      ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader_tags.py", line 129, in get_parent
    return self.find_template(parent, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader_tags.py", line 107, in find_template
    template, origin = context.template.engine.find_template(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\defaulttags.py", line 1088, in load
    lib = find_library(parser, name)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\defaulttags.py", line 1028, in find_library
    raise TemplateSyntaxError(
django.template.exceptions.TemplateSyntaxError: 'sports_tags' is not a registered tag library. Must be one of:
admin_list
admin_modify
admin_urls
cache
compress
i18n
l10n
log
performance_tags
rest_framework
static
tz
ERROR 2025-07-22 13:17:38,908 basehttp 24188 2300 "GET / HTTP/1.1" **********
INFO 2025-07-22 13:18:31,709 autoreload 20700 8232 Watching for file changes with StatReloader
ERROR 2025-07-22 13:18:50,452 log 24188 2300 Internal Server Error: /sports/boxing/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\defaulttags.py", line 1026, in find_library
    return parser.libraries[name]
           ~~~~~~~~~~~~~~~~^^^^^^
KeyError: 'sports_tags'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\sports\views.py", line 63, in sport_detail_view
    return render(request, 'sports/sport_detail.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader_tags.py", line 132, in render
    compiled_parent = self.get_parent(context)
                      ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader_tags.py", line 129, in get_parent
    return self.find_template(parent, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loader_tags.py", line 107, in find_template
    template, origin = context.template.engine.find_template(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\defaulttags.py", line 1088, in load
    lib = find_library(parser, name)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\projects\betzide\venv\Lib\site-packages\django\template\defaulttags.py", line 1028, in find_library
    raise TemplateSyntaxError(
django.template.exceptions.TemplateSyntaxError: 'sports_tags' is not a registered tag library. Must be one of:
admin_list
admin_modify
admin_urls
cache
compress
i18n
l10n
log
performance_tags
rest_framework
static
tz
ERROR 2025-07-22 13:18:50,461 basehttp 24188 2300 "GET /sports/boxing/ HTTP/1.1" **********
INFO 2025-07-22 13:19:24,287 basehttp 24188 11344 "GET /sports/boxing/ HTTP/1.1" 200 13807
WARNING 2025-07-22 13:19:27,212 basehttp 24188 11344 "GET /static/js/betting.js HTTP/1.1" 404 1899
INFO 2025-07-22 13:19:27,815 basehttp 24188 3880 "GET /api/v1/sports/ HTTP/1.1" 200 2108
WARNING 2025-07-22 13:19:29,570 log 24188 3880 Not Found: /api/v1/sports/events/
WARNING 2025-07-22 13:19:29,576 basehttp 24188 3880 "GET /api/v1/sports/events/?status=live HTTP/1.1" 404 23
INFO 2025-07-22 13:19:33,461 basehttp 24188 3880 "GET / HTTP/1.1" 200 45873
INFO 2025-07-22 13:19:45,037 basehttp 24188 3880 "GET /sports/boxing/ HTTP/1.1" 200 13807
WARNING 2025-07-22 13:19:45,163 basehttp 24188 3880 "GET /static/js/betting.js HTTP/1.1" 404 1899
INFO 2025-07-22 13:24:54,599 basehttp 24188 11832 "GET /sports/boxing/ HTTP/1.1" 200 16613
WARNING 2025-07-22 13:24:56,728 basehttp 24188 11832 "GET /static/js/betting.js HTTP/1.1" 404 1899
INFO 2025-07-22 13:24:57,171 basehttp 24188 21884 "GET /api/v1/sports/ HTTP/1.1" 200 2108
WARNING 2025-07-22 13:24:59,189 log 24188 21884 Not Found: /api/v1/sports/events/
WARNING 2025-07-22 13:24:59,196 basehttp 24188 21884 "GET /api/v1/sports/events/?status=live HTTP/1.1" 404 23
INFO 2025-07-22 13:25:04,508 basehttp 24188 21884 "GET / HTTP/1.1" 200 45873
WARNING 2025-07-22 13:32:10,708 log 24188 24392 Not Found: /betting/dashboard/
WARNING 2025-07-22 13:32:10,721 basehttp 24188 24392 "GET /betting/dashboard/ HTTP/1.1" 404 5431
WARNING 2025-07-22 13:32:23,361 log 24188 24392 Not Found: /payments/dashboard/
WARNING 2025-07-22 13:32:23,365 basehttp 24188 24392 "GET /payments/dashboard/ HTTP/1.1" 404 6084
INFO 2025-07-22 13:32:53,712 basehttp 24188 24392 "GET /payments/ HTTP/1.1" 302 0
INFO 2025-07-22 13:32:53,763 basehttp 24188 24392 "GET /accounts/login/?next=/payments/ HTTP/1.1" 200 14952
INFO 2025-07-22 13:32:56,931 basehttp 24188 24392 "GET /api/v1/sports/ HTTP/1.1" 200 2108
WARNING 2025-07-22 13:32:58,299 log 24188 24392 Not Found: /api/v1/sports/events/
WARNING 2025-07-22 13:32:58,300 basehttp 24188 24392 "GET /api/v1/sports/events/?status=live HTTP/1.1" 404 23
INFO 2025-07-22 13:33:05,193 basehttp 24188 24392 "GET /sports/ HTTP/1.1" 200 39689
INFO 2025-07-22 13:33:16,784 basehttp 24188 24392 "GET /accounts/profile/ HTTP/1.1" 302 0
INFO 2025-07-22 13:33:16,932 basehttp 24188 24392 "GET /accounts/login/?next=/accounts/profile/ HTTP/1.1" 200 14952
INFO 2025-07-22 13:40:04,301 autoreload 1996 8072 C:\Users\<USER>\Desktop\projects\betzide\sports\views.py changed, reloading.
INFO 2025-07-22 13:40:04,314 autoreload 20700 8232 C:\Users\<USER>\Desktop\projects\betzide\sports\views.py changed, reloading.
INFO 2025-07-22 13:40:04,348 autoreload 5216 20856 C:\Users\<USER>\Desktop\projects\betzide\sports\views.py changed, reloading.
INFO 2025-07-22 13:40:04,448 autoreload 24188 5716 C:\Users\<USER>\Desktop\projects\betzide\sports\views.py changed, reloading.
INFO 2025-07-22 13:40:04,481 autoreload 18380 15096 C:\Users\<USER>\Desktop\projects\betzide\sports\views.py changed, reloading.
INFO 2025-07-22 13:40:07,750 autoreload 14484 23568 Watching for file changes with StatReloader
INFO 2025-07-22 13:40:07,826 autoreload 8344 9648 Watching for file changes with StatReloader
INFO 2025-07-22 13:40:07,840 autoreload 18980 5380 Watching for file changes with StatReloader
INFO 2025-07-22 13:40:08,114 autoreload 9172 23684 Watching for file changes with StatReloader
INFO 2025-07-22 13:40:09,771 autoreload 14324 7772 Watching for file changes with StatReloader
INFO 2025-07-22 13:40:28,956 basehttp 14484 5260 "GET /sports/ HTTP/1.1" 200 52867
INFO 2025-07-22 13:40:31,190 basehttp 14484 5260 "GET /api/v1/sports/ HTTP/1.1" 200 2108
WARNING 2025-07-22 13:40:33,108 log 14484 5260 Not Found: /api/v1/sports/events/
WARNING 2025-07-22 13:40:33,112 basehttp 14484 5260 "GET /api/v1/sports/events/?status=live HTTP/1.1" 404 23
INFO 2025-07-22 13:40:39,860 basehttp 14484 5260 "GET / HTTP/1.1" 200 45873
INFO 2025-07-22 13:42:14,473 basehttp 14484 11332 "GET /sports/boxing/ HTTP/1.1" 200 16613
INFO 2025-07-22 13:42:14,925 basehttp 14484 12564 "GET /static/css/pages/home.css HTTP/1.1" 304 0
INFO 2025-07-22 13:42:15,000 basehttp 14484 11652 "GET /static/css/sports.css HTTP/1.1" 304 0
INFO 2025-07-22 13:42:15,087 basehttp 14484 996 "GET /static/js/pages/home.js HTTP/1.1" 304 0
INFO 2025-07-22 13:42:15,094 basehttp 14484 23740 "GET /static/js/sports.js HTTP/1.1" 304 0
WARNING 2025-07-22 13:42:16,186 basehttp 14484 11332 "GET /static/js/betting.js HTTP/1.1" 404 1899
INFO 2025-07-22 13:42:16,718 basehttp 14484 23740 "GET /api/v1/sports/ HTTP/1.1" 200 2108
WARNING 2025-07-22 13:42:16,881 log 14484 23740 Not Found: /api/v1/sports/events/
WARNING 2025-07-22 13:42:16,894 basehttp 14484 23740 "GET /api/v1/sports/events/?status=live HTTP/1.1" 404 23
INFO 2025-07-22 13:42:20,654 basehttp 14484 23740 "GET /sports/rugby/ HTTP/1.1" 200 16611
WARNING 2025-07-22 13:42:21,152 basehttp 14484 23740 "GET /static/js/betting.js HTTP/1.1" 404 1899
INFO 2025-07-22 13:42:25,431 basehttp 14484 3424 "GET /sports/aussie-rules/ HTTP/1.1" 200 16639
WARNING 2025-07-22 13:42:25,965 basehttp 14484 3424 "GET /static/js/betting.js HTTP/1.1" 404 1899
INFO 2025-07-22 13:42:29,634 basehttp 14484 11988 "GET /sports/baseball/ HTTP/1.1" 200 16623
WARNING 2025-07-22 13:42:30,579 basehttp 14484 11988 "GET /static/js/betting.js HTTP/1.1" 404 1899
INFO 2025-07-22 13:42:34,591 basehttp 14484 1580 "GET /sports/table-tennis/ HTTP/1.1" 200 16638
WARNING 2025-07-22 13:42:35,117 basehttp 14484 1580 "GET /static/js/betting.js HTTP/1.1" 404 1899
INFO 2025-07-22 13:42:37,098 basehttp 14484 14828 "GET /sports/cricket/ HTTP/1.1" 200 16613
WARNING 2025-07-22 13:42:37,332 basehttp 14484 14828 "GET /static/js/betting.js HTTP/1.1" 404 1899
INFO 2025-07-22 13:42:40,018 basehttp 14484 5844 "GET /sports/tennis/ HTTP/1.1" 200 19533
WARNING 2025-07-22 13:42:40,292 basehttp 14484 5844 "GET /static/js/betting.js HTTP/1.1" 404 1899
INFO 2025-07-22 13:42:42,867 basehttp 14484 23172 "GET /sports/mma/ HTTP/1.1" 200 16601
WARNING 2025-07-22 13:42:43,050 basehttp 14484 23172 "GET /static/js/betting.js HTTP/1.1" 404 1899
INFO 2025-07-22 13:42:47,402 basehttp 14484 7012 "GET /sports/basketball/ HTTP/1.1" 200 22122
WARNING 2025-07-22 13:42:47,504 basehttp 14484 7012 "GET /static/js/betting.js HTTP/1.1" 404 1899
INFO 2025-07-22 13:42:54,434 basehttp 14484 11736 "GET /sports/water-polo/ HTTP/1.1" 200 16625
WARNING 2025-07-22 13:42:54,530 basehttp 14484 11736 "GET /static/js/betting.js HTTP/1.1" 404 1899
INFO 2025-07-22 13:42:56,011 basehttp 14484 23256 "GET /sports/volleyball/ HTTP/1.1" 200 16633
WARNING 2025-07-22 13:42:56,087 basehttp 14484 23256 "GET /static/js/betting.js HTTP/1.1" 404 1899
INFO 2025-07-22 13:42:57,660 basehttp 14484 15468 "GET /sports/esoccer/ HTTP/1.1" 200 16613
WARNING 2025-07-22 13:42:57,734 basehttp 14484 15468 "GET /static/js/betting.js HTTP/1.1" 404 1899
INFO 2025-07-22 13:42:59,987 basehttp 14484 22648 "GET /sports/handball/ HTTP/1.1" 200 16620
WARNING 2025-07-22 13:43:00,084 basehttp 14484 22648 "GET /static/js/betting.js HTTP/1.1" 404 1899
INFO 2025-07-22 13:43:01,703 basehttp 14484 20828 "GET /sports/darts/ HTTP/1.1" 200 16606
WARNING 2025-07-22 13:43:01,778 basehttp 14484 20828 "GET /static/js/betting.js HTTP/1.1" 404 1899
INFO 2025-07-22 13:43:40,606 basehttp 14484 15236 "GET / HTTP/1.1" 200 45873
INFO 2025-07-22 13:43:41,108 basehttp 14484 15236 "GET /static/css/base.css HTTP/1.1" 200 3517
INFO 2025-07-22 13:43:41,113 basehttp 14484 5260 "GET /static/css/responsive.css HTTP/1.1" 200 8994
INFO 2025-07-22 13:43:41,117 basehttp 14484 20968 "GET /static/css/pages/home.css HTTP/1.1" 200 12811
INFO 2025-07-22 13:43:41,147 basehttp 14484 8364 "GET /static/css/components/betslip.css HTTP/1.1" 200 9514
INFO 2025-07-22 13:43:41,148 basehttp 14484 21884 "GET /static/css/theme/betika-theme.css HTTP/1.1" 200 16115
INFO 2025-07-22 13:43:41,198 basehttp 14484 8364 "GET /static/js/performance/cache-manager.js HTTP/1.1" 200 9855
INFO 2025-07-22 13:43:41,199 basehttp 14484 21884 "GET /static/js/mobile/touch-interface.js HTTP/1.1" 200 12025
INFO 2025-07-22 13:43:41,201 basehttp 14484 20968 "GET /static/js/performance/lazy-loading.js HTTP/1.1" 200 8581
INFO 2025-07-22 13:43:41,203 basehttp 14484 15236 "GET /static/js/betting/betslip.js HTTP/1.1" 200 28025
INFO 2025-07-22 13:43:41,206 basehttp 14484 15632 "GET /static/js/main.js HTTP/1.1" 200 7698
INFO 2025-07-22 13:43:41,206 basehttp 14484 5260 "GET /static/js/navigation/sidebar.js HTTP/1.1" 200 2540
INFO 2025-07-22 13:43:41,225 basehttp 14484 8364 "GET /static/js/pages/home.js HTTP/1.1" 200 16425
INFO 2025-07-22 13:43:41,269 basehttp 14484 8364 "GET /api/v1/sports/ HTTP/1.1" 200 2108
WARNING 2025-07-22 13:43:41,365 log 14484 8364 Not Found: /api/v1/sports/events/
WARNING 2025-07-22 13:43:41,378 basehttp 14484 8364 "GET /api/v1/sports/events/?status=live HTTP/1.1" 404 23
INFO 2025-07-22 13:43:47,473 basehttp 14484 8364 "GET /accounts/register/ HTTP/1.1" 200 17592
INFO 2025-07-22 13:43:52,611 basehttp 14484 8364 "GET /accounts/login/ HTTP/1.1" 200 14952
INFO 2025-07-22 13:44:36,721 basehttp 14484 8364 "GET /sports/ HTTP/1.1" 200 52867
INFO 2025-07-22 13:44:36,805 basehttp 14484 8364 "GET /static/css/sports.css HTTP/1.1" 200 19146
INFO 2025-07-22 13:44:36,819 basehttp 14484 5260 "GET /static/js/sports.js HTTP/1.1" 200 25761
WARNING 2025-07-22 13:44:40,671 log 14484 5260 Not Found: /jackpots/
WARNING 2025-07-22 13:44:40,675 basehttp 14484 5260 "GET /jackpots/ HTTP/1.1" 404 4025
INFO 2025-07-22 13:45:19,008 basehttp 14484 15188 "GET /jackpot/ HTTP/1.1" 302 0
INFO 2025-07-22 13:45:19,069 basehttp 14484 15188 "GET /accounts/login/?next=/jackpot/ HTTP/1.1" 200 14952
INFO 2025-07-22 13:45:21,292 basehttp 14484 15188 "GET /api/v1/sports/ HTTP/1.1" 200 2108
WARNING 2025-07-22 13:45:23,784 log 14484 15188 Not Found: /api/v1/sports/events/
WARNING 2025-07-22 13:45:23,981 basehttp 14484 15188 "GET /api/v1/sports/events/?status=live HTTP/1.1" 404 23
INFO 2025-07-22 13:59:33,869 autoreload 9172 23684 C:\Users\<USER>\Desktop\projects\betzide\jackpot\views.py changed, reloading.

INFO 2025-07-22 13:59:33,879 autoreload 14324 7772 C:\Users\<USER>\Desktop\projects\betzide\jackpot\views.py changed, reloading.
INFO 2025-07-22 13:59:33,905 autoreload 8344 9648 C:\Users\<USER>\Desktop\projects\betzide\jackpot\views.py changed, reloading.
INFO 2025-07-22 13:59:33,985 autoreload 18980 5380 C:\Users\<USER>\Desktop\projects\betzide\jackpot\views.py changed, reloading.
INFO 2025-07-22 13:59:37,338 autoreload 4380 6000 Watching for file changes with StatReloader
INFO 2025-07-22 13:59:37,681 autoreload 3124 8632 Watching for file changes with StatReloader
INFO 2025-07-22 13:59:38,171 autoreload 23260 22064 Watching for file changes with StatReloader
INFO 2025-07-22 13:59:38,202 autoreload 21204 2640 Watching for file changes with StatReloader
INFO 2025-07-22 13:59:39,224 autoreload 12544 1936 Watching for file changes with StatReloader
INFO 2025-07-22 14:01:24,826 autoreload 3124 8632 C:\Users\<USER>\Desktop\projects\betzide\jackpot\urls.py changed, reloading.
INFO 2025-07-22 14:01:24,832 autoreload 4380 6000 C:\Users\<USER>\Desktop\projects\betzide\jackpot\urls.py changed, reloading.
INFO 2025-07-22 14:01:24,840 autoreload 12544 1936 C:\Users\<USER>\Desktop\projects\betzide\jackpot\urls.py changed, reloading.
INFO 2025-07-22 14:01:24,841 autoreload 23260 22064 C:\Users\<USER>\Desktop\projects\betzide\jackpot\urls.py changed, reloading.
INFO 2025-07-22 14:01:24,869 autoreload 21204 2640 C:\Users\<USER>\Desktop\projects\betzide\jackpot\urls.py changed, reloading.
INFO 2025-07-22 14:01:27,365 autoreload 21852 24048 Watching for file changes with StatReloader
INFO 2025-07-22 14:01:27,521 autoreload 20864 6692 Watching for file changes with StatReloader
INFO 2025-07-22 14:01:27,545 autoreload 4352 18476 Watching for file changes with StatReloader
INFO 2025-07-22 14:01:27,600 autoreload 18036 9180 Watching for file changes with StatReloader
INFO 2025-07-22 14:01:27,763 autoreload 16316 16340 Watching for file changes with StatReloader
INFO 2025-07-22 14:02:08,896 autoreload 4352 18476 C:\Users\<USER>\Desktop\projects\betzide\jackpot\views.py changed, reloading.
INFO 2025-07-22 14:02:08,909 autoreload 21852 24048 C:\Users\<USER>\Desktop\projects\betzide\jackpot\views.py changed, reloading.
INFO 2025-07-22 14:02:08,917 autoreload 20864 6692 C:\Users\<USER>\Desktop\projects\betzide\jackpot\views.py changed, reloading.
INFO 2025-07-22 14:02:09,217 autoreload 18036 9180 C:\Users\<USER>\Desktop\projects\betzide\jackpot\views.py changed, reloading.
INFO 2025-07-22 14:02:09,230 autoreload 16316 16340 C:\Users\<USER>\Desktop\projects\betzide\jackpot\views.py changed, reloading.
INFO 2025-07-22 14:02:10,856 autoreload 12444 18272 Watching for file changes with StatReloader
INFO 2025-07-22 14:02:11,116 autoreload 18588 14528 Watching for file changes with StatReloader
INFO 2025-07-22 14:02:11,448 autoreload 920 23572 Watching for file changes with StatReloader
INFO 2025-07-22 14:02:11,764 autoreload 1284 23092 Watching for file changes with StatReloader
INFO 2025-07-22 14:02:12,185 autoreload 12476 2268 Watching for file changes with StatReloader
INFO 2025-07-22 14:02:29,351 autoreload 920 23572 C:\Users\<USER>\Desktop\projects\betzide\jackpot\views.py changed, reloading.
INFO 2025-07-22 14:02:29,367 autoreload 12444 18272 C:\Users\<USER>\Desktop\projects\betzide\jackpot\views.py changed, reloading.
INFO 2025-07-22 14:02:30,127 autoreload 12476 2268 C:\Users\<USER>\Desktop\projects\betzide\jackpot\views.py changed, reloading.
INFO 2025-07-22 14:02:30,142 autoreload 1284 23092 C:\Users\<USER>\Desktop\projects\betzide\jackpot\views.py changed, reloading.
INFO 2025-07-22 14:02:30,280 autoreload 18588 14528 C:\Users\<USER>\Desktop\projects\betzide\jackpot\views.py changed, reloading.
INFO 2025-07-22 14:02:31,987 autoreload 15400 9104 Watching for file changes with StatReloader
INFO 2025-07-22 14:02:32,041 autoreload 1356 1032 Watching for file changes with StatReloader
INFO 2025-07-22 14:02:35,722 autoreload 21432 21120 Watching for file changes with StatReloader
INFO 2025-07-22 14:02:35,760 autoreload 22724 5808 Watching for file changes with StatReloader
INFO 2025-07-22 14:02:36,323 autoreload 11340 24004 Watching for file changes with StatReloader
INFO 2025-07-22 14:03:02,225 autoreload 15400 9104 C:\Users\<USER>\Desktop\projects\betzide\jackpot\views.py changed, reloading.
INFO 2025-07-22 14:03:02,241 autoreload 11340 24004 C:\Users\<USER>\Desktop\projects\betzide\jackpot\views.py changed, reloading.
INFO 2025-07-22 14:03:02,876 autoreload 1356 1032 C:\Users\<USER>\Desktop\projects\betzide\jackpot\views.py changed, reloading.
INFO 2025-07-22 14:03:02,974 autoreload 22724 5808 C:\Users\<USER>\Desktop\projects\betzide\jackpot\views.py changed, reloading.
INFO 2025-07-22 14:03:03,021 autoreload 21432 21120 C:\Users\<USER>\Desktop\projects\betzide\jackpot\views.py changed, reloading.
INFO 2025-07-22 14:03:04,266 autoreload 24256 15464 Watching for file changes with StatReloader
INFO 2025-07-22 14:03:04,465 autoreload 5908 22924 Watching for file changes with StatReloader
INFO 2025-07-22 14:03:06,874 autoreload 4004 6704 Watching for file changes with StatReloader
INFO 2025-07-22 14:03:06,996 autoreload 8340 18072 Watching for file changes with StatReloader
INFO 2025-07-22 14:03:07,230 autoreload 21192 1208 Watching for file changes with StatReloader
INFO 2025-07-22 14:04:00,679 autoreload 8340 18072 C:\Users\<USER>\Desktop\projects\betzide\jackpot\models.py changed, reloading.
INFO 2025-07-22 14:04:00,697 autoreload 21192 1208 C:\Users\<USER>\Desktop\projects\betzide\jackpot\models.py changed, reloading.
INFO 2025-07-22 14:04:00,726 autoreload 24256 15464 C:\Users\<USER>\Desktop\projects\betzide\jackpot\models.py changed, reloading.
INFO 2025-07-22 14:04:00,736 autoreload 4004 6704 C:\Users\<USER>\Desktop\projects\betzide\jackpot\models.py changed, reloading.
INFO 2025-07-22 14:04:00,759 autoreload 5908 22924 C:\Users\<USER>\Desktop\projects\betzide\jackpot\models.py changed, reloading.
INFO 2025-07-22 14:04:03,019 autoreload 21856 18800 Watching for file changes with StatReloader
INFO 2025-07-22 14:04:03,046 autoreload 12956 24260 Watching for file changes with StatReloader
INFO 2025-07-22 14:04:03,200 autoreload 24416 20312 Watching for file changes with StatReloader
INFO 2025-07-22 14:04:03,217 autoreload 14240 11204 Watching for file changes with StatReloader
INFO 2025-07-22 14:04:03,938 autoreload 540 16388 Watching for file changes with StatReloader
INFO 2025-07-22 14:04:17,401 autoreload 12956 24260 C:\Users\<USER>\Desktop\projects\betzide\jackpot\models.py changed, reloading.
INFO 2025-07-22 14:04:17,479 autoreload 24416 20312 C:\Users\<USER>\Desktop\projects\betzide\jackpot\models.py changed, reloading.
INFO 2025-07-22 14:04:17,486 autoreload 14240 11204 C:\Users\<USER>\Desktop\projects\betzide\jackpot\models.py changed, reloading.
INFO 2025-07-22 14:04:18,368 autoreload 540 16388 C:\Users\<USER>\Desktop\projects\betzide\jackpot\models.py changed, reloading.
INFO 2025-07-22 14:04:18,601 autoreload 21856 18800 C:\Users\<USER>\Desktop\projects\betzide\jackpot\models.py changed, reloading.
INFO 2025-07-22 14:04:19,471 autoreload 20516 11624 Watching for file changes with StatReloader
INFO 2025-07-22 14:04:19,497 autoreload 11224 22724 Watching for file changes with StatReloader
INFO 2025-07-22 14:04:19,761 autoreload 7340 4632 Watching for file changes with StatReloader
INFO 2025-07-22 14:04:23,528 autoreload 18280 18312 Watching for file changes with StatReloader
INFO 2025-07-22 14:04:23,788 autoreload 24160 20700 Watching for file changes with StatReloader
INFO 2025-07-22 16:04:59,544 autoreload 27192 27104 Watching for file changes with StatReloader
INFO 2025-07-22 16:06:53,564 autoreload 26320 12476 Watching for file changes with StatReloader
INFO 2025-07-22 16:07:30,749 basehttp 20516 27120 "GET / HTTP/1.1" 200 50943
INFO 2025-07-22 16:07:30,904 basehttp 20516 5288 "GET /static/js/performance/lazy-loading.js HTTP/1.1" 304 0
INFO 2025-07-22 16:07:30,911 basehttp 20516 27120 "GET /static/css/responsive.css HTTP/1.1" 304 0
INFO 2025-07-22 16:07:30,915 basehttp 20516 27492 "GET /static/js/performance/cache-manager.js HTTP/1.1" 304 0
INFO 2025-07-22 16:07:30,920 basehttp 20516 25568 "GET /static/css/pages/home.css HTTP/1.1" 304 0
INFO 2025-07-22 16:07:30,930 basehttp 20516 27484 "GET /static/js/mobile/touch-interface.js HTTP/1.1" 304 0
INFO 2025-07-22 16:07:30,971 basehttp 20516 25460 "GET /static/js/pages/home.js HTTP/1.1" 304 0
INFO 2025-07-22 16:07:31,581 basehttp 20516 25460 "GET /api/v1/sports/ HTTP/1.1" 200 2109
WARNING 2025-07-22 16:07:31,613 log 20516 25460 Not Found: /api/v1/sports/events/
WARNING 2025-07-22 16:07:31,615 basehttp 20516 25460 "GET /api/v1/sports/events/?status=live HTTP/1.1" 404 23
INFO 2025-07-22 16:07:36,965 basehttp 20516 25460 "GET /jackpot/ HTTP/1.1" 200 37854
INFO 2025-07-22 16:07:37,567 basehttp 20516 25460 "GET /static/css/jackpot.css HTTP/1.1" 200 5193
INFO 2025-07-22 16:07:37,747 basehttp 20516 27484 "GET /static/js/jackpot.js HTTP/1.1" 200 12058
INFO 2025-07-22 16:08:32,367 basehttp 20516 27484 "GET /accounts/login/ HTTP/1.1" 200 14952
INFO 2025-07-22 16:08:32,574 basehttp 20516 27484 "GET /api/v1/sports/ HTTP/1.1" 200 2109
WARNING 2025-07-22 16:08:32,596 log 20516 27484 Not Found: /api/v1/sports/events/
WARNING 2025-07-22 16:08:32,598 basehttp 20516 27484 "GET /api/v1/sports/events/?status=live HTTP/1.1" 404 23
WARNING 2025-07-22 16:08:41,631 log 20516 27484 Not Found: /shikisha/
WARNING 2025-07-22 16:08:41,632 basehttp 20516 27484 "GET /shikisha/ HTTP/1.1" 404 4025
WARNING 2025-07-22 16:08:55,195 log 20516 27484 Not Found: /live-score/
WARNING 2025-07-22 16:08:55,197 basehttp 20516 27484 "GET /live-score/ HTTP/1.1" 404 4031
WARNING 2025-07-22 16:09:02,729 log 20516 27484 Not Found: /live-score/
WARNING 2025-07-22 16:09:02,730 basehttp 20516 27484 "GET /live-score/ HTTP/1.1" 404 4031
INFO 2025-07-22 16:09:11,742 basehttp 20516 27484 "GET /sports/boxing/ HTTP/1.1" 200 16613
WARNING 2025-07-22 16:09:11,805 basehttp 20516 27484 "GET /static/js/betting.js HTTP/1.1" 404 1899
INFO 2025-07-22 16:09:14,848 basehttp 20516 25460 "GET /sports/boxing/ HTTP/1.1" 200 16613
WARNING 2025-07-22 16:09:14,926 basehttp 20516 25460 "GET /static/js/betting.js HTTP/1.1" 404 1899
INFO 2025-07-22 16:09:16,063 basehttp 20516 25568 "GET /sports/soccer/ HTTP/1.1" 200 60152
WARNING 2025-07-22 16:09:16,104 basehttp 20516 25568 "GET /static/js/betting.js HTTP/1.1" 404 1899
INFO 2025-07-22 16:10:06,857 basehttp 20516 27492 "GET / HTTP/1.1" 200 50943
INFO 2025-07-22 16:10:06,916 basehttp 20516 27492 "GET /api/v1/sports/ HTTP/1.1" 200 2109
WARNING 2025-07-22 16:10:06,927 log 20516 27492 Not Found: /api/v1/sports/events/
WARNING 2025-07-22 16:10:06,934 basehttp 20516 27492 "GET /api/v1/sports/events/?status=live HTTP/1.1" 404 23
INFO 2025-07-22 16:10:44,905 basehttp 20516 27492 "GET /sports/ HTTP/1.1" 200 52868
INFO 2025-07-22 16:10:46,783 basehttp 20516 27492 "GET / HTTP/1.1" 200 50943
INFO 2025-07-22 16:10:53,089 basehttp 20516 27492 "GET /jackpot/ HTTP/1.1" 200 37854
INFO 2025-07-22 16:11:44,731 basehttp 20516 27492 "GET /sports/soccer/ HTTP/1.1" 200 60152
WARNING 2025-07-22 16:11:44,789 basehttp 20516 27492 "GET /static/js/betting.js HTTP/1.1" 404 1899
INFO 2025-07-22 16:11:44,828 basehttp 20516 27120 "GET /api/v1/sports/ HTTP/1.1" 200 2109
WARNING 2025-07-22 16:11:44,838 log 20516 27120 Not Found: /api/v1/sports/events/
WARNING 2025-07-22 16:11:44,839 basehttp 20516 27120 "GET /api/v1/sports/events/?status=live HTTP/1.1" 404 23
INFO 2025-07-22 16:12:05,556 basehttp 20516 27120 "GET / HTTP/1.1" 200 50943
INFO 2025-07-22 16:14:21,627 autoreload 22328 5332 Watching for file changes with StatReloader
INFO 2025-07-22 16:14:40,377 basehttp 20516 27120 "GET /jackpot/ HTTP/1.1" 200 37854
INFO 2025-07-22 16:14:42,717 basehttp 20516 27120 "GET /api/v1/sports/ HTTP/1.1" 200 2109
WARNING 2025-07-22 16:14:42,752 log 20516 27120 Not Found: /api/v1/sports/events/
WARNING 2025-07-22 16:14:42,754 basehttp 20516 27120 "GET /api/v1/sports/events/?status=live HTTP/1.1" 404 23
INFO 2025-07-22 16:25:07,093 basehttp 20516 9084 "GET /accounts/login/ HTTP/1.1" 200 5771
INFO 2025-07-22 16:25:07,852 basehttp 20516 9084 "GET /static/css/auth.css HTTP/1.1" 200 9981
INFO 2025-07-22 16:25:24,270 basehttp 20516 9084 "GET /accounts/register/ HTTP/1.1" 200 9874
INFO 2025-07-22 16:25:24,433 basehttp 20516 9084 "GET /static/css/auth.css HTTP/1.1" 304 0
INFO 2025-07-22 16:25:33,501 basehttp 20516 9084 "GET / HTTP/1.1" 200 50943
INFO 2025-07-22 16:25:33,989 basehttp 20516 9084 "GET /api/v1/sports/ HTTP/1.1" 200 2109
WARNING 2025-07-22 16:25:34,025 log 20516 9084 Not Found: /api/v1/sports/events/
WARNING 2025-07-22 16:25:34,028 basehttp 20516 9084 "GET /api/v1/sports/events/?status=live HTTP/1.1" 404 23
INFO 2025-07-22 16:25:40,856 basehttp 20516 9084 "GET /accounts/login/ HTTP/1.1" 200 5771
INFO 2025-07-22 16:25:40,889 basehttp 20516 9084 "GET /static/css/auth.css HTTP/1.1" 304 0
INFO 2025-07-22 17:18:43,636 basehttp 20516 25248 "GET / HTTP/1.1" 200 47737
INFO 2025-07-22 17:18:44,332 basehttp 20516 19840 "GET /static/js/pages/home.js HTTP/1.1" 304 0
INFO 2025-07-22 17:18:44,333 basehttp 20516 25248 "GET /static/css/pages/home.css HTTP/1.1" 304 0
INFO 2025-07-22 17:18:44,981 basehttp 20516 25248 "GET /api/v1/sports/ HTTP/1.1" 200 2109
WARNING 2025-07-22 17:18:45,200 log 20516 25248 Not Found: /api/v1/sports/events/
WARNING 2025-07-22 17:18:45,209 basehttp 20516 25248 "GET /api/v1/sports/events/?status=live HTTP/1.1" 404 23
INFO 2025-07-22 17:18:55,251 basehttp 20516 25248 "GET /accounts/login/ HTTP/1.1" 200 5771
INFO 2025-07-22 17:18:55,401 basehttp 20516 25248 "GET /static/css/auth.css HTTP/1.1" 200 9505
INFO 2025-07-22 17:19:01,810 basehttp 20516 25248 "GET /accounts/register/ HTTP/1.1" 200 9874
INFO 2025-07-22 17:19:19,585 basehttp 20516 25248 "GET /accounts/register/ HTTP/1.1" 200 9874
WARNING 2025-07-22 17:19:30,336 log 20516 25248 Not Found: /terms/
WARNING 2025-07-22 17:19:30,342 basehttp 20516 25248 "GET /terms/ HTTP/1.1" 404 4016
INFO 2025-07-22 17:20:29,713 autoreload 7340 4632 C:\Users\<USER>\Desktop\projects\betzide\core\views.py changed, reloading.
INFO 2025-07-22 17:20:29,894 autoreload 22328 5332 C:\Users\<USER>\Desktop\projects\betzide\core\views.py changed, reloading.
INFO 2025-07-22 17:20:31,094 autoreload 20516 11624 C:\Users\<USER>\Desktop\projects\betzide\core\views.py changed, reloading.
INFO 2025-07-22 17:20:39,394 autoreload 16204 26208 Watching for file changes with StatReloader
INFO 2025-07-22 17:20:40,156 autoreload 24240 14736 Watching for file changes with StatReloader
INFO 2025-07-22 17:20:43,098 autoreload 26948 11052 Watching for file changes with StatReloader
INFO 2025-07-22 17:20:50,243 autoreload 16204 26208 C:\Users\<USER>\Desktop\projects\betzide\betika_clone\urls.py changed, reloading.
INFO 2025-07-22 17:20:54,674 autoreload 25092 23636 Watching for file changes with StatReloader
INFO 2025-07-22 17:21:01,760 autoreload 26948 11052 C:\Users\<USER>\Desktop\projects\betzide\betika_clone\urls.py changed, reloading.
INFO 2025-07-22 17:21:01,765 autoreload 24240 14736 C:\Users\<USER>\Desktop\projects\betzide\betika_clone\urls.py changed, reloading.
INFO 2025-07-22 17:21:02,269 autoreload 25092 23636 C:\Users\<USER>\Desktop\projects\betzide\betika_clone\urls.py changed, reloading.
INFO 2025-07-22 17:21:05,299 autoreload 20740 18996 Watching for file changes with StatReloader
INFO 2025-07-22 17:21:05,312 autoreload 26796 26252 Watching for file changes with StatReloader
INFO 2025-07-22 17:21:07,202 autoreload 18280 15676 Watching for file changes with StatReloader
INFO 2025-07-22 17:22:39,349 basehttp 20740 25956 "GET /terms/ HTTP/1.1" 200 17181
INFO 2025-07-22 17:22:39,618 basehttp 20740 25956 "GET /static/css/pages/legal.css HTTP/1.1" 200 3565
INFO 2025-07-22 17:22:39,874 basehttp 20740 25956 "GET /api/v1/sports/ HTTP/1.1" 200 2109
WARNING 2025-07-22 17:22:39,911 log 20740 25956 Not Found: /api/v1/sports/events/
WARNING 2025-07-22 17:22:39,913 basehttp 20740 25956 "GET /api/v1/sports/events/?status=live HTTP/1.1" 404 23
INFO 2025-07-22 17:22:44,868 basehttp 20740 25956 "GET /privacy/ HTTP/1.1" 200 18388
INFO 2025-07-22 17:22:45,110 basehttp 20740 25956 "GET /static/css/pages/legal.css HTTP/1.1" 304 0
INFO 2025-07-22 17:22:59,008 basehttp 20740 25956 "GET /accounts/register/ HTTP/1.1" 200 9874
INFO 2025-07-22 17:26:57,634 basehttp 20740 25120 "GET /accounts/register/ HTTP/1.1" 200 9874
INFO 2025-07-22 17:26:58,302 basehttp 20740 25120 "GET /static/css/auth.css HTTP/1.1" 200 9505
INFO 2025-07-22 17:27:11,059 basehttp 20740 25120 "GET /privacy/ HTTP/1.1" 200 18388
INFO 2025-07-22 17:27:11,245 basehttp 20740 25120 "GET /static/css/pages/legal.css HTTP/1.1" 304 0
INFO 2025-07-22 17:27:11,826 basehttp 20740 25120 "GET /api/v1/sports/ HTTP/1.1" 200 2109
WARNING 2025-07-22 17:27:11,940 log 20740 25120 Not Found: /api/v1/sports/events/
WARNING 2025-07-22 17:27:11,942 basehttp 20740 25120 "GET /api/v1/sports/events/?status=live HTTP/1.1" 404 23
INFO 2025-07-22 17:27:19,627 basehttp 20740 25120 "GET /terms/ HTTP/1.1" 200 17181
INFO 2025-07-22 17:27:25,801 basehttp 20740 25120 "GET /accounts/register/ HTTP/1.1" 200 9874
INFO 2025-07-22 17:27:28,926 basehttp 20740 5180 "GET /terms/ HTTP/1.1" 200 17181
WARNING 2025-07-22 17:27:46,723 log 20740 16888 Not Found: /live-score/
WARNING 2025-07-22 17:27:46,830 basehttp 20740 16888 "GET /live-score/ HTTP/1.1" 404 4275
WARNING 2025-07-22 17:27:46,997 log 20740 13396 Not Found: /live-score/
INFO 2025-07-22 17:27:47,580 basehttp 20740 13396 - Broken pipe from ('127.0.0.1', 63635)
WARNING 2025-07-22 17:35:23,194 log 20740 16608 Not Found: /accounts/password_reset/
WARNING 2025-07-22 17:35:23,206 basehttp 20740 16608 "GET /accounts/password_reset/ HTTP/1.1" 404 6101
INFO 2025-07-22 17:35:28,718 basehttp 20740 16608 "GET /accounts/verify/ HTTP/1.1" 302 0
INFO 2025-07-22 17:35:28,751 basehttp 20740 16608 "GET /accounts/login/?next=/accounts/verify/ HTTP/1.1" 200 5771
INFO 2025-07-22 17:35:28,844 basehttp 20740 16608 "GET /static/css/auth.css HTTP/1.1" 200 11962
WARNING 2025-07-22 17:36:56,419 log 20740 16608 Not Found: /accounts/password_reset/
WARNING 2025-07-22 17:36:56,421 basehttp 20740 16608 "GET /accounts/password_reset/ HTTP/1.1" 404 6101
WARNING 2025-07-22 17:37:31,224 log 24932 27432 Not Found: /accounts/password_reset/
WARNING 2025-07-22 17:37:31,480 log 24932 27432 Not Found: /accounts/password_reset_confirm/
WARNING 2025-07-22 17:37:31,716 log 24932 27432 Not Found: /accounts/password_change/
