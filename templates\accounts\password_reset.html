{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset - Betzide!</title>
    <link rel="stylesheet" href="{% static 'css/base.css' %}">
    <link rel="stylesheet" href="{% static 'css/auth.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="auth-body">
    <!-- Back to Home -->
    <div class="back-to-home">
        <a href="{% url 'home' %}" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Back to home
        </a>
    </div>

    <!-- Main Auth Container -->
    <div class="auth-container">
        <div class="auth-card">
            <!-- Left Side - Illustration -->
            <div class="auth-illustration">
                <div class="illustration-content">
                    <div class="colorful-shapes">
                        <div class="shape shape-1"></div>
                        <div class="shape shape-2"></div>
                        <div class="shape shape-3"></div>
                        <div class="shape shape-4"></div>
                        <div class="shape shape-5"></div>
                    </div>
                    <div class="character">
                        <div class="character-body"></div>
                        <div class="character-head"></div>
                        <div class="character-arm-left"></div>
                        <div class="character-arm-right"></div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Password Reset Form -->
            <div class="auth-form-section">
                <!-- Logo -->
                <div class="auth-logo">
                    <span class="logo-text">B!</span>
                </div>

                <!-- Form Header -->
                <div class="auth-header">
                    <h2>Reset Password</h2>
                    <p>Enter your phone number and we'll send you a verification code</p>
                </div>

                <!-- Password Reset Form -->
                <form method="post" class="auth-form">
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                        <div class="error-message">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}

                    <!-- Phone Number Field -->
                    <div class="form-group">
                        <label for="{{ form.phone_number.id_for_label }}">Phone Number</label>
                        <div class="phone-input-group">
                            <div class="country-code">
                                <span class="flag-emoji">🇰🇪</span>
                                <span>+254</span>
                            </div>
                            {{ form.phone_number }}
                        </div>
                        {% if form.phone_number.errors %}
                            <div class="field-error">
                                {{ form.phone_number.errors }}
                            </div>
                        {% endif %}
                        {% if form.phone_number.help_text %}
                            <div class="field-help">{{ form.phone_number.help_text }}</div>
                        {% endif %}
                    </div>

                    <!-- Reset Button -->
                    <button type="submit" class="auth-btn auth-btn-primary">
                        Send Reset Code
                    </button>
                </form>

                <!-- Additional Links -->
                <div class="auth-links">
                    <div class="login-link">
                        <span>Remember your password?</span>
                        <a href="{% url 'accounts:login' %}">Login</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Form validation and enhancement
        document.addEventListener('DOMContentLoaded', function() {
            const phoneInput = document.getElementById('{{ form.phone_number.id_for_label }}');

            // Format phone number input
            if (phoneInput) {
                phoneInput.addEventListener('input', function(e) {
                    let value = e.target.value.replace(/\D/g, '');
                    if (value.startsWith('254')) {
                        value = value.substring(3);
                    }
                    if (value.startsWith('0')) {
                        value = value.substring(1);
                    }
                    e.target.value = value;
                });
            }
        });
    </script>
</body>
</html>