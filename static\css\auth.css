/* Authentication Pages Styles */

/* Body and Layout */
.auth-body {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #1a1a1a;
    min-height: 100vh;
    color: #ffffff;
    overflow-x: hidden;
}

/* Back to Home Link */
.back-to-home {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 10;
}

.back-link {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #b0b0b0;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.back-link:hover {
    color: #4CAF50;
}

/* Main Auth Container */
.auth-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.auth-card {
    display: flex;
    background: #2a2a2a;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    border: 1px solid #404040;
    max-width: 900px;
    width: 100%;
    min-height: 600px;
}

.register-card {
    max-width: 1000px;
    min-height: 700px;
}

/* Illustration Side */
.auth-illustration {
    flex: 1;
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    min-height: 600px;
}

.illustration-content {
    position: relative;
    width: 300px;
    height: 300px;
}

/* Colorful Shapes */
.colorful-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    border-radius: 50%;
    opacity: 0.8;
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #66BB6A, #4CAF50);
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #81C784, #66BB6A);
    top: 20%;
    right: 15%;
    animation-delay: 1s;
}

.shape-3 {
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, #A5D6A7, #81C784);
    bottom: 30%;
    left: 5%;
    animation-delay: 2s;
}

.shape-4 {
    width: 70px;
    height: 70px;
    background: linear-gradient(45deg, #388E3C, #2E7D32);
    bottom: 10%;
    right: 20%;
    animation-delay: 3s;
}

.shape-5 {
    width: 90px;
    height: 90px;
    background: linear-gradient(45deg, #43A047, #388E3C);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-delay: 4s;
}

.shape-6 {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #C8E6C9, #A5D6A7);
    top: 60%;
    right: 10%;
    animation-delay: 5s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* Character */
.character {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
}

.character-body {
    width: 80px;
    height: 120px;
    background: linear-gradient(45deg, #2a2a2a, #1a1a1a);
    border-radius: 40px;
    position: relative;
}

.character-head {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #FFD54F, #FFC107);
    border-radius: 50%;
    position: absolute;
    top: -30px;
    left: 10px;
}

.character-arm-left,
.character-arm-right {
    width: 30px;
    height: 80px;
    background: linear-gradient(45deg, #4CAF50, #388E3C);
    border-radius: 15px;
    position: absolute;
    top: 20px;
}

.character-arm-left {
    left: -25px;
    transform: rotate(-30deg);
}

.character-arm-right {
    right: -25px;
    transform: rotate(30deg);
}

.character-trophy {
    width: 40px;
    height: 50px;
    background: linear-gradient(45deg, #FFD700, #FFC107);
    border-radius: 20px 20px 5px 5px;
    position: absolute;
    top: -10px;
    right: -60px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Form Section */
.auth-form-section {
    flex: 1;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background: #1a1a1a;
}

.register-form-section {
    padding: 30px;
}

/* Logo */
.auth-logo {
    text-align: center;
    margin-bottom: 30px;
}

.logo-text {
    font-size: 48px;
    font-weight: bold;
    background: linear-gradient(45deg, #4CAF50, #45a049);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Form Header */
.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-header h2 {
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #fff;
}

.auth-header p {
    color: #b0b0b0;
    margin: 0;
    font-size: 14px;
}

/* Form Styles */
.auth-form {
    width: 100%;
}

.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: flex;
    gap: 15px;
}

.half-width {
    flex: 1;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #fff;
    font-size: 14px;
    font-weight: 500;
}

/* Input Styles */
.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"],
.form-group input[type="date"],
.form-group input[type="tel"] {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #404040;
    border-radius: 8px;
    background: #2a2a2a;
    color: #ffffff;
    font-size: 14px;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus {
    outline: none;
    border-color: #4CAF50;
    background: #333333;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.form-group input::placeholder {
    color: #b0b0b0;
}

/* Phone Input Group */
.phone-input-group {
    display: flex;
    border: 1px solid #404040;
    border-radius: 8px;
    background: #2a2a2a;
    overflow: hidden;
    transition: all 0.3s ease;
}

.phone-input-group:focus-within {
    border-color: #4CAF50;
    background: #333333;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.country-code {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: #333333;
    border-right: 1px solid #404040;
    color: #ffffff;
    font-size: 14px;
}

.flag-emoji {
    font-size: 18px;
    line-height: 1;
}

.phone-input-group input {
    border: none !important;
    background: transparent !important;
    box-shadow: none !important;
}

/* Password Input Group */
.password-input-group {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #b0b0b0;
    cursor: pointer;
    padding: 4px;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #4CAF50;
}

/* Checkbox Styles */
.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.custom-checkbox {
    width: 18px;
    height: 18px;
    margin: 0;
    flex-shrink: 0;
}

.checkbox-label {
    font-size: 13px;
    color: #b0b0b0;
    line-height: 1.4;
    margin: 0;
}

.checkbox-label a {
    color: #4CAF50;
    text-decoration: none;
}

.checkbox-label a:hover {
    text-decoration: underline;
}

/* Button Styles */
.auth-btn {
    width: 100%;
    padding: 14px 20px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.auth-btn-primary {
    background: #4CAF50;
    color: #ffffff;
}

.auth-btn-primary:hover {
    background: #45a049;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
}

.auth-btn-secondary {
    background: #404040;
    color: #ffffff;
}

.auth-btn-secondary:hover {
    background: #505050;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(64, 64, 64, 0.3);
}

/* Auth Links */
.auth-links {
    margin-top: 30px;
    text-align: center;
}

.register-link,
.login-link {
    margin-bottom: 15px;
}

.register-link span,
.login-link span {
    color: #b0b0b0;
    font-size: 14px;
}

.register-link a,
.login-link a,
.forgot-password a {
    color: #4CAF50;
    text-decoration: none;
    font-weight: 500;
    margin-left: 5px;
}

.register-link a:hover,
.login-link a:hover,
.forgot-password a:hover {
    text-decoration: underline;
}

.forgot-password {
    margin-top: 15px;
}

.forgot-password a {
    color: #b0b0b0;
    font-size: 14px;
    margin-left: 0;
}

/* Error Messages */
.error-message,
.field-error {
    background: rgba(244, 67, 54, 0.1);
    border: 1px solid rgba(244, 67, 54, 0.3);
    color: #ff6b6b;
    padding: 10px 12px;
    border-radius: 6px;
    font-size: 13px;
    margin-top: 8px;
}

.error-message {
    margin-bottom: 20px;
}

/* Help Text */
.field-help {
    font-size: 12px;
    color: #b0b0b0;
    margin-top: 5px;
}

/* Success Content Styles */
.success-content {
    text-align: center;
    margin: 30px 0;
}

.success-icon {
    margin-bottom: 20px;
}

.success-icon i {
    font-size: 4rem;
    color: #4CAF50;
}

.success-message h3 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.success-message p {
    color: #b0b0b0;
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 20px;
}

.info-note {
    background: rgba(33, 150, 243, 0.1);
    border: 1px solid rgba(33, 150, 243, 0.3);
    border-radius: 8px;
    padding: 15px;
    margin: 20px 0;
    display: flex;
    align-items: flex-start;
    gap: 12px;
    text-align: left;
}

.info-note i {
    color: #2196F3;
    font-size: 1.2rem;
    margin-top: 2px;
    flex-shrink: 0;
}

.info-note p {
    color: #ffffff;
    font-size: 0.9rem;
    margin: 0;
    line-height: 1.5;
}

/* Auth Actions */
.auth-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 20px;
}

.auth-actions .auth-btn {
    margin-top: 0;
}

/* Verification Specific Styles */
.verification-info {
    text-align: center;
    margin: 20px 0;
}

.phone-display {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: #2a2a2a;
    padding: 12px 20px;
    border-radius: 8px;
    border: 1px solid #404040;
    color: #4CAF50;
    font-weight: 500;
}

.phone-display i {
    font-size: 1.2rem;
}

.resend-section {
    text-align: center;
    margin-top: 30px;
}

.resend-section p {
    color: #b0b0b0;
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.resend-btn {
    background: none;
    border: none;
    color: #4CAF50;
    text-decoration: underline;
    cursor: pointer;
    font-size: 0.9rem;
    padding: 5px 10px;
    transition: color 0.3s ease;
}

.resend-btn:hover {
    color: #45a049;
}

.resend-btn:disabled {
    color: #666;
    cursor: not-allowed;
    text-decoration: none;
}

.resend-message {
    margin-top: 15px;
    padding: 10px;
    border-radius: 6px;
    font-size: 0.9rem;
}

.resend-message .text-success {
    color: #4CAF50;
}

.resend-message .text-danger {
    color: #f44336;
}

.resend-message .text-info {
    color: #2196F3;
    font-weight: 600;
    margin-top: 5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-card {
        flex-direction: column;
        margin: 10px;
        min-height: auto;
    }
    
    .register-card {
        min-height: auto;
    }
    
    .auth-illustration {
        min-height: 200px;
        flex: none;
    }
    
    .illustration-content {
        width: 200px;
        height: 200px;
    }
    
    .auth-form-section {
        padding: 30px 20px;
    }
    
    .register-form-section {
        padding: 20px;
    }
    
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .auth-header h2 {
        font-size: 24px;
    }
    
    .logo-text {
        font-size: 36px;
    }
    
    .back-to-home {
        position: relative;
        top: 10px;
        left: 10px;
        margin-bottom: 10px;
    }
}
