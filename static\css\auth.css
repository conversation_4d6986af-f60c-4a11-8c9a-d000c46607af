/* Authentication Pages Styles */

/* Body and Layout */
.auth-body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    min-height: 100vh;
    color: #fff;
    overflow-x: hidden;
}

/* Back to Home Link */
.back-to-home {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 10;
}

.back-link {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.back-link:hover {
    color: #fff;
}

/* Main Auth Container */
.auth-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.auth-card {
    display: flex;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    max-width: 900px;
    width: 100%;
    min-height: 600px;
}

.register-card {
    max-width: 1000px;
    min-height: 700px;
}

/* Illustration Side */
.auth-illustration {
    flex: 1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    min-height: 600px;
}

.illustration-content {
    position: relative;
    width: 300px;
    height: 300px;
}

/* Colorful Shapes */
.colorful-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    border-radius: 50%;
    opacity: 0.8;
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #48dbfb, #0abde3);
    top: 20%;
    right: 15%;
    animation-delay: 1s;
}

.shape-3 {
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, #ff9ff3, #f368e0);
    bottom: 30%;
    left: 5%;
    animation-delay: 2s;
}

.shape-4 {
    width: 70px;
    height: 70px;
    background: linear-gradient(45deg, #54a0ff, #2e86de);
    bottom: 10%;
    right: 20%;
    animation-delay: 3s;
}

.shape-5 {
    width: 90px;
    height: 90px;
    background: linear-gradient(45deg, #5f27cd, #341f97);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-delay: 4s;
}

.shape-6 {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #00d2d3, #54a0ff);
    top: 60%;
    right: 10%;
    animation-delay: 5s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* Character */
.character {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
}

.character-body {
    width: 80px;
    height: 120px;
    background: linear-gradient(45deg, #2c3e50, #34495e);
    border-radius: 40px;
    position: relative;
}

.character-head {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #f39c12, #e67e22);
    border-radius: 50%;
    position: absolute;
    top: -30px;
    left: 10px;
}

.character-arm-left,
.character-arm-right {
    width: 30px;
    height: 80px;
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    border-radius: 15px;
    position: absolute;
    top: 20px;
}

.character-arm-left {
    left: -25px;
    transform: rotate(-30deg);
}

.character-arm-right {
    right: -25px;
    transform: rotate(30deg);
}

.character-trophy {
    width: 40px;
    height: 50px;
    background: linear-gradient(45deg, #f1c40f, #f39c12);
    border-radius: 20px 20px 5px 5px;
    position: absolute;
    top: -10px;
    right: -60px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Form Section */
.auth-form-section {
    flex: 1;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background: rgba(255, 255, 255, 0.02);
}

.register-form-section {
    padding: 30px;
}

/* Logo */
.auth-logo {
    text-align: center;
    margin-bottom: 30px;
}

.logo-text {
    font-size: 48px;
    font-weight: bold;
    background: linear-gradient(45deg, #feca57, #ff9ff3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Form Header */
.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-header h2 {
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #fff;
}

.auth-header p {
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
    font-size: 14px;
}

/* Form Styles */
.auth-form {
    width: 100%;
}

.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: flex;
    gap: 15px;
}

.half-width {
    flex: 1;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #fff;
    font-size: 14px;
    font-weight: 500;
}

/* Input Styles */
.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"],
.form-group input[type="date"],
.form-group input[type="tel"] {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    color: #fff;
    font-size: 14px;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus {
    outline: none;
    border-color: #4CAF50;
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.form-group input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

/* Phone Input Group */
.phone-input-group {
    display: flex;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    overflow: hidden;
    transition: all 0.3s ease;
}

.phone-input-group:focus-within {
    border-color: #4CAF50;
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.country-code {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.05);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 14px;
}

.flag-emoji {
    font-size: 18px;
    line-height: 1;
}

.phone-input-group input {
    border: none !important;
    background: transparent !important;
    box-shadow: none !important;
}

/* Password Input Group */
.password-input-group {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    cursor: pointer;
    padding: 4px;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #fff;
}

/* Checkbox Styles */
.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.custom-checkbox {
    width: 18px;
    height: 18px;
    margin: 0;
    flex-shrink: 0;
}

.checkbox-label {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
    margin: 0;
}

.checkbox-label a {
    color: #4CAF50;
    text-decoration: none;
}

.checkbox-label a:hover {
    text-decoration: underline;
}

/* Button Styles */
.auth-btn {
    width: 100%;
    padding: 14px 20px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.auth-btn-primary {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: #fff;
}

.auth-btn-primary:hover {
    background: linear-gradient(45deg, #45a049, #3d8b40);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
}

/* Auth Links */
.auth-links {
    margin-top: 30px;
    text-align: center;
}

.register-link,
.login-link {
    margin-bottom: 15px;
}

.register-link span,
.login-link span {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
}

.register-link a,
.login-link a,
.forgot-password a {
    color: #4CAF50;
    text-decoration: none;
    font-weight: 500;
    margin-left: 5px;
}

.register-link a:hover,
.login-link a:hover,
.forgot-password a:hover {
    text-decoration: underline;
}

.forgot-password {
    margin-top: 15px;
}

.forgot-password a {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    margin-left: 0;
}

/* Error Messages */
.error-message,
.field-error {
    background: rgba(244, 67, 54, 0.1);
    border: 1px solid rgba(244, 67, 54, 0.3);
    color: #ff6b6b;
    padding: 10px 12px;
    border-radius: 6px;
    font-size: 13px;
    margin-top: 8px;
}

.error-message {
    margin-bottom: 20px;
}

/* Help Text */
.field-help {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    margin-top: 5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-card {
        flex-direction: column;
        margin: 10px;
        min-height: auto;
    }
    
    .register-card {
        min-height: auto;
    }
    
    .auth-illustration {
        min-height: 200px;
        flex: none;
    }
    
    .illustration-content {
        width: 200px;
        height: 200px;
    }
    
    .auth-form-section {
        padding: 30px 20px;
    }
    
    .register-form-section {
        padding: 20px;
    }
    
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .auth-header h2 {
        font-size: 24px;
    }
    
    .logo-text {
        font-size: 36px;
    }
    
    .back-to-home {
        position: relative;
        top: 10px;
        left: 10px;
        margin-bottom: 10px;
    }
}
