{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/pages/home.css' %}">
<link rel="stylesheet" href="{% static 'css/sports.css' %}">
{% endblock %}

{% block content %}
<!-- Promotional Banner -->
<div class="promo-banner">
    <div class="promo-content">
        <div class="promo-image">
            {% if sport.icon %}
            <i class="{{ sport.icon }}"></i>
            {% else %}
            <i class="fas fa-trophy"></i>
            {% endif %}
        </div>
        <div class="promo-text">
            <h2>{{ sport.name }}</h2>
            <p>Bet on exciting <strong>{{ sport.name }}</strong> matches with the best odds!</p>
        </div>
        <div class="promo-action">
            <a href="{% url 'sports:list' %}" class="btn btn-outline">All Sports</a>
            <button class="promo-close">&times;</button>
        </div>
    </div>
</div>

<!-- Event Navigation Tabs -->
<div class="event-tabs">
    <div class="tab-list">
        <button class="tab-btn active" data-tab="live">Live</button>
        <button class="tab-btn" data-tab="upcoming">Upcoming</button>
        <button class="tab-btn" data-tab="today">Today</button>
        <button class="tab-btn" data-tab="tomorrow">Tomorrow</button>
    </div>

    <div class="event-filters">
        <div class="filter-group">
            <button class="filter-btn">
                <i class="fas fa-filter"></i>
                Filters
            </button>
        </div>
        <div class="filter-group">
            <select class="filter-select">
                <option>All Leagues</option>
                {% for league in leagues %}
                <option value="{{ league }}">{{ league }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="filter-group">
            <select class="filter-select">
                <option>All Markets</option>
                <option>Match Result</option>
                <option>Over/Under</option>
            </select>
        </div>
    </div>
</div>

    <!-- Filters and Search -->
    <div class="filters-section">
        <form method="get" class="filters-form">
            <div class="filter-group">
                <label for="search">Search:</label>
                <input type="text" id="search" name="search" value="{{ search_query }}" 
                       placeholder="Team names, leagues...">
            </div>
            <div class="filter-group">
                <label for="status">Status:</label>
                <select id="status" name="status">
                    <option value="all" {% if status_filter == 'all' %}selected{% endif %}>All Events</option>
                    <option value="upcoming" {% if status_filter == 'upcoming' %}selected{% endif %}>Upcoming</option>
                    <option value="live" {% if status_filter == 'live' %}selected{% endif %}>Live</option>
                </select>
            </div>
            <button type="submit" class="btn btn-primary">Filter</button>
            {% if search_query or status_filter != 'all' %}
            <a href="{% url 'sports:sport_detail' sport.slug %}" class="btn btn-secondary">Clear</a>
            {% endif %}
        </form>
    </div>

    <!-- Events List -->
    <div class="events-section">
        {% if events %}
        <div class="events-header">
            <h2>Events ({{ events.paginator.count }})</h2>
        </div>
        
        <div class="events-list">
            {% for event in events %}
            <div class="event-card" data-event-id="{{ event.id }}">
                <div class="event-info">
                    <div class="event-teams">
                        <div class="team home-team">{{ event.home_team }}</div>
                        <div class="vs">vs</div>
                        <div class="team away-team">{{ event.away_team }}</div>
                    </div>
                    <div class="event-details">
                        {% if event.league %}
                        <span class="league">{{ event.league }}</span>
                        {% endif %}
                        <span class="start-time">{{ event.start_time|date:"M d, H:i" }}</span>
                        <span class="status status-{{ event.status }}">{{ event.get_status_display }}</span>
                    </div>
                    {% if event.get_score_display %}
                    <div class="event-score">{{ event.get_score_display }}</div>
                    {% endif %}
                </div>
                
                <!-- Markets Preview -->
                <div class="markets-preview">
                    {% for market in event.markets.all|slice:":3" %}
                    <div class="market-preview">
                        <div class="market-name">{{ market.name }}</div>
                        <div class="odds-preview">
                            {% for odds in market.odds.all|slice:":3" %}
                            <button class="odds-btn"
                                    data-odds="{{ odds.odds_value }}"
                                    data-selection="{{ odds.selection }}"
                                    data-event-id="{{ event.id }}"
                                    data-event-name="{{ event.home_team }} vs {{ event.away_team }}"
                                    data-market-type="{{ market.market_type }}"
                                    data-market-name="{{ market.name }}"
                                    data-odds-id="{{ odds.id }}"
                                    data-market-id="{{ market.id }}">
                                <span class="selection">{{ odds.selection }}</span>
                                <span class="odds-value">{{ odds.odds_value }}</span>
                            </button>
                            {% endfor %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <div class="event-actions">
                    <a href="{% url 'sports:event_detail' event.id %}" class="btn btn-primary">
                        View All Markets
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if events.has_other_pages %}
        <div class="pagination-container">
            <div class="pagination">
                {% if events.has_previous %}
                <a href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}" class="page-link">First</a>
                <a href="?page={{ events.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}" class="page-link">Previous</a>
                {% endif %}
                
                <span class="page-info">
                    Page {{ events.number }} of {{ events.paginator.num_pages }}
                </span>
                
                {% if events.has_next %}
                <a href="?page={{ events.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}" class="page-link">Next</a>
                <a href="?page={{ events.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}" class="page-link">Last</a>
                {% endif %}
            </div>
        </div>
        {% endif %}
        
        {% else %}
        <div class="no-events">
            <i class="fas fa-calendar-times"></i>
            <h3>No events found</h3>
            {% if search_query or status_filter != 'all' %}
            <p>Try adjusting your search criteria or <a href="{% url 'sports:sport_detail' sport.slug %}">view all events</a>.</p>
            {% else %}
            <p>There are no events available for {{ sport.name }} at the moment.</p>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Betslip Component -->
<div class="betslip-container" id="betslip-container">
    <div class="betslip" id="betslip">
        <div class="betslip-header">
            <h3>Betslip (<span id="betslip-count">0</span>)</h3>
            <button class="betslip-clear" id="clear-betslip">Clear All</button>
        </div>

        <div class="betslip-content" id="betslip-content">
            <div class="betslip-empty" id="betslip-empty">
                <i class="fas fa-ticket-alt"></i>
                <p>Your betslip is empty</p>
                <small>Click on odds to add selections</small>
            </div>

            <div class="betslip-selections" id="betslip-selections">
                <!-- Dynamic selections will be added here -->
            </div>
        </div>

        <div class="betslip-footer" id="betslip-footer" style="display: none;">
            <div class="betslip-totals">
                <div class="total-odds">
                    <span>Total Odds:</span>
                    <span id="total-odds">0.00</span>
                </div>
                <div class="potential-win">
                    <span>Potential Win:</span>
                    <span id="potential-win">KES 0.00</span>
                </div>
            </div>

            <div class="stake-input">
                <label for="stake-amount">Stake Amount (KES)</label>
                <input type="number" id="stake-amount" placeholder="100" min="1" step="1">
            </div>

            {% if user.is_authenticated %}
            <div class="user-balance">
                <span>Balance: KES {{ user.balance|default:"0.00" }}</span>
                {% if user.balance < 100 %}
                <a href="{% url 'payments:deposit' %}" class="deposit-link">Deposit</a>
                {% endif %}
            </div>

            <button class="place-bet-btn" id="place-bet-btn">
                <i class="fas fa-check"></i>
                Place Bet
            </button>
            {% else %}
            <div class="login-prompt">
                <p>Login to place bets</p>
                <a href="{% url 'accounts:login' %}" class="btn btn-primary">Login</a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/pages/home.js' %}"></script>
<script src="{% static 'js/sports.js' %}"></script>
<script src="{% static 'js/betting.js' %}"></script>
{% endblock %}