/* Betika Theme - Dark theme with green accents */

:root {
    /* Betika Color Palette */
    --primary-dark: #1a1a1a;        /* Main background */
    --secondary-dark: #2d2d2d;      /* Card backgrounds */
    --tertiary-dark: #3a3a3a;       /* Lighter dark sections */
    --accent-green: #4CAF50;        /* Primary green */
    --accent-light-green: #66BB6A;  /* Hover states */
    --accent-dark-green: #388E3C;   /* Active states */
    --text-primary: #ffffff;        /* Primary text */
    --text-secondary: #b0b0b0;      /* Secondary text */
    --text-muted: #808080;          /* Muted text */
    --border-color: #404040;        /* Borders and dividers */
    --warning-orange: #FF9800;      /* Warnings and highlights */
    --danger-red: #f44336;          /* Error states */
    --info-blue: #2196F3;           /* Info states */
    --success-green: #4CAF50;       /* Success states */
}

/* Base theme styles */
.betika-theme {
    background-color: var(--primary-dark);
    color: var(--text-primary);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header Navigation */
.header-nav {
    background-color: var(--secondary-dark);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    max-width: 1400px;
    margin: 0 auto;
    height: 60px;
}

/* Logo */
.logo {
    flex-shrink: 0;
}

.logo a {
    text-decoration: none;
    color: var(--text-primary);
}

.logo-text {
    font-size: 24px;
    font-weight: bold;
    color: var(--accent-green);
}

/* Main Navigation */
.main-nav {
    flex: 1;
    margin: 0 20px;
    overflow-x: auto;
}

.nav-tabs {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 5px;
    white-space: nowrap;
}

.nav-tab {
    display: inline-flex;
    align-items: center;
    padding: 8px 12px;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-tab:hover {
    background-color: var(--tertiary-dark);
    color: var(--text-primary);
}

.nav-tab.active {
    background-color: var(--accent-green);
    color: white;
}

.badge {
    background-color: var(--warning-orange);
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 5px;
    font-weight: bold;
}

/* User Section */
.user-section {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    gap: 15px;
}

.balance {
    background-color: var(--accent-green);
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: bold;
    font-size: 14px;
}

.user-dropdown {
    position: relative;
}

.user-btn {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.user-btn:hover {
    border-color: var(--accent-green);
    background-color: var(--tertiary-dark);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--secondary-dark);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    min-width: 150px;
    display: none;
    z-index: 1001;
}

.user-dropdown:hover .dropdown-menu {
    display: block;
}

.dropdown-menu a {
    display: block;
    padding: 10px 15px;
    color: var(--text-primary);
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.dropdown-menu a:hover {
    background-color: var(--tertiary-dark);
}

.auth-buttons {
    display: flex;
    gap: 10px;
}

/* Main Layout */
.main-layout {
    display: flex;
    min-height: calc(100vh - 60px);
}

/* Left Sidebar */
.sidebar {
    width: 250px;
    background-color: var(--secondary-dark);
    border-right: 1px solid var(--border-color);
    flex-shrink: 0;
    overflow-y: auto;
}

.sidebar-header {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--tertiary-dark);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 18px;
    cursor: pointer;
    margin-right: 10px;
}

.sidebar-title {
    font-weight: bold;
    color: var(--text-primary);
}

.sports-nav {
    padding: 10px 0;
}

.sports-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.sport-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.3s ease;
    gap: 12px;
}

.sport-link:hover {
    background-color: var(--tertiary-dark);
    color: var(--text-primary);
}

.sport-link.active {
    background-color: var(--accent-green);
    color: white;
    border-right: 3px solid var(--accent-light-green);
}

.sport-link i {
    width: 20px;
    text-align: center;
    font-size: 16px;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 20px;
    background-color: var(--primary-dark);
    overflow-y: auto;
}

/* Right Sidebar - Betslip */
.betslip-sidebar {
    width: 300px;
    background-color: var(--secondary-dark);
    border-left: 1px solid var(--border-color);
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
}

.betslip-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--tertiary-dark);
}

.betslip-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 18px;
}

.betslip-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 16px;
    cursor: pointer;
    display: none;
}

.load-betslip {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.load-betslip p {
    margin: 0 0 10px 0;
    color: var(--text-secondary);
    font-size: 14px;
}

.betslip-code-input {
    display: flex;
    gap: 10px;
}

.betslip-code-input input {
    flex: 1;
    padding: 8px 12px;
    background-color: var(--primary-dark);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    color: var(--text-primary);
    font-size: 14px;
}

.betslip-code-input input::placeholder {
    color: var(--text-muted);
}

.bet-selections {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    max-height: calc(100vh - 400px);
}

.empty-betslip {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-muted);
}

.empty-betslip i {
    font-size: 48px;
    margin-bottom: 15px;
    color: var(--border-color);
}

.empty-betslip p {
    margin: 0 0 5px 0;
    font-size: 16px;
}

.empty-betslip small {
    font-size: 12px;
}

/* Bet Selection Styling */
.bet-selection {
    background-color: var(--primary-dark);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    margin-bottom: 12px;
    padding: 12px;
    transition: all 0.3s ease;
}

.bet-selection:hover {
    border-color: var(--accent-green);
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

.selection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.selection-event {
    font-size: 12px;
    color: var(--text-muted);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.remove-selection {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.remove-selection:hover {
    background-color: var(--danger-red);
    color: white;
}

.selection-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.selection-name {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 14px;
}

.selection-odds {
    background-color: var(--accent-green);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: bold;
    font-size: 14px;
}

/* Odds change animation */
.selection-odds.odds-changed {
    animation: oddsChange 0.5s ease;
}

@keyframes oddsChange {
    0% { background-color: var(--warning-orange); }
    100% { background-color: var(--accent-green); }
}

/* Betslip counter in header */
.betslip-counter {
    background-color: var(--accent-green);
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 5px;
    font-weight: bold;
    display: none;
}

/* Mobile betslip toggle button */
.mobile-betslip-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: var(--accent-green);
    color: white;
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    font-size: 18px;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 1003;
    display: none;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.mobile-betslip-toggle:hover {
    background-color: var(--accent-dark-green);
    transform: scale(1.1);
}

.mobile-betslip-toggle .badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: var(--warning-orange);
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: bold;
    min-width: 18px;
    text-align: center;
}

.bet-totals {
    padding: 20px;
    border-top: 1px solid var(--border-color);
    background-color: var(--tertiary-dark);
}

.bet-type-selector {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.bet-type-selector label {
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--text-primary);
    cursor: pointer;
    font-size: 14px;
}

.stake-input {
    margin-bottom: 15px;
}

.stake-input label {
    display: block;
    margin-bottom: 5px;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 500;
}

.stake-input input {
    width: 100%;
    padding: 10px 12px;
    background-color: var(--primary-dark);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    color: var(--text-primary);
    font-size: 14px;
}

.potential-winnings {
    margin-bottom: 20px;
}

.winnings-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    color: var(--text-primary);
    font-size: 14px;
}

.winnings-row:last-child {
    font-weight: bold;
    font-size: 16px;
    color: var(--accent-green);
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    line-height: 1.4;
}

.btn-primary {
    background-color: var(--accent-green);
    color: white;
}

.btn-primary:hover {
    background-color: var(--accent-dark-green);
}

.btn-outline {
    background-color: transparent;
    color: var(--accent-green);
    border: 1px solid var(--accent-green);
}

.btn-outline:hover {
    background-color: var(--accent-green);
    color: white;
}

.btn-place-bet {
    width: 100%;
    margin-bottom: 10px;
    padding: 12px;
    font-size: 16px;
    font-weight: bold;
}

.btn-clear {
    width: 100%;
    padding: 8px;
    font-size: 14px;
}

/* Messages */
.messages-container {
    margin-bottom: 20px;
}

.alert {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-radius: 4px;
    margin-bottom: 10px;
    position: relative;
}

.alert i {
    margin-right: 10px;
}

.alert-success {
    background-color: rgba(76, 175, 80, 0.1);
    border: 1px solid var(--success-green);
    color: var(--success-green);
}

.alert-error {
    background-color: rgba(244, 67, 54, 0.1);
    border: 1px solid var(--danger-red);
    color: var(--danger-red);
}

.alert-warning {
    background-color: rgba(255, 152, 0, 0.1);
    border: 1px solid var(--warning-orange);
    color: var(--warning-orange);
}

.alert-info {
    background-color: rgba(33, 150, 243, 0.1);
    border: 1px solid var(--info-blue);
    color: var(--info-blue);
}

.alert-close {
    position: absolute;
    right: 10px;
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    font-size: 18px;
}

/* Mobile Responsiveness */
@media (max-width: 1024px) {
    .betslip-sidebar {
        position: fixed;
        right: -300px;
        top: 60px;
        height: calc(100vh - 60px);
        z-index: 1002;
        transition: right 0.3s ease;
    }
    
    .betslip-sidebar.open {
        right: 0;
    }
    
    .betslip-toggle {
        display: block;
    }
}

@media (max-width: 768px) {
    .header-container {
        padding: 0 15px;
    }
    
    .main-nav {
        display: none;
    }
    
    .sidebar {
        position: fixed;
        left: -250px;
        top: 60px;
        height: calc(100vh - 60px);
        z-index: 1001;
        transition: left 0.3s ease;
    }
    
    .sidebar.open {
        left: 0;
    }
    
    .main-content {
        padding: 15px;
        padding-bottom: 100px; /* Space for mobile betslip toggle */
    }
    
    .mobile-overlay {
        position: fixed;
        top: 60px;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        display: none;
    }
    
    .mobile-overlay.active {
        display: block;
    }
    
    /* Show mobile betslip toggle on mobile */
    .mobile-betslip-toggle {
        display: flex;
    }
    
    /* Adjust betslip for mobile */
    .betslip-sidebar {
        width: 100%;
        right: -100%;
    }
    
    .betslip-sidebar.open {
        right: 0;
    }
    
    /* Improve bet selection styling on mobile */
    .bet-selection {
        padding: 10px;
        margin-bottom: 10px;
    }
    
    .selection-header {
        margin-bottom: 6px;
    }
    
    .selection-event {
        font-size: 11px;
    }
    
    .selection-name {
        font-size: 13px;
    }
    
    .selection-odds {
        padding: 3px 6px;
        font-size: 13px;
    }
    
    /* Adjust bet totals for mobile */
    .bet-totals {
        padding: 15px;
    }
    
    .stake-input input {
        padding: 8px 10px;
        font-size: 16px; /* Prevent zoom on iOS */
    }
    
    .btn-place-bet {
        padding: 14px;
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .header-container {
        padding: 0 10px;
    }
    
    .logo-text {
        font-size: 20px;
    }
    
    .user-section {
        gap: 10px;
    }
    
    .balance {
        font-size: 12px;
        padding: 4px 8px;
    }
    
    .main-content {
        padding: 10px;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--primary-dark);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}