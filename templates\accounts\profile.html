{% extends 'base.html' %}
{% load static %}

{% block title %}Profile - <PERSON><PERSON> Clone{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/pages/home.css' %}">
{% endblock %}

{% block content %}
<!-- Promotional Banner -->
<div class="promo-banner">
    <div class="promo-content">
        <div class="promo-image">
            <i class="fas fa-user"></i>
        </div>
        <div class="promo-text">
            <h2>My Profile</h2>
            <p>Manage your account settings and <strong>personal information</strong> securely!</p>
        </div>
        <div class="promo-action">
            <a href="{% url 'accounts:edit_profile' %}" class="btn btn-outline">Edit Profile</a>
            <button class="promo-close">&times;</button>
        </div>
    </div>
</div>

<!-- Event Navigation Tabs -->
<div class="event-tabs">
    <div class="tab-list">
        <button class="tab-btn active" data-tab="personal">Personal Info</button>
        <button class="tab-btn" data-tab="security">Security</button>
        <button class="tab-btn" data-tab="preferences">Preferences</button>
        <button class="tab-btn" data-tab="activity">Activity</button>
    </div>

    <div class="event-filters">
        <div class="filter-group">
            <div class="verification-status">
                {% if user.is_verified %}
                <span class="verified"><i class="fas fa-check-circle"></i> Verified</span>
                {% else %}
                <span class="unverified"><i class="fas fa-clock"></i> Pending Verification</span>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Events Content -->
<div class="events-content">
    <div class="tab-content active" id="personal">
        <div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3>My Profile</h3>
                    <a href="{% url 'accounts:edit_profile' %}" class="btn btn-primary btn-sm">
                        Edit Profile
                    </a>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Personal Information</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td>{{ user.full_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Phone:</strong></td>
                                    <td>
                                        {{ user.phone_number }}
                                        {% if user.is_verified %}
                                            <span class="badge bg-success">Verified</span>
                                        {% else %}
                                            <span class="badge bg-warning">Not Verified</span>
                                            <a href="{% url 'accounts:verify_account' %}" class="btn btn-link btn-sm">Verify Now</a>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>{{ user.email }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Date of Birth:</strong></td>
                                    <td>{{ user.date_of_birth|default:"Not provided" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Member Since:</strong></td>
                                    <td>{{ user.date_joined|date:"F d, Y" }}</td>
                                </tr>
                            </table>
                        </div>
                        
                        <div class="col-md-6">
                            <h5>Account Information</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Balance:</strong></td>
                                    <td class="text-success">KES {{ user.balance }}</td>
                                </tr>
                                <tr>
                                    <td><strong>KYC Status:</strong></td>
                                    <td>
                                        {% if profile.kyc_status == 'approved' %}
                                            <span class="badge bg-success">Approved</span>
                                        {% elif profile.kyc_status == 'in_review' %}
                                            <span class="badge bg-info">In Review</span>
                                        {% elif profile.kyc_status == 'rejected' %}
                                            <span class="badge bg-danger">Rejected</span>
                                        {% else %}
                                            <span class="badge bg-warning">Pending</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Can Bet:</strong></td>
                                    <td>
                                        {% if user.can_bet %}
                                            <span class="badge bg-success">Yes</span>
                                        {% else %}
                                            <span class="badge bg-danger">No</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Language:</strong></td>
                                    <td>{{ profile.get_preferred_language_display }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Location:</strong></td>
                                    <td>
                                        {% if profile.city and profile.county %}
                                            {{ profile.city }}, {{ profile.county }}
                                        {% else %}
                                            Not provided
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    {% if profile.address %}
                    <div class="row mt-3">
                        <div class="col-12">
                            <h5>Address</h5>
                            <p>{{ profile.address }}</p>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if profile.favorite_sports %}
                    <div class="row mt-3">
                        <div class="col-12">
                            <h5>Favorite Sports</h5>
                            <div class="d-flex flex-wrap gap-2">
                                {% for sport in profile.favorite_sports %}
                                    <span class="badge bg-secondary">{{ sport }}</span>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5>Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        {% if not user.is_verified %}
                            <a href="{% url 'accounts:verify_account' %}" class="btn btn-warning">
                                Verify Account
                            </a>
                        {% endif %}
                        
                        <a href="{% url 'accounts:edit_profile' %}" class="btn btn-primary">
                            Edit Profile
                        </a>
                        
                        <a href="{% url 'accounts:password_change' %}" class="btn btn-secondary">
                            Change Password
                        </a>
                        
                        <a href="/payments/" class="btn btn-success">
                            Manage Payments
                        </a>
                        
                        <a href="/betting/history/" class="btn btn-info">
                            Betting History
                        </a>
                    </div>
                </div>
            </div>
            
            {% if not user.can_bet %}
            <div class="card mt-3">
                <div class="card-header bg-warning">
                    <h6 class="mb-0">Account Restrictions</h6>
                </div>
                <div class="card-body">
                    <p class="small mb-2">You cannot place bets because:</p>
                    <ul class="small">
                        {% if not user.is_verified %}
                            <li>Account not verified</li>
                        {% endif %}
                        {% if not user.is_adult %}
                            <li>Must be 18 years or older</li>
                        {% endif %}
                        {% if user.is_suspended %}
                            <li>Account is suspended</li>
                        {% endif %}
                    </ul>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
        </div>
    </div>

    <div class="tab-content" id="security">
        <div class="empty-state">
            <i class="fas fa-shield-alt"></i>
            <h3>Security Settings</h3>
            <p>Manage your account security</p>
        </div>
    </div>

    <div class="tab-content" id="preferences">
        <div class="empty-state">
            <i class="fas fa-cog"></i>
            <h3>Preferences</h3>
            <p>Customize your betting experience</p>
        </div>
    </div>

    <div class="tab-content" id="activity">
        <div class="empty-state">
            <i class="fas fa-chart-line"></i>
            <h3>Account Activity</h3>
            <p>View your account activity</p>
        </div>
    </div>
</div>

<!-- Betslip Component -->
<div class="betslip-container" id="betslip-container">
    <div class="betslip" id="betslip">
        <div class="betslip-header">
            <h3>Betslip (<span id="betslip-count">0</span>)</h3>
            <button class="betslip-clear" id="clear-betslip">Clear All</button>
        </div>

        <div class="betslip-content" id="betslip-content">
            <div class="betslip-empty" id="betslip-empty">
                <i class="fas fa-ticket-alt"></i>
                <p>Your betslip is empty</p>
                <small>Click on odds to add selections</small>
            </div>

            <div class="betslip-selections" id="betslip-selections">
                <!-- Dynamic selections will be added here -->
            </div>
        </div>

        <div class="betslip-footer" id="betslip-footer" style="display: none;">
            <div class="betslip-totals">
                <div class="total-odds">
                    <span>Total Odds:</span>
                    <span id="total-odds">0.00</span>
                </div>
                <div class="potential-win">
                    <span>Potential Win:</span>
                    <span id="potential-win">KES 0.00</span>
                </div>
            </div>

            <div class="stake-input">
                <label for="stake-amount">Stake Amount (KES)</label>
                <input type="number" id="stake-amount" placeholder="100" min="1" step="1">
            </div>

            {% if user.is_authenticated %}
            <div class="user-balance">
                <span>Balance: KES {{ user.balance|default:"0.00" }}</span>
                {% if user.balance < 100 %}
                <a href="{% url 'payments:deposit' %}" class="deposit-link">Deposit</a>
                {% endif %}
            </div>

            <button class="place-bet-btn" id="place-bet-btn">
                <i class="fas fa-check"></i>
                Place Bet
            </button>
            {% else %}
            <div class="login-prompt">
                <p>Login to place bets</p>
                <a href="{% url 'accounts:login' %}" class="btn btn-primary">Login</a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.d-flex {
    display: flex;
}

.justify-content-between {
    justify-content: space-between;
}

.align-items-center {
    align-items: center;
}

.flex-wrap {
    flex-wrap: wrap;
}

.gap-2 {
    gap: 0.5rem;
}

.d-grid {
    display: grid;
}

.table-borderless {
    border: none;
}

.table-borderless td {
    border: none;
    padding: 0.5rem 0;
}

.badge {
    display: inline-block;
    padding: 0.25em 0.5em;
    font-size: 0.75em;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.375rem;
}

.bg-success {
    background-color: var(--success-color) !important;
    color: white;
}

.bg-warning {
    background-color: var(--warning-color) !important;
    color: white;
}

.bg-danger {
    background-color: var(--danger-color) !important;
    color: white;
}

.bg-info {
    background-color: var(--info-color) !important;
    color: white;
}

.bg-secondary {
    background-color: var(--text-muted) !important;
    color: white;
}

.text-success {
    color: var(--success-color) !important;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.btn-link {
    background: none;
    border: none;
    color: var(--primary-color);
    text-decoration: underline;
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.col-md-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
}

.col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
}

@media (max-width: 768px) {
    .col-md-4, .col-md-6, .col-md-8 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/pages/home.js' %}"></script>
{% endblock %}