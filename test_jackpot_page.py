#!/usr/bin/env python
"""
Simple test script to verify jackpot page functionality
"""

import os
import sys
import django
from django.test import Client
from django.contrib.auth import get_user_model

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'betika_clone.settings')
django.setup()

def test_jackpot_page():
    """Test that the jackpot page loads correctly"""
    client = Client()
    
    print("Testing jackpot page...")
    
    # Test anonymous access (should work)
    response = client.get('/jackpot/')
    print(f"Anonymous access status: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ Jackpot page loads successfully for anonymous users")
        
        # Check if jackpots are displayed
        content = response.content.decode('utf-8')
        if 'Mega Jackpot' in content:
            print("✅ Mega Jackpot is displayed")
        else:
            print("❌ Mega Jackpot not found in page content")
            
        if 'KES 15000000' in content or 'KES 15,000,000' in content:
            print("✅ Prize pool is displayed")
        else:
            print("❌ Prize pool not found in page content")
            # Debug: show what prize pool text is actually there
            import re
            prize_matches = re.findall(r'KES [0-9,]+', content)
            if prize_matches:
                print(f"Found prize pools: {prize_matches}")
            else:
                print("No KES amounts found in content")
            
        if 'Login to place bet' in content:
            print("✅ Login prompt is shown for anonymous users")
        else:
            print("❌ Login prompt not found")
            
    else:
        print(f"❌ Jackpot page failed to load: {response.status_code}")
        if hasattr(response, 'content'):
            print(f"Error content: {response.content.decode('utf-8')[:500]}")
    
    # Test with authenticated user
    User = get_user_model()
    try:
        user = User.objects.create_user(
            phone_number='0712345678',
            email='<EMAIL>',
            password='testpass123'
        )
        client.login(phone_number='0712345678', password='testpass123')
        
        response = client.get('/jackpot/')
        print(f"Authenticated access status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Jackpot page loads successfully for authenticated users")
            content = response.content.decode('utf-8')
            if 'Place Jackpot Bet' in content:
                print("✅ Place bet button is shown for authenticated users")
            else:
                print("❌ Place bet button not found for authenticated users")
        else:
            print(f"❌ Jackpot page failed for authenticated user: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing authenticated access: {e}")

if __name__ == '__main__':
    test_jackpot_page()
