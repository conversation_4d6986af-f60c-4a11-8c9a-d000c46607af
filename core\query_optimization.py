"""
Query optimization utilities for Django ORM
"""

from django.db.models import <PERSON>fe<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Av<PERSON>, <PERSON>, <PERSON>, F
from django.db.models.query import QuerySet
from django.utils import timezone
from typing import Dict, List, Optional, Any
import logging

logger = logging.getLogger(__name__)


class QueryOptimizer:
    """
    Utility class for optimizing Django ORM queries
    """
    
    @staticmethod
    def optimize_user_queries(queryset: QuerySet) -> QuerySet:
        """Optimize user-related queries"""
        return queryset.select_related('profile').prefetch_related(
            'transactions',
            'bets'
        )
    
    @staticmethod
    def optimize_bet_queries(queryset: QuerySet) -> QuerySet:
        """Optimize bet-related queries"""
        return queryset.select_related('user').prefetch_related(
            Prefetch(
                'selections',
                queryset=None  # Will be defined based on context
            )
        )
    
    @staticmethod
    def optimize_event_queries(queryset: QuerySet, include_markets: bool = False) -> QuerySet:
        """Optimize event-related queries"""
        optimized = queryset.select_related('sport')
        
        if include_markets:
            optimized = optimized.prefetch_related(
                Prefetch(
                    'markets',
                    queryset=None  # Will be defined based on context
                ),
                'markets__odds'
            )
        
        return optimized
    
    @staticmethod
    def optimize_odds_queries(queryset: QuerySet) -> QuerySet:
        """Optimize odds-related queries"""
        return queryset.select_related('market', 'market__event', 'market__event__sport')
    
    @staticmethod
    def get_user_betting_stats(user_id: int) -> Dict[str, Any]:
        """Get optimized user betting statistics"""
        from betting.models import Bet
        from django.db.models import Case, When, DecimalField
        
        stats = Bet.objects.filter(user_id=user_id).aggregate(
            total_bets=Count('id'),
            total_stake=Sum('stake'),
            total_winnings=Sum(
                Case(
                    When(status='won', then='actual_winnings'),
                    default=0,
                    output_field=DecimalField(max_digits=10, decimal_places=2)
                )
            ),
            won_bets=Count('id', filter=Q(status='won')),
            lost_bets=Count('id', filter=Q(status='lost')),
            pending_bets=Count('id', filter=Q(status='pending')),
            avg_stake=Avg('stake'),
            max_stake=Max('stake'),
            min_stake=Min('stake'),
            last_bet_date=Max('placed_at')
        )
        
        # Calculate win rate
        if stats['total_bets'] > 0:
            stats['win_rate'] = (stats['won_bets'] / stats['total_bets']) * 100
        else:
            stats['win_rate'] = 0
        
        # Calculate profit/loss
        stats['profit_loss'] = (stats['total_winnings'] or 0) - (stats['total_stake'] or 0)
        
        return stats
    
    @staticmethod
    def get_popular_sports(limit: int = 10) -> List[Dict[str, Any]]:
        """Get popular sports based on betting activity"""
        from sports.models import Sport
        from betting.models import BetSelection
        
        # Get sports with betting activity
        popular_sports = Sport.objects.annotate(
            total_bets=Count('events__markets__odds__betselection'),
            total_stake=Sum('events__markets__odds__betselection__bet__stake'),
            unique_bettors=Count(
                'events__markets__odds__betselection__bet__user',
                distinct=True
            )
        ).filter(
            total_bets__gt=0
        ).order_by('-total_bets')[:limit]
        
        return [
            {
                'id': sport.id,
                'name': sport.name,
                'slug': sport.slug,
                'total_bets': sport.total_bets,
                'total_stake': float(sport.total_stake or 0),
                'unique_bettors': sport.unique_bettors
            }
            for sport in popular_sports
        ]
    
    @staticmethod
    def get_live_events_optimized() -> QuerySet:
        """Get optimized queryset for live events"""
        from sports.models import Event, Market, Odds
        
        return Event.objects.filter(
            status='live'
        ).select_related('sport').prefetch_related(
            Prefetch(
                'markets',
                queryset=Market.objects.filter(is_active=True).prefetch_related(
                    Prefetch(
                        'odds',
                        queryset=Odds.objects.filter(is_active=True).order_by('display_order')
                    )
                )
            )
        ).order_by('start_time')
    
    @staticmethod
    def get_upcoming_events_optimized(sport_id: Optional[int] = None, limit: int = 50) -> QuerySet:
        """Get optimized queryset for upcoming events"""
        from sports.models import Event
        
        queryset = Event.objects.filter(
            status='upcoming',
            start_time__gte=timezone.now()
        ).select_related('sport')
        
        if sport_id:
            queryset = queryset.filter(sport_id=sport_id)
        
        return queryset.order_by('start_time')[:limit]
    
    @staticmethod
    def get_user_recent_bets_optimized(user_id: int, limit: int = 10) -> QuerySet:
        """Get optimized queryset for user's recent bets"""
        from betting.models import Bet, BetSelection
        
        return Bet.objects.filter(
            user_id=user_id
        ).select_related('user').prefetch_related(
            Prefetch(
                'selections',
                queryset=BetSelection.objects.select_related(
                    'odds', 'odds__market', 'odds__market__event'
                )
            )
        ).order_by('-placed_at')[:limit]
    
    @staticmethod
    def get_jackpot_entries_optimized(jackpot_id: int) -> QuerySet:
        """Get optimized queryset for jackpot entries"""
        from jackpot.models import JackpotEntry, JackpotPrediction
        
        return JackpotEntry.objects.filter(
            jackpot_id=jackpot_id
        ).select_related('user', 'jackpot').prefetch_related(
            Prefetch(
                'predictions',
                queryset=JackpotPrediction.objects.select_related(
                    'game', 'predicted_odds', 'predicted_odds__market'
                )
            )
        ).order_by('-created_at')
    
    @staticmethod
    def get_analytics_data_optimized(date_from: timezone.datetime, date_to: timezone.datetime) -> Dict[str, Any]:
        """Get optimized analytics data for a date range"""
        from analytics.models import DailyMetrics, UserActivity
        from betting.models import Bet
        from accounts.models import CustomUser
        
        # Get daily metrics
        daily_metrics = DailyMetrics.objects.filter(
            date__range=[date_from.date(), date_to.date()]
        ).aggregate(
            total_users=Sum('active_users'),
            total_bets=Sum('total_bets'),
            total_stake=Sum('total_stake'),
            total_winnings=Sum('total_winnings'),
            avg_bet_amount=Avg('average_bet_amount')
        )
        
        # Get user activity summary
        user_activity = UserActivity.objects.filter(
            activity_date__range=[date_from.date(), date_to.date()]
        ).values('activity_type').annotate(
            count=Count('id')
        )
        
        # Get betting trends
        betting_trends = Bet.objects.filter(
            placed_at__range=[date_from, date_to]
        ).extra(
            select={'day': 'DATE(placed_at)'}
        ).values('day').annotate(
            bet_count=Count('id'),
            total_stake=Sum('stake')
        ).order_by('day')
        
        return {
            'daily_metrics': daily_metrics,
            'user_activity': list(user_activity),
            'betting_trends': list(betting_trends),
            'date_range': {
                'from': date_from.isoformat(),
                'to': date_to.isoformat()
            }
        }


class DatabaseConnectionOptimizer:
    """
    Utilities for optimizing database connections
    """
    
    @staticmethod
    def get_connection_info():
        """Get database connection information"""
        from django.db import connection
        
        return {
            'vendor': connection.vendor,
            'queries_count': len(connection.queries),
            'settings': {
                'CONN_MAX_AGE': connection.settings_dict.get('CONN_MAX_AGE'),
                'OPTIONS': connection.settings_dict.get('OPTIONS', {})
            }
        }
    
    @staticmethod
    def reset_queries():
        """Reset query log (useful for debugging)"""
        from django.db import reset_queries
        reset_queries()
    
    @staticmethod
    def get_slow_queries(threshold_ms: int = 100):
        """Get queries that took longer than threshold"""
        from django.db import connection
        
        slow_queries = []
        for query in connection.queries:
            time_ms = float(query['time']) * 1000
            if time_ms > threshold_ms:
                slow_queries.append({
                    'sql': query['sql'],
                    'time_ms': time_ms
                })
        
        return slow_queries
