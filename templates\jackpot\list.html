{% extends 'base.html' %}
{% load static %}

{% block title %}Jackpots - Betika!{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/pages/home.css' %}">
<link rel="stylesheet" href="{% static 'css/jackpot.css' %}">
{% endblock %}

{% block content %}
<!-- Promotional Banner -->
<div class="promo-banner">
    <div class="promo-content">
        <div class="promo-image">
            <i class="fas fa-trophy"></i>
        </div>
        <div class="promo-text">
            <h2>Jackpots</h2>
            <p>Win big with our <strong>mega jackpots</strong> - predict and win millions!</p>
        </div>
        <div class="promo-action">
            <a href="{% url 'home' %}" class="btn btn-outline">Back to Home</a>
            <button class="promo-close">&times;</button>
        </div>
    </div>
</div>

<!-- Event Navigation Tabs -->
<div class="event-tabs">
    <div class="tab-list">
        <button class="tab-btn active" data-tab="mega-jackpot">Mega Jackpot</button>
        <button class="tab-btn" data-tab="midweek-jackpot">Midweek Jackpot</button>
        <button class="tab-btn" data-tab="sabatisha-jackpot">Sabatisha Jackpot</button>
        <button class="tab-btn" data-tab="my-entries">My Entries</button>
    </div>
    
    <div class="event-filters">
        <div class="filter-group">
            <button class="filter-btn">
                <i class="fas fa-history"></i>
                Previous Results
            </button>
        </div>
        <div class="filter-group">
            <select class="filter-select">
                <option>All Jackpots</option>
                <option>Active</option>
                <option>Upcoming</option>
                <option>Settled</option>
            </select>
        </div>
    </div>
</div>

<!-- Events Content -->
<div class="events-content">
    <div class="tab-content active" id="mega-jackpot">
        {% for jackpot in active_jackpots %}
        {% if jackpot.jackpot_type == 'mega' %}
        <div class="jackpot-card" data-jackpot-id="{{ jackpot.id }}">
            <div class="jackpot-header">
                <div class="jackpot-info">
                    <h2>Win KES {{ jackpot.current_prize_pool|floatformat:0 }} with {{ jackpot.total_games }} BOB</h2>
                    <p>Predict {{ jackpot.total_games }} games to win. Prizes for {{ jackpot.minimum_correct_predictions }}-{{ jackpot.total_games }} correct predictions.</p>
                    <div class="jackpot-meta">
                        <span>Expires on {{ jackpot.end_time|date:"d/m/Y H:i" }}</span>
                        <span class="jackpot-id">JP #: {{ jackpot.id|slice:":4" }}</span>
                    </div>
                </div>
                <div class="jackpot-countdown">
                    <div class="countdown-timer" data-end-time="{{ jackpot.end_time|date:'c' }}">
                        <div class="time-unit">
                            <span class="time-value" id="days-{{ jackpot.id }}">00</span>
                            <span class="time-label">Days</span>
                        </div>
                        <div class="time-unit">
                            <span class="time-value" id="hours-{{ jackpot.id }}">00</span>
                            <span class="time-label">Hours</span>
                        </div>
                        <div class="time-unit">
                            <span class="time-value" id="minutes-{{ jackpot.id }}">00</span>
                            <span class="time-label">Minutes</span>
                        </div>
                        <div class="time-unit">
                            <span class="time-value" id="seconds-{{ jackpot.id }}">00</span>
                            <span class="time-label">Seconds</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="jackpot-matches">
                <div class="matches-header">
                    <div class="match-column">No.</div>
                    <div class="match-column">Date & Time</div>
                    <div class="match-column">Match</div>
                    <div class="match-column">1(Home)</div>
                    <div class="match-column">X(Draw)</div>
                    <div class="match-column">2(Away)</div>
                </div>
                
                {% for game in jackpot.games.all %}
                <div class="match-row" data-game-id="{{ game.id }}">
                    <div class="match-number">{{ game.game_number }}</div>
                    <div class="match-datetime">
                        <div class="match-date">{{ game.event.start_time|date:"d/m/Y" }}</div>
                        <div class="match-time">{{ game.event.start_time|date:"H:i" }}</div>
                        <div class="match-league">{{ game.event.sport.name }} • {{ game.event.league|default:"League" }}</div>
                    </div>
                    <div class="match-teams">
                        <div class="home-team">{{ game.event.home_team }}</div>
                        <div class="away-team">{{ game.event.away_team }}</div>
                    </div>
                    
                    {% for odds in game.market.odds.all %}
                    {% if odds.selection == '1' %}
                    <div class="odds-cell">
                        <button class="odds-btn jackpot-odds" 
                                data-jackpot-id="{{ jackpot.id }}"
                                data-game-id="{{ game.id }}"
                                data-selection="{{ odds.selection }}"
                                data-odds="{{ odds.odds_value }}"
                                data-match="{{ game.event.home_team }} vs {{ game.event.away_team }}">
                            {{ odds.odds_value }}
                        </button>
                    </div>
                    {% elif odds.selection == 'X' %}
                    <div class="odds-cell">
                        <button class="odds-btn jackpot-odds" 
                                data-jackpot-id="{{ jackpot.id }}"
                                data-game-id="{{ game.id }}"
                                data-selection="{{ odds.selection }}"
                                data-odds="{{ odds.odds_value }}"
                                data-match="{{ game.event.home_team }} vs {{ game.event.away_team }}">
                            {{ odds.odds_value }}
                        </button>
                    </div>
                    {% elif odds.selection == '2' %}
                    <div class="odds-cell">
                        <button class="odds-btn jackpot-odds" 
                                data-jackpot-id="{{ jackpot.id }}"
                                data-game-id="{{ game.id }}"
                                data-selection="{{ odds.selection }}"
                                data-odds="{{ odds.odds_value }}"
                                data-match="{{ game.event.home_team }} vs {{ game.event.away_team }}">
                            {{ odds.odds_value }}
                        </button>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>
                {% endfor %}
            </div>
            
            <div class="jackpot-actions">
                <div class="auto-pick">
                    <button class="auto-pick-btn" data-jackpot-id="{{ jackpot.id }}">
                        <i class="fas fa-magic"></i> Auto Pick
                    </button>
                    <button class="clear-all-btn" data-jackpot-id="{{ jackpot.id }}">
                        Clear All <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="jackpot-summary">
                    <div class="picked-count">
                        <span>Picked</span>
                        <span id="picked-count-{{ jackpot.id }}">0</span>
                    </div>
                    <div class="combination">
                        <span>Combination</span>
                        <span id="combination-{{ jackpot.id }}">0</span>
                    </div>
                    <div class="total-stake">
                        <span>Total Stake</span>
                        <span id="total-stake-{{ jackpot.id }}">KES 0</span>
                    </div>
                </div>
                
                {% if user.is_authenticated %}
                <button class="place-jackpot-bet-btn" data-jackpot-id="{{ jackpot.id }}">
                    <i class="fas fa-check"></i>
                    Place Jackpot Bet
                </button>
                {% else %}
                <button class="login-to-bet-btn" onclick="window.location.href='{% url 'accounts:login' %}'">
                    Login to place bet
                </button>
                {% endif %}
            </div>
        </div>
        {% endif %}
        {% empty %}
        <div class="empty-state">
            <i class="fas fa-trophy"></i>
            <h3>No Active Mega Jackpots</h3>
            <p>Check back later for new jackpot opportunities</p>
        </div>
        {% endfor %}
    </div>
    
    <div class="tab-content" id="midweek-jackpot">
        {% for jackpot in active_jackpots %}
        {% if jackpot.jackpot_type == 'weekly' %}
        <div class="jackpot-card" data-jackpot-id="{{ jackpot.id }}">
            <!-- Similar structure as mega jackpot -->
            <div class="jackpot-header">
                <div class="jackpot-info">
                    <h2>Midweek Jackpot - KES {{ jackpot.current_prize_pool|floatformat:0 }}</h2>
                    <p>Predict {{ jackpot.total_games }} games to win big this week!</p>
                </div>
            </div>
            <div class="empty-state">
                <i class="fas fa-calendar-week"></i>
                <h3>Midweek Jackpot</h3>
                <p>Coming soon - check back for midweek opportunities</p>
            </div>
        </div>
        {% endif %}
        {% empty %}
        <div class="empty-state">
            <i class="fas fa-calendar-week"></i>
            <h3>No Midweek Jackpots</h3>
            <p>Check back later for midweek opportunities</p>
        </div>
        {% endfor %}
    </div>
    
    <div class="tab-content" id="sabatisha-jackpot">
        <div class="empty-state">
            <i class="fas fa-star"></i>
            <h3>Sabatisha Jackpot</h3>
            <p>Weekend jackpot coming soon</p>
        </div>
    </div>
    
    <div class="tab-content" id="my-entries">
        {% if user.is_authenticated %}
        <div class="empty-state">
            <i class="fas fa-ticket-alt"></i>
            <h3>My Jackpot Entries</h3>
            <p>Your jackpot entries will appear here</p>
            <a href="{% url 'jackpot:my_entries' %}" class="btn btn-primary">View All Entries</a>
        </div>
        {% else %}
        <div class="empty-state">
            <i class="fas fa-user"></i>
            <h3>Login Required</h3>
            <p>Login to view your jackpot entries</p>
            <a href="{% url 'accounts:login' %}" class="btn btn-primary">Login</a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/pages/home.js' %}"></script>
<script src="{% static 'js/jackpot.js' %}"></script>
{% endblock %}
