// Deposit page JavaScript functionality

document.addEventListener('DOMContentLoaded', function() {
    const depositForm = document.getElementById('depositForm');
    const amountInput = document.getElementById('id_amount');
    const paymentMethodRadios = document.querySelectorAll('input[name="payment_method"]');
    const mpesaFields = document.querySelector('.mpesa-fields');
    const mpesaPhoneInput = document.getElementById('id_mpesa_phone_number');
    const quickAmountButtons = document.querySelectorAll('.quick-amount-btn');
    const savedMethodItems = document.querySelectorAll('.saved-method-item');
    const useMethodButtons = document.querySelectorAll('.use-method-btn');
    const submitBtn = document.getElementById('submitBtn');

    // Quick amount button functionality
    quickAmountButtons.forEach(button => {
        button.addEventListener('click', function() {
            const amount = this.getAttribute('data-amount');
            amountInput.value = amount;
            
            // Update active state
            quickAmountButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Trigger validation
            validateAmount();
        });
    });

    // Payment method change handler
    paymentMethodRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            handlePaymentMethodChange(this.value);
        });
    });

    // Initialize payment method display
    const checkedMethod = document.querySelector('input[name="payment_method"]:checked');
    if (checkedMethod) {
        handlePaymentMethodChange(checkedMethod.value);
    }

    // Handle payment method change
    function handlePaymentMethodChange(method) {
        // Hide all conditional fields
        mpesaFields.style.display = 'none';
        
        // Show relevant fields based on method
        if (method === 'mpesa') {
            mpesaFields.style.display = 'block';
            mpesaPhoneInput.required = true;
        } else {
            mpesaPhoneInput.required = false;
        }
        
        // Update minimum amount validation
        updateMinimumAmount(method);
    }

    // Update minimum amount based on payment method
    function updateMinimumAmount(method) {
        let minAmount = 10;
        let infoText = '';
        
        switch(method) {
            case 'mpesa':
                minAmount = 10;
                infoText = 'Minimum: KES 10.00 | Maximum: KES 500,000.00';
                break;
            case 'card':
                minAmount = 100;
                infoText = 'Minimum: KES 100.00 | Maximum: KES 500,000.00';
                break;
            case 'bank_transfer':
                minAmount = 500;
                infoText = 'Minimum: KES 500.00 | Maximum: KES 500,000.00';
                break;
        }
        
        amountInput.setAttribute('min', minAmount);
        
        const amountInfo = document.querySelector('.amount-info small');
        if (amountInfo) {
            amountInfo.textContent = infoText;
        }
        
        // Validate current amount
        validateAmount();
    }

    // Amount validation
    function validateAmount() {
        const amount = parseFloat(amountInput.value);
        const minAmount = parseFloat(amountInput.getAttribute('min'));
        const maxAmount = 500000;
        
        // Remove existing error styling
        amountInput.classList.remove('error');
        
        let errorMessage = '';
        
        if (isNaN(amount) || amount <= 0) {
            errorMessage = 'Please enter a valid amount';
        } else if (amount < minAmount) {
            errorMessage = `Minimum amount is KES ${minAmount.toFixed(2)}`;
        } else if (amount > maxAmount) {
            errorMessage = `Maximum amount is KES ${maxAmount.toFixed(2)}`;
        }
        
        // Display error if any
        let errorDiv = amountInput.parentNode.querySelector('.amount-error');
        if (errorMessage) {
            amountInput.classList.add('error');
            if (!errorDiv) {
                errorDiv = document.createElement('div');
                errorDiv.className = 'form-errors amount-error';
                amountInput.parentNode.appendChild(errorDiv);
            }
            errorDiv.innerHTML = `<span class="error">${errorMessage}</span>`;
            return false;
        } else {
            if (errorDiv) {
                errorDiv.remove();
            }
            return true;
        }
    }

    // Phone number validation
    function validatePhoneNumber() {
        if (!mpesaPhoneInput.required) return true;
        
        const phoneNumber = mpesaPhoneInput.value.trim();
        mpesaPhoneInput.classList.remove('error');
        
        let errorMessage = '';
        
        if (!phoneNumber) {
            errorMessage = 'M-Pesa phone number is required';
        } else {
            // Normalize and validate format
            let normalizedPhone = phoneNumber;
            if (normalizedPhone.startsWith('0')) {
                normalizedPhone = '+254' + normalizedPhone.substring(1);
            } else if (normalizedPhone.startsWith('254')) {
                normalizedPhone = '+' + normalizedPhone;
            }
            
            const phoneRegex = /^\+254[0-9]{9}$/;
            if (!phoneRegex.test(normalizedPhone)) {
                errorMessage = 'Please enter a valid Kenyan phone number (e.g., +254712345678)';
            } else {
                // Update the input with normalized format
                mpesaPhoneInput.value = normalizedPhone;
            }
        }
        
        // Display error if any
        let errorDiv = mpesaPhoneInput.parentNode.querySelector('.phone-error');
        if (errorMessage) {
            mpesaPhoneInput.classList.add('error');
            if (!errorDiv) {
                errorDiv = document.createElement('div');
                errorDiv.className = 'form-errors phone-error';
                mpesaPhoneInput.parentNode.appendChild(errorDiv);
            }
            errorDiv.innerHTML = `<span class="error">${errorMessage}</span>`;
            return false;
        } else {
            if (errorDiv) {
                errorDiv.remove();
            }
            return true;
        }
    }

    // Saved payment method usage
    useMethodButtons.forEach(button => {
        button.addEventListener('click', function() {
            const methodItem = this.closest('.saved-method-item');
            const methodType = methodItem.getAttribute('data-method-type');
            const methodId = methodItem.getAttribute('data-method-id');
            
            // Select the appropriate payment method radio
            const methodRadio = document.querySelector(`input[name="payment_method"][value="${methodType}"]`);
            if (methodRadio) {
                methodRadio.checked = true;
                handlePaymentMethodChange(methodType);
                
                // Fill in the details based on method type
                if (methodType === 'mpesa') {
                    const phoneNumber = methodItem.querySelector('.method-details').textContent.trim();
                    mpesaPhoneInput.value = phoneNumber;
                }
            }
            
            // Scroll to form
            depositForm.scrollIntoView({ behavior: 'smooth' });
        });
    });

    // Real-time validation
    amountInput.addEventListener('input', validateAmount);
    amountInput.addEventListener('blur', validateAmount);
    
    mpesaPhoneInput.addEventListener('input', validatePhoneNumber);
    mpesaPhoneInput.addEventListener('blur', validatePhoneNumber);

    // Form submission
    depositForm.addEventListener('submit', function(e) {
        let isValid = true;
        
        // Validate amount
        if (!validateAmount()) {
            isValid = false;
        }
        
        // Validate phone number if required
        if (!validatePhoneNumber()) {
            isValid = false;
        }
        
        if (!isValid) {
            e.preventDefault();
            
            // Scroll to first error
            const firstError = depositForm.querySelector('.error');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
            return;
        }
        
        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        
        // Form will submit normally
    });

    // Add error styling for invalid inputs
    const style = document.createElement('style');
    style.textContent = `
        .form-control.error {
            border-color: var(--danger-color) !important;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }
        
        .amount-error,
        .phone-error {
            margin-top: 8px;
        }
        
        .amount-error .error,
        .phone-error .error {
            display: block;
            color: var(--danger-color);
            font-size: 0.85rem;
        }
    `;
    document.head.appendChild(style);

    // Auto-format phone number as user types
    mpesaPhoneInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, ''); // Remove non-digits
        
        if (value.startsWith('254')) {
            value = '+' + value;
        } else if (value.startsWith('0') && value.length > 1) {
            value = '+254' + value.substring(1);
        } else if (value.length > 0 && !value.startsWith('254')) {
            value = '+254' + value;
        }
        
        // Limit to correct length
        if (value.length > 13) {
            value = value.substring(0, 13);
        }
        
        this.value = value;
    });

    // Format amount input with commas
    amountInput.addEventListener('input', function() {
        let value = this.value.replace(/,/g, '');
        if (!isNaN(value) && value !== '') {
            // Format with commas but keep decimal places
            const parts = value.split('.');
            parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            this.value = parts.join('.');
        }
    });

    // Remove commas before form submission for proper validation
    depositForm.addEventListener('submit', function() {
        amountInput.value = amountInput.value.replace(/,/g, '');
    });
});