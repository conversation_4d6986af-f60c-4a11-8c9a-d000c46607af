/**
 * Lazy Loading Implementation for Betika Clone
 * Improves page load performance by loading content on demand
 */

class LazyLoader {
    constructor() {
        this.imageObserver = null;
        this.contentObserver = null;
        this.init();
    }

    init() {
        // Initialize lazy loading for images
        this.initImageLazyLoading();
        
        // Initialize lazy loading for content sections
        this.initContentLazyLoading();
        
        // Initialize lazy loading for odds data
        this.initOddsLazyLoading();
    }

    initImageLazyLoading() {
        // Check if Intersection Observer is supported
        if ('IntersectionObserver' in window) {
            this.imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        this.loadImage(img);
                        observer.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            // Observe all images with data-src attribute
            document.querySelectorAll('img[data-src]').forEach(img => {
                this.imageObserver.observe(img);
            });
        } else {
            // Fallback for browsers without Intersection Observer
            this.loadAllImages();
        }
    }

    loadImage(img) {
        const src = img.getAttribute('data-src');
        if (src) {
            img.src = src;
            img.classList.add('loaded');
            img.removeAttribute('data-src');
            
            // Add fade-in effect
            img.style.opacity = '0';
            img.onload = () => {
                img.style.transition = 'opacity 0.3s';
                img.style.opacity = '1';
            };
        }
    }

    loadAllImages() {
        document.querySelectorAll('img[data-src]').forEach(img => {
            this.loadImage(img);
        });
    }

    initContentLazyLoading() {
        if ('IntersectionObserver' in window) {
            this.contentObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const element = entry.target;
                        this.loadContent(element);
                        observer.unobserve(element);
                    }
                });
            }, {
                rootMargin: '100px 0px',
                threshold: 0.1
            });

            // Observe elements with lazy-content class
            document.querySelectorAll('.lazy-content').forEach(element => {
                this.contentObserver.observe(element);
            });
        }
    }

    loadContent(element) {
        const contentUrl = element.getAttribute('data-content-url');
        if (contentUrl) {
            // Show loading spinner
            element.innerHTML = '<div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div>';
            
            fetch(contentUrl)
                .then(response => response.text())
                .then(html => {
                    element.innerHTML = html;
                    element.classList.add('content-loaded');
                    
                    // Trigger custom event for loaded content
                    element.dispatchEvent(new CustomEvent('contentLoaded', {
                        detail: { url: contentUrl }
                    }));
                })
                .catch(error => {
                    console.error('Error loading content:', error);
                    element.innerHTML = '<div class="error-message">Failed to load content</div>';
                });
        }
    }

    initOddsLazyLoading() {
        // Lazy load odds data for events not currently visible
        if ('IntersectionObserver' in window) {
            const oddsObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const eventElement = entry.target;
                        this.loadOddsData(eventElement);
                        observer.unobserve(eventElement);
                    }
                });
            }, {
                rootMargin: '200px 0px',
                threshold: 0.1
            });

            // Observe event cards that need odds data
            document.querySelectorAll('.event-card[data-event-id]').forEach(card => {
                if (!card.classList.contains('odds-loaded')) {
                    oddsObserver.observe(card);
                }
            });
        }
    }

    loadOddsData(eventElement) {
        const eventId = eventElement.getAttribute('data-event-id');
        if (!eventId) return;

        const oddsContainer = eventElement.querySelector('.odds-container');
        if (!oddsContainer) return;

        // Show loading state
        oddsContainer.innerHTML = '<div class="odds-loading">Loading odds...</div>';

        fetch(`/api/v1/sports/events/${eventId}/odds/`)
            .then(response => response.json())
            .then(data => {
                this.renderOdds(oddsContainer, data.odds);
                eventElement.classList.add('odds-loaded');
            })
            .catch(error => {
                console.error('Error loading odds:', error);
                oddsContainer.innerHTML = '<div class="odds-error">Failed to load odds</div>';
            });
    }

    renderOdds(container, oddsData) {
        let oddsHtml = '';
        
        // Render main markets (1X2, Over/Under, etc.)
        Object.keys(oddsData).forEach(marketType => {
            const market = oddsData[marketType];
            if (market.odds && market.odds.length > 0) {
                oddsHtml += `<div class="market-odds" data-market="${marketType}">`;
                
                market.odds.forEach(odd => {
                    oddsHtml += `
                        <button class="odds-button" 
                                data-odds-id="${odd.id}" 
                                data-odds-value="${odd.odds_value}"
                                onclick="BetSlip.addSelection(this)">
                            <span class="selection">${odd.selection}</span>
                            <span class="odds-value">${odd.odds_value}</span>
                        </button>
                    `;
                });
                
                oddsHtml += '</div>';
            }
        });

        container.innerHTML = oddsHtml;
    }

    // Method to manually trigger lazy loading for dynamically added content
    observeNewContent(selector) {
        if (this.imageObserver) {
            document.querySelectorAll(`${selector} img[data-src]`).forEach(img => {
                this.imageObserver.observe(img);
            });
        }

        if (this.contentObserver) {
            document.querySelectorAll(`${selector} .lazy-content`).forEach(element => {
                this.contentObserver.observe(element);
            });
        }
    }

    // Preload critical images
    preloadCriticalImages() {
        const criticalImages = document.querySelectorAll('img[data-critical="true"]');
        criticalImages.forEach(img => {
            this.loadImage(img);
        });
    }

    // Cleanup observers
    destroy() {
        if (this.imageObserver) {
            this.imageObserver.disconnect();
        }
        if (this.contentObserver) {
            this.contentObserver.disconnect();
        }
    }
}

// Initialize lazy loading when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.lazyLoader = new LazyLoader();
    
    // Preload critical images immediately
    window.lazyLoader.preloadCriticalImages();
});

// Utility function to create lazy-loaded image
function createLazyImage(src, alt = '', className = '') {
    const img = document.createElement('img');
    img.setAttribute('data-src', src);
    img.alt = alt;
    img.className = className;
    img.src = 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1 1"%3E%3C/svg%3E'; // Placeholder
    
    if (window.lazyLoader && window.lazyLoader.imageObserver) {
        window.lazyLoader.imageObserver.observe(img);
    }
    
    return img;
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LazyLoader;
}
