#!/usr/bin/env python
"""
Simple test script to verify authentication pages functionality
"""

import os
import sys
import django
from django.test import Client

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'betika_clone.settings')
django.setup()

def test_auth_pages():
    """Test that the authentication pages load correctly"""
    client = Client()
    
    print("Testing authentication pages...")
    
    # Test login page
    print("\n1. Testing Login Page:")
    response = client.get('/accounts/login/')
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        print("   ✅ Login page loads successfully")
        
        content = response.content.decode('utf-8')
        
        # Check for key elements
        checks = [
            ('B!', 'Logo'),
            ('Login', 'Login header'),
            ('Phone Number', 'Phone number field'),
            ('Password', 'Password field'),
            ('Back to home', 'Back to home link'),
            ('Register', 'Register link'),
            ('Forgot Password', 'Forgot password link'),
        ]
        
        for text, description in checks:
            if text in content:
                print(f"   ✅ {description} found")
            else:
                print(f"   ❌ {description} missing")
                
    else:
        print(f"   ❌ Login page failed to load: {response.status_code}")
    
    # Test register page
    print("\n2. Testing Register Page:")
    response = client.get('/accounts/register/')
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        print("   ✅ Register page loads successfully")
        
        content = response.content.decode('utf-8')
        
        # Check for key elements
        checks = [
            ('B!', 'Logo'),
            ('Create Account', 'Register header'),
            ('Phone Number', 'Phone number field'),
            ('Email Address', 'Email field'),
            ('First Name', 'First name field'),
            ('Last Name', 'Last name field'),
            ('Date of Birth', 'Date of birth field'),
            ('Password', 'Password field'),
            ('Confirm Password', 'Confirm password field'),
            ('Terms and Conditions', 'Terms checkbox'),
            ('Back to home', 'Back to home link'),
            ('Login', 'Login link'),
        ]
        
        for text, description in checks:
            if text in content:
                print(f"   ✅ {description} found")
            else:
                print(f"   ❌ {description} missing")
                
    else:
        print(f"   ❌ Register page failed to load: {response.status_code}")
    
    # Test form submission (basic)
    print("\n3. Testing Form Submissions:")
    
    # Test login form with invalid data
    response = client.post('/accounts/login/', {
        'username': 'invalid',
        'password': 'invalid'
    })
    print(f"   Login with invalid data: {response.status_code}")
    if response.status_code in [200, 302]:
        print("   ✅ Login form handles invalid data correctly")
    else:
        print("   ❌ Login form submission failed")
    
    # Test register form with missing data
    response = client.post('/accounts/register/', {
        'phone_number': '',
        'email': '',
        'password1': '',
        'password2': ''
    })
    print(f"   Register with missing data: {response.status_code}")
    if response.status_code == 200:
        print("   ✅ Register form handles missing data correctly")
    else:
        print("   ❌ Register form submission failed")

    print("\n✅ Authentication pages testing completed!")

if __name__ == '__main__':
    test_auth_pages()
