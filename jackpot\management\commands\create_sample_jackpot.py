"""
Management command to create sample jackpot data
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
import uuid

from jackpot.models import Jackpot, JackpotGame
from sports.models import Sport, Event, Market, Odds


class Command(BaseCommand):
    help = 'Create sample jackpot data for testing'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample jackpot data...')
        
        # Create or get soccer sport
        soccer, created = Sport.objects.get_or_create(
            name='Soccer',
            defaults={'description': 'Football/Soccer sport'}
        )
        if created:
            self.stdout.write(f'Created sport: {soccer}')
        else:
            self.stdout.write(f'Using existing sport: {soccer}')

        # Sample events data
        events_data = [
            {'home_team': 'FCV Dender EH', 'away_team': 'Cercle Brugge', 'league': 'Soccer • Pro League'},
            {'home_team': 'Grasshopper Club Zurich', 'away_team': 'FC Luzern', 'league': 'Soccer • Super League'},
            {'home_team': 'FC St. Gallen 1879', 'away_team': 'FC Basel 1893', 'league': 'Soccer • Pro League'},
            {'home_team': 'SV Zulte Waregem', 'away_team': 'Yellow-Red KV Mechelen', 'league': 'Soccer • Pro League'},
            {'home_team': 'St. Truidense VV', 'away_team': 'KAA Gent', 'league': 'Soccer • Primera LFP'},
            {'home_team': 'CA Talleres de Cordoba', 'away_team': 'CD Godoy Cruz', 'league': 'Soccer • Extraklasa'},
            {'home_team': 'Korona Kielce', 'away_team': 'Legia Warszawa', 'league': 'Soccer • Primera LFP'},
            {'home_team': 'Gimnasia Y Esgrima La Plata', 'away_team': 'CA Independiente Avellaneda', 'league': 'Soccer • Primera LFP'},
        ]

        # Create events
        events = []
        base_time = timezone.now() + timedelta(days=1)
        
        for i, event_data in enumerate(events_data):
            event, created = Event.objects.get_or_create(
                home_team=event_data['home_team'],
                away_team=event_data['away_team'],
                defaults={
                    'sport': soccer,
                    'league': event_data['league'],
                    'start_time': base_time + timedelta(hours=i*2),
                    'status': 'upcoming',
                    'external_id': f'jackpot_sample_{i+1}'
                }
            )
            events.append(event)
            if created:
                self.stdout.write(f'Created event: {event}')
            else:
                self.stdout.write(f'Using existing event: {event}')

        # Create markets and odds for each event
        for event in events:
            # Create 1X2 market
            market, created = Market.objects.get_or_create(
                event=event,
                market_type='1X2',
                defaults={
                    'name': 'Match Result',
                    'description': 'Full Time Result'
                }
            )
            
            if created:
                self.stdout.write(f'Created market for {event}')
                
                # Create odds for 1X2 with varied realistic values
                import random
                home_odds = round(random.uniform(2.0, 4.0), 2)
                draw_odds = round(random.uniform(3.0, 4.5), 2)
                away_odds = round(random.uniform(1.8, 4.5), 2)

                odds_data = [
                    ('1', Decimal(str(home_odds))),
                    ('X', Decimal(str(draw_odds))),
                    ('2', Decimal(str(away_odds))),
                ]

                for selection, odds_value in odds_data:
                    Odds.objects.create(
                        market=market,
                        selection=selection,
                        odds_value=odds_value
                    )

        # Create a mega jackpot
        jackpot, created = Jackpot.objects.get_or_create(
            name='Mega Jackpot',
            defaults={
                'description': 'Win KES 15,000,000.00 with 15 BOB',
                'jackpot_type': 'mega',
                'start_time': timezone.now(),
                'end_time': timezone.now() + timedelta(days=4, hours=7, minutes=30),
                'entry_fee': Decimal('50.00'),
                'current_prize_pool': Decimal('15000000.00'),
                'minimum_correct_predictions': 12,
                'total_games': 15,
                'status': 'active',
                'is_active': True
            }
        )
        
        if created:
            self.stdout.write(f'Created jackpot: {jackpot}')
            
            # Create jackpot games
            for i, event in enumerate(events[:15]):  # Use first 15 events
                # Get the 1X2 market for this event
                try:
                    market = Market.objects.get(event=event, market_type='1X2')
                    
                    JackpotGame.objects.create(
                        jackpot=jackpot,
                        event=event,
                        market=market,
                        game_number=i + 1
                    )
                    self.stdout.write(f'Added game {i+1}: {event}')
                except Market.DoesNotExist:
                    self.stdout.write(f'No market found for {event}')
        else:
            self.stdout.write(f'Using existing jackpot: {jackpot}')

        # Create a weekly jackpot
        weekly_jackpot, created = Jackpot.objects.get_or_create(
            name='Midweek Jackpot',
            defaults={
                'description': 'Midweek jackpot with smaller games',
                'jackpot_type': 'weekly',
                'start_time': timezone.now(),
                'end_time': timezone.now() + timedelta(days=2, hours=12),
                'entry_fee': Decimal('20.00'),
                'current_prize_pool': Decimal('500000.00'),
                'minimum_correct_predictions': 10,
                'total_games': 13,
                'status': 'active',
                'is_active': True
            }
        )
        
        if created:
            self.stdout.write(f'Created weekly jackpot: {weekly_jackpot}')

        self.stdout.write(
            self.style.SUCCESS('Successfully created sample jackpot data!')
        )
