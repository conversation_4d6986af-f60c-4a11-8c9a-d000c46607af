"""
Management command for CSS optimization
"""

from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
import json

from core.css_optimizer import CSSOptimizer, CSSAnalyzer


class Command(BaseCommand):
    help = 'Optimize CSS files for better performance'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--action',
            type=str,
            choices=['optimize', 'analyze', 'critical', 'sprite'],
            default='optimize',
            help='Action to perform'
        )
        
        parser.add_argument(
            '--file',
            type=str,
            help='Specific CSS file to optimize'
        )
        
        parser.add_argument(
            '--output',
            type=str,
            help='Output file path'
        )
        
        parser.add_argument(
            '--format',
            type=str,
            choices=['json', 'table'],
            default='table',
            help='Output format for analysis'
        )
    
    def handle(self, *args, **options):
        action = options['action']
        
        if action == 'optimize':
            self.optimize_css(options.get('file'), options.get('output'))
        elif action == 'analyze':
            self.analyze_css(options['format'])
        elif action == 'critical':
            self.generate_critical_css()
        elif action == 'sprite':
            self.create_sprite()
    
    def optimize_css(self, file_path=None, output_path=None):
        """Optimize CSS files"""
        optimizer = CSSOptimizer()
        
        if file_path:
            # Optimize specific file
            result = optimizer.optimize_css_file(file_path, output_path)
            if result:
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Optimized {file_path}:\n"
                        f"  Original: {result['original_size']:,} bytes\n"
                        f"  Minified: {result['minified_size']:,} bytes\n"
                        f"  Reduction: {result['compression_ratio']:.1f}%"
                    )
                )
            else:
                self.stdout.write(
                    self.style.ERROR(f"Failed to optimize {file_path}")
                )
        else:
            # Optimize all CSS files
            self.stdout.write('Optimizing all CSS files...')
            results = optimizer.optimize_all_css_files()
            
            if results:
                total_original = sum(r['original_size'] for r in results)
                total_minified = sum(r['minified_size'] for r in results)
                total_reduction = (1 - total_minified / total_original) * 100
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f"\nOptimization complete!\n"
                        f"Files processed: {len(results)}\n"
                        f"Total original size: {total_original:,} bytes\n"
                        f"Total minified size: {total_minified:,} bytes\n"
                        f"Total reduction: {total_reduction:.1f}%"
                    )
                )
                
                # Show individual file results
                self.stdout.write('\nIndividual file results:')
                for result in results:
                    self.stdout.write(
                        f"  {result['file']}: "
                        f"{result['compression_ratio']:.1f}% reduction"
                    )
            else:
                self.stdout.write(
                    self.style.WARNING('No CSS files found to optimize')
                )
    
    def analyze_css(self, output_format):
        """Analyze CSS performance"""
        analyzer = CSSAnalyzer()
        
        self.stdout.write('Analyzing CSS files...')
        report = analyzer.generate_performance_report()
        
        if output_format == 'json':
            self.stdout.write(json.dumps(report, indent=2))
        else:
            self.stdout.write('\nCSS Performance Report')
            self.stdout.write('=' * 50)
            self.stdout.write(f"Total files: {report['total_files']}")
            self.stdout.write(f"Total size: {report['total_size']:,} bytes")
            self.stdout.write(f"Total selectors: {report['total_selectors']:,}")
            self.stdout.write(f"Total complexity: {report['total_complexity']:.1f}")
            self.stdout.write(f"Average file size: {report['average_file_size']:.0f} bytes")
            
            self.stdout.write('\nFile Details:')
            self.stdout.write('-' * 80)
            self.stdout.write(
                f"{'File':<40} {'Size':<10} {'Selectors':<10} {'Complexity':<12}"
            )
            self.stdout.write('-' * 80)
            
            for file_info in report['files']:
                file_name = file_info['file_path'].split('/')[-1]
                self.stdout.write(
                    f"{file_name:<40} "
                    f"{file_info['file_size']:<10,} "
                    f"{file_info['selector_count']:<10} "
                    f"{file_info['complexity_score']:<12.1f}"
                )
            
            # Performance recommendations
            self.stdout.write('\nRecommendations:')
            if report['total_size'] > 100000:  # 100KB
                self.stdout.write(
                    self.style.WARNING(
                        '• Consider splitting large CSS files'
                    )
                )
            
            if report['total_complexity'] > 1000:
                self.stdout.write(
                    self.style.WARNING(
                        '• High CSS complexity detected - consider simplification'
                    )
                )
            
            large_files = [
                f for f in report['files'] 
                if f['file_size'] > 50000  # 50KB
            ]
            if large_files:
                self.stdout.write(
                    self.style.WARNING(
                        f"• {len(large_files)} large CSS files found - consider optimization"
                    )
                )
    
    def generate_critical_css(self):
        """Generate critical CSS"""
        optimizer = CSSOptimizer()
        
        self.stdout.write('Generating critical CSS...')
        
        try:
            critical_css_path = optimizer.generate_critical_css_file()
            
            # Get file size
            with open(critical_css_path, 'r', encoding='utf-8') as f:
                critical_css_size = len(f.read())
            
            self.stdout.write(
                self.style.SUCCESS(
                    f"Critical CSS generated successfully!\n"
                    f"File: {critical_css_path}\n"
                    f"Size: {critical_css_size:,} bytes"
                )
            )
            
            # Recommendations
            if critical_css_size > 14000:  # 14KB (recommended max)
                self.stdout.write(
                    self.style.WARNING(
                        'Warning: Critical CSS is larger than recommended 14KB'
                    )
                )
            else:
                self.stdout.write(
                    self.style.SUCCESS(
                        'Critical CSS size is within recommended limits'
                    )
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Failed to generate critical CSS: {e}")
            )
    
    def create_sprite(self):
        """Create CSS sprite"""
        optimizer = CSSOptimizer()
        
        self.stdout.write('Creating CSS sprite...')
        
        try:
            # This is a placeholder - in production you'd implement actual sprite generation
            sprite_path = settings.STATIC_ROOT / 'css' / 'sprite.css'
            result = optimizer.create_css_sprite(
                settings.STATIC_ROOT / 'images' / 'icons',
                sprite_path
            )
            
            self.stdout.write(
                self.style.SUCCESS(
                    f"CSS sprite created: {result}"
                )
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Failed to create sprite: {e}")
            )
    
    def show_optimization_tips(self):
        """Show CSS optimization tips"""
        tips = [
            "1. Use CSS minification to reduce file sizes",
            "2. Combine multiple CSS files to reduce HTTP requests",
            "3. Use critical CSS inlining for above-the-fold content",
            "4. Remove unused CSS selectors",
            "5. Use CSS sprites for small images",
            "6. Enable gzip compression on your server",
            "7. Use CSS preprocessors for better organization",
            "8. Optimize CSS delivery with async loading",
            "9. Use CSS containment for better performance",
            "10. Profile CSS performance with browser dev tools"
        ]
        
        self.stdout.write('\nCSS Optimization Tips:')
        self.stdout.write('=' * 50)
        for tip in tips:
            self.stdout.write(tip)
