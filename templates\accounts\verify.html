{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Account - Betzide!</title>
    <link rel="stylesheet" href="{% static 'css/base.css' %}">
    <link rel="stylesheet" href="{% static 'css/auth.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="auth-body">
    <!-- Back to Home -->
    <div class="back-to-home">
        <a href="{% url 'home' %}" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Back to home
        </a>
    </div>

    <!-- Main Auth Container -->
    <div class="auth-container">
        <div class="auth-card">
            <!-- Left Side - Illustration -->
            <div class="auth-illustration">
                <div class="illustration-content">
                    <div class="colorful-shapes">
                        <div class="shape shape-1"></div>
                        <div class="shape shape-2"></div>
                        <div class="shape shape-3"></div>
                        <div class="shape shape-4"></div>
                        <div class="shape shape-5"></div>
                    </div>
                    <div class="character">
                        <div class="character-body"></div>
                        <div class="character-head"></div>
                        <div class="character-arm-left"></div>
                        <div class="character-arm-right"></div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Verification Form -->
            <div class="auth-form-section">
                <!-- Logo -->
                <div class="auth-logo">
                    <span class="logo-text">B!</span>
                </div>

                <!-- Form Header -->
                <div class="auth-header">
                    <h2>Verify Your Account</h2>
                    <p>Enter the 6-digit code sent to your phone</p>
                </div>

                <!-- Phone Number Display -->
                <div class="verification-info">
                    <div class="phone-display">
                        <i class="fas fa-mobile-alt"></i>
                        <span>{{ user.phone_number }}</span>
                    </div>
                </div>

                <!-- Verification Form -->
                <form method="post" id="verificationForm" class="auth-form">
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                        <div class="error-message">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}

                    <!-- Verification Code Field -->
                    <div class="form-group">
                        <label for="{{ form.code.id_for_label }}">Verification Code</label>
                        {{ form.code }}
                        {% if form.code.errors %}
                            <div class="field-error">
                                {{ form.code.errors }}
                            </div>
                        {% endif %}
                        {% if form.code.help_text %}
                            <div class="field-help">{{ form.code.help_text }}</div>
                        {% endif %}
                    </div>

                    <!-- Verify Button -->
                    <button type="submit" class="auth-btn auth-btn-primary">
                        Verify Account
                    </button>
                </form>

                <!-- Resend Section -->
                <div class="resend-section">
                    <p>Didn't receive the code?</p>
                    <button type="button" class="resend-btn" id="resendBtn">
                        Resend Code
                    </button>
                    <div id="resendMessage" class="resend-message" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
document.addEventListener('DOMContentLoaded', function() {
    const resendBtn = document.getElementById('resendBtn');
    const resendMessage = document.getElementById('resendMessage');
    
    resendBtn.addEventListener('click', function() {
        resendBtn.disabled = true;
        resendBtn.textContent = 'Sending...';
        
        fetch('{% url "accounts:resend_verification" %}', {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                resendMessage.innerHTML = `<div class="text-danger">${data.error}</div>`;
            } else {
                resendMessage.innerHTML = `<div class="text-success">${data.message}</div>`;
                // In development, show the code
                if (data.code) {
                    resendMessage.innerHTML += `<div class="text-info">Code: ${data.code}</div>`;
                }
            }
            resendMessage.style.display = 'block';
            
            // Re-enable button after 60 seconds
            setTimeout(() => {
                resendBtn.disabled = false;
                resendBtn.textContent = 'Resend Code';
            }, 60000);
        })
        .catch(error => {
            resendMessage.innerHTML = '<div class="text-danger">Error sending code. Please try again.</div>';
            resendMessage.style.display = 'block';
            resendBtn.disabled = false;
            resendBtn.textContent = 'Resend Code';
        });
    });
});
    </script>
</body>
</html>