{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Betzide!</title>
    <link rel="stylesheet" href="{% static 'css/base.css' %}">
    <link rel="stylesheet" href="{% static 'css/auth.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="auth-body">
    <!-- Back to Home -->
    <div class="back-to-home">
        <a href="{% url 'home' %}" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Back to home
        </a>
    </div>

    <!-- Main Auth Container -->
    <div class="auth-container">
        <div class="auth-card">
            <!-- Left Side - Illustration -->
            <div class="auth-illustration">
                <div class="illustration-content">
                    <div class="colorful-shapes">
                        <div class="shape shape-1"></div>
                        <div class="shape shape-2"></div>
                        <div class="shape shape-3"></div>
                        <div class="shape shape-4"></div>
                        <div class="shape shape-5"></div>
                    </div>
                    <div class="character">
                        <div class="character-body"></div>
                        <div class="character-head"></div>
                        <div class="character-arm-left"></div>
                        <div class="character-arm-right"></div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Login Form -->
            <div class="auth-form-section">
                <!-- Logo -->
                <div class="auth-logo">
                    <span class="logo-text">B!</span>
                </div>

                <!-- Form Header -->
                <div class="auth-header">
                    <h2>Login</h2>
                    <p>Enter your phone number to login</p>
                </div>

                <!-- Login Form -->
                <form method="post" class="auth-form">
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                        <div class="error-message">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}

                    <!-- Phone Number Field -->
                    <div class="form-group">
                        <label for="{{ form.username.id_for_label }}">Phone Number</label>
                        <div class="phone-input-group">
                            <div class="country-code">
                                <span class="flag-emoji">🇰🇪</span>
                                <span>+254</span>
                            </div>
                            {{ form.username }}
                        </div>
                        {% if form.username.errors %}
                            <div class="field-error">
                                {{ form.username.errors }}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Password Field -->
                    <div class="form-group">
                        <label for="{{ form.password.id_for_label }}">Password</label>
                        <div class="password-input-group">
                            {{ form.password }}
                            <button type="button" class="password-toggle" onclick="togglePassword()">
                                <i class="fas fa-eye" id="password-eye"></i>
                            </button>
                        </div>
                        {% if form.password.errors %}
                            <div class="field-error">
                                {{ form.password.errors }}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Login Button -->
                    <button type="submit" class="auth-btn auth-btn-primary">
                        Login
                    </button>
                </form>

                <!-- Additional Links -->
                <div class="auth-links">
                    <div class="register-link">
                        <span>Don't have an account?</span>
                        <a href="{% url 'accounts:register' %}">Register</a>
                    </div>

                    <div class="forgot-password">
                        <a href="{% url 'accounts:password_reset' %}">Forgot Password</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        function togglePassword() {
            const passwordField = document.getElementById('{{ form.password.id_for_label }}');
            const eyeIcon = document.getElementById('password-eye');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                eyeIcon.classList.remove('fa-eye');
                eyeIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                eyeIcon.classList.remove('fa-eye-slash');
                eyeIcon.classList.add('fa-eye');
            }
        }

        // Form validation and enhancement
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('.auth-form');
            const phoneInput = document.getElementById('{{ form.username.id_for_label }}');

            // Format phone number input
            if (phoneInput) {
                phoneInput.addEventListener('input', function(e) {
                    let value = e.target.value.replace(/\D/g, '');
                    if (value.startsWith('254')) {
                        value = value.substring(3);
                    }
                    if (value.startsWith('0')) {
                        value = value.substring(1);
                    }
                    e.target.value = value;
                });
            }
        });
    </script>
</body>
</html>