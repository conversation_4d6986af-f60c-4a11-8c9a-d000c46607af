"""
Management command to create all sports referenced in the navigation
"""

from django.core.management.base import BaseCommand
from sports.models import Sport


class Command(BaseCommand):
    help = 'Create all sports referenced in the sidebar navigation'

    def handle(self, *args, **options):
        self.stdout.write('Creating all sports...')

        # Define all sports from the navigation with their slugs and icons
        sports_data = [
            {'name': 'Soccer', 'slug': 'soccer', 'icon': 'fas fa-futbol', 'display_order': 1},
            {'name': 'Boxing', 'slug': 'boxing', 'icon': 'fas fa-fist-raised', 'display_order': 2},
            {'name': 'Rugby', 'slug': 'rugby', 'icon': 'fas fa-football-ball', 'display_order': 3},
            {'name': 'Aussie Rules', 'slug': 'aussie-rules', 'icon': 'fas fa-football-ball', 'display_order': 4},
            {'name': 'Baseball', 'slug': 'baseball', 'icon': 'fas fa-baseball-ball', 'display_order': 5},
            {'name': 'Table Tennis', 'slug': 'table-tennis', 'icon': 'fas fa-table-tennis', 'display_order': 6},
            {'name': 'Cricket', 'slug': 'cricket', 'icon': 'fas fa-cricket', 'display_order': 7},
            {'name': 'Tennis', 'slug': 'tennis', 'icon': 'fas fa-tennis-ball', 'display_order': 8},
            {'name': 'MMA', 'slug': 'mma', 'icon': 'fas fa-fist-raised', 'display_order': 9},
            {'name': 'Basketball', 'slug': 'basketball', 'icon': 'fas fa-basketball-ball', 'display_order': 10},
            {'name': 'Water Polo', 'slug': 'water-polo', 'icon': 'fas fa-swimmer', 'display_order': 11},
            {'name': 'Volleyball', 'slug': 'volleyball', 'icon': 'fas fa-volleyball-ball', 'display_order': 12},
            {'name': 'eSoccer', 'slug': 'esoccer', 'icon': 'fas fa-gamepad', 'display_order': 13},
            {'name': 'Handball', 'slug': 'handball', 'icon': 'fas fa-hand-paper', 'display_order': 14},
            {'name': 'Darts', 'slug': 'darts', 'icon': 'fas fa-bullseye', 'display_order': 15},
        ]

        # Also update existing sports that might have missing slugs
        existing_sports_updates = [
            {'name': 'Football', 'slug': 'soccer'},  # Map Football to soccer slug
        ]

        # Update existing sports first
        for sport_update in existing_sports_updates:
            try:
                sport = Sport.objects.get(name=sport_update['name'])
                if not sport.slug:
                    sport.slug = sport_update['slug']
                    sport.save()
                    self.stdout.write(f'Updated {sport.name} with slug: {sport.slug}')
            except Sport.DoesNotExist:
                pass

        # Create or update all sports
        for sport_data in sports_data:
            sport, created = Sport.objects.get_or_create(
                slug=sport_data['slug'],
                defaults={
                    'name': sport_data['name'],
                    'icon': sport_data['icon'],
                    'display_order': sport_data['display_order'],
                    'is_active': True
                }
            )

            if created:
                self.stdout.write(f'Created sport: {sport.name} (slug: {sport.slug})')
            else:
                # Update existing sport if needed
                updated = False
                if sport.name != sport_data['name']:
                    sport.name = sport_data['name']
                    updated = True
                if sport.icon != sport_data['icon']:
                    sport.icon = sport_data['icon']
                    updated = True
                if sport.display_order != sport_data['display_order']:
                    sport.display_order = sport_data['display_order']
                    updated = True
                
                if updated:
                    sport.save()
                    self.stdout.write(f'Updated sport: {sport.name} (slug: {sport.slug})')
                else:
                    self.stdout.write(f'Sport already exists: {sport.name} (slug: {sport.slug})')

        self.stdout.write(
            self.style.SUCCESS('Successfully created/updated all sports!')
        )
