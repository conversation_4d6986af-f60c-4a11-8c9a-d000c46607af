{% load static %}
{% load compress %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#1a472a">
    <title>{% block title %}Betika! - Sports Betting Platform{% endblock %}</title>
    
    <!-- CSS -->
    {% compress css %}
    <link rel="stylesheet" type="text/css" href="{% static 'css/base.css' %}">
    <link rel="stylesheet" type="text/css" href="{% static 'css/theme/betika-theme.css' %}">
    <link rel="stylesheet" type="text/css" href="{% static 'css/components/betslip.css' %}">
    <link rel="stylesheet" type="text/css" href="{% static 'css/responsive.css' %}">
    {% block extra_css %}{% endblock %}
    {% endcompress %}
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{% static 'images/favicon.ico' %}">

    <!-- CSRF Token for AJAX requests -->
    <meta name="csrf-token" content="{{ csrf_token }}">
</head>
<body class="betika-theme">
    <!-- Header Navigation -->
    <header class="header-nav">
        <div class="header-container">
            <!-- Logo -->
            <div class="logo">
                <a href="{% url 'home' %}">
                    <span class="logo-text">Betzide!</span>
                </a>
            </div>
            
            <!-- Main Navigation Tabs -->
            <nav class="main-nav">
                <ul class="nav-tabs">
                    <li><a href="{% url 'home' %}" class="nav-tab {% if request.resolver_match.url_name == 'home' %}active{% endif %}">Home</a></li>
                    <li><a href="/sports/" class="nav-tab">Live <span class="badge">49</span></a></li>
                    <li><a href="{% url 'jackpot:list' %}" class="nav-tab {% if request.resolver_match.namespace == 'jackpot' %}active{% endif %}">Jackpots</a></li>
                    <li><a href="/shikisha/" class="nav-tab">Shikisha Bet <span class="badge">6</span></a></li>
                    <li><a href="/aviator/" class="nav-tab">Aviator</a></li>
                    <li><a href="/ligi-bora/" class="nav-tab">Ligi Bora</a></li>
                    <li><a href="/casino/" class="nav-tab">Casino</a></li>
                    <li><a href="/promotions/" class="nav-tab">Promotions <span class="badge">11</span></a></li>
                    <li><a href="/virtuals/" class="nav-tab">Virtuals</a></li>
                    <li><a href="/betika-fast/" class="nav-tab">Betika Fast</a></li>
                    <li><a href="/crash-games/" class="nav-tab">Crash Games</a></li>
                    <li><a href="/live-score/" class="nav-tab">Live Score</a></li>
                    <li><a href="/app/" class="nav-tab">App</a></li>
                </ul>
            </nav>
            
            <!-- User Section -->
            <div class="user-section">
                {% if user.is_authenticated %}
                    <div class="user-info">
                        <span class="balance">KES {{ user.balance|default:"0.00" }}</span>
                        <div class="user-dropdown">
                            <button class="user-btn">
                                <i class="fas fa-user"></i>
                                {{ user.first_name|default:user.phone_number }}
                            </button>
                            <div class="dropdown-menu">
                                <a href="/accounts/profile/">Profile</a>
                                <a href="/betting/">My Bets</a>
                                <a href="/payments/">Payments</a>
                                <form method="post" action="{% url 'accounts:logout' %}" style="display: inline;">
                                    {% csrf_token %}
                                    <button type="submit" style="background: none; border: none; color: inherit; cursor: pointer; text-decoration: none; padding: 0; font: inherit;">
                                        Logout
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <div class="auth-buttons">
                        <a href="/accounts/login/" class="btn btn-outline">Login</a>
                        <a href="/accounts/register/" class="btn btn-primary">Register</a>
                    </div>
                {% endif %}
            </div>
        </div>
    </header>

    <!-- Main Layout -->
    <div class="main-layout">
        <!-- Left Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <span class="sidebar-title">Sports</span>
            </div>
            
            <nav class="sports-nav">
                <ul class="sports-list">
                    <li><a href="/sports/soccer/" class="sport-link">
                        <i class="fas fa-futbol"></i>
                        <span>Soccer</span>
                    </a></li>
                    <li><a href="/sports/boxing/" class="sport-link">
                        <i class="fas fa-fist-raised"></i>
                        <span>Boxing</span>
                    </a></li>
                    <li><a href="/sports/rugby/" class="sport-link">
                        <i class="fas fa-football-ball"></i>
                        <span>Rugby</span>
                    </a></li>
                    <li><a href="/sports/aussie-rules/" class="sport-link">
                        <i class="fas fa-football-ball"></i>
                        <span>Aussie Rules</span>
                    </a></li>
                    <li><a href="/sports/baseball/" class="sport-link">
                        <i class="fas fa-baseball-ball"></i>
                        <span>Baseball</span>
                    </a></li>
                    <li><a href="/sports/table-tennis/" class="sport-link">
                        <i class="fas fa-table-tennis"></i>
                        <span>Table Tennis</span>
                    </a></li>
                    <li><a href="/sports/cricket/" class="sport-link">
                        <i class="fas fa-cricket"></i>
                        <span>Cricket</span>
                    </a></li>
                    <li><a href="/sports/tennis/" class="sport-link">
                        <i class="fas fa-tennis-ball"></i>
                        <span>Tennis</span>
                    </a></li>
                    <li><a href="/sports/mma/" class="sport-link">
                        <i class="fas fa-fist-raised"></i>
                        <span>MMA</span>
                    </a></li>
                    <li><a href="/sports/basketball/" class="sport-link">
                        <i class="fas fa-basketball-ball"></i>
                        <span>Basketball</span>
                    </a></li>
                    <li><a href="/sports/water-polo/" class="sport-link">
                        <i class="fas fa-swimmer"></i>
                        <span>Water polo</span>
                    </a></li>
                    <li><a href="/sports/volleyball/" class="sport-link">
                        <i class="fas fa-volleyball-ball"></i>
                        <span>Volleyball</span>
                    </a></li>
                    <li><a href="/sports/esoccer/" class="sport-link">
                        <i class="fas fa-gamepad"></i>
                        <span>eSoccer</span>
                    </a></li>
                    <li><a href="/sports/handball/" class="sport-link">
                        <i class="fas fa-hand-paper"></i>
                        <span>Handball</span>
                    </a></li>
                    <li><a href="/sports/darts/" class="sport-link">
                        <i class="fas fa-bullseye"></i>
                        <span>Darts</span>
                    </a></li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Messages -->
            {% if messages %}
                <div class="messages-container">
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }}">
                            <i class="fas fa-info-circle"></i>
                            {{ message }}
                            <button class="alert-close">&times;</button>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
            
            {% block content %}{% endblock %}
        </main>

        <!-- Right Sidebar - Bet Slip -->
        <aside class="betslip-sidebar" id="betslip">
            <div class="betslip-header">
                <h3>Betslip</h3>
                <button class="betslip-toggle" id="betslipToggle">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <!-- Load Betslip Section -->
            <div class="load-betslip">
                <p>Do you have a shared betslip code? Enter it here.</p>
                <div class="betslip-code-input">
                    <input type="text" placeholder="e.g. VBmSU" id="betslipCode">
                    <button class="btn btn-primary" id="loadBetslip">Load Betslip</button>
                </div>
            </div>
            
            <!-- Bet Selections -->
            <div class="bet-selections" id="betSelections">
                <div class="empty-betslip" id="emptyBetslip">
                    <i class="fas fa-ticket-alt"></i>
                    <p>Your betslip is empty</p>
                    <small>Click on odds to add selections</small>
                </div>
            </div>
            
            <!-- Bet Totals -->
            <div class="bet-totals" id="betTotals" style="display: none;">
                <div class="bet-type-selector">
                    <label>
                        <input type="radio" name="betType" value="single" checked>
                        <span>Single</span>
                    </label>
                    <label>
                        <input type="radio" name="betType" value="multi">
                        <span>Multi</span>
                    </label>
                </div>
                
                <div class="stake-input">
                    <label for="stakeAmount">Stake Amount (KES)</label>
                    <input type="number" id="stakeAmount" placeholder="0.00" min="10" max="100000" step="0.01">
                    <div class="stake-warning" id="stakeWarning" style="display: none;"></div>
                </div>
                
                <div class="potential-winnings">
                    <div class="winnings-row">
                        <span>Total Odds:</span>
                        <span id="totalOdds">0.00</span>
                    </div>
                    <div class="winnings-row">
                        <span>Total Stake:</span>
                        <span id="totalStake">KES 0.00</span>
                    </div>
                    <div class="winnings-row">
                        <span>Potential Winnings:</span>
                        <span id="potentialWinnings">KES 0.00</span>
                    </div>
                    <div class="winnings-row profit-row">
                        <span>Potential Profit:</span>
                        <span id="potentialProfit">KES 0.00</span>
                    </div>
                </div>
                
                <div class="bet-actions">
                    <button class="btn btn-primary btn-place-bet" id="placeBet">
                        <i class="fas fa-check"></i>
                        Place Bet
                    </button>
                    
                    <button class="btn btn-outline btn-clear" id="clearBetslip">
                        <i class="fas fa-trash"></i>
                        Clear All
                    </button>
                </div>
                
                <!-- Quick Stake Buttons -->
                <div class="quick-stakes">
                    <span class="quick-stakes-label">Quick Stakes:</span>
                    <div class="quick-stake-buttons">
                        <button class="quick-stake-btn" data-amount="50">50</button>
                        <button class="quick-stake-btn" data-amount="100">100</button>
                        <button class="quick-stake-btn" data-amount="500">500</button>
                        <button class="quick-stake-btn" data-amount="1000">1K</button>
                    </div>
                </div>
            </div>
        </aside>
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobileOverlay"></div>

    <!-- Mobile Betslip Toggle Button -->
    <button class="mobile-betslip-toggle" id="mobileBetslipToggle">
        <i class="fas fa-ticket-alt"></i>
        <span class="badge" id="betslipCounter" style="display: none;">0</span>
    </button>

    <!-- JavaScript -->
    {% compress js %}
    <script src="{% static 'js/main.js' %}"></script>
    <script src="{% static 'js/betting/betslip.js' %}"></script>
    <script src="{% static 'js/navigation/sidebar.js' %}"></script>
    <script src="{% static 'js/performance/lazy-loading.js' %}"></script>
    <script src="{% static 'js/performance/cache-manager.js' %}"></script>
    <script src="{% static 'js/mobile/touch-interface.js' %}"></script>
    {% block extra_js %}{% endblock %}
    {% endcompress %}
</body>
</html>