from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IsAdmin<PERSON>ser, AllowAny
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db.models import Prefetch, Count, Q
from datetime import <PERSON><PERSON><PERSON>
from .models import Sport, Event, Market, Odds
from .services import OddsUpdateService, OddsValidationService
from core.cache_utils import cache_result, SportsCache, OddsCache
from core.query_optimization import QueryOptimizer
import json


class SportsListAPIView(APIView):
    """API endpoint for listing all active sports with caching and optimization"""
    permission_classes = [AllowAny]

    @cache_result(timeout=3600, key_prefix='sports_list_api')
    def get_sports_data(self):
        """Get optimized sports data with event counts"""
        # Use optimized query with annotation to avoid N+1 queries
        sports = Sport.active.annotate(
            active_events_count=Count(
                'events',
                filter=Q(events__status__in=['upcoming', 'live'])
            )
        ).order_by('display_order', 'name')

        data = []
        for sport in sports:
            data.append({
                'id': sport.id,
                'name': sport.name,
                'slug': sport.slug,
                'description': sport.description,
                'icon': sport.icon,
                'active_events_count': sport.active_events_count,
                'url': sport.get_absolute_url()
            })

        return data

    def get(self, request):
        sports_data = self.get_sports_data()
        return Response({
            'sports': sports_data,
            'count': len(sports_data)
        })


class SportDetailAPIView(APIView):
    """API endpoint for sport details"""
    permission_classes = [AllowAny]
    
    def get(self, request, sport_slug):
        sport = get_object_or_404(Sport.active, slug=sport_slug)
        
        return Response({
            'id': sport.id,
            'name': sport.name,
            'slug': sport.slug,
            'description': sport.description,
            'icon': sport.icon,
            'active_events_count': sport.get_active_events_count(),
            'url': sport.get_absolute_url()
        })


class SportEventsAPIView(APIView):
    """API endpoint for events in a specific sport with optimization"""
    permission_classes = [AllowAny]

    def get(self, request, sport_slug):
        sport = get_object_or_404(Sport.active, slug=sport_slug)

        # Apply filters
        status_filter = request.GET.get('status')

        # Use optimized query
        events_queryset = QueryOptimizer.optimize_event_queries(
            Event.active.filter(sport=sport)
        )

        if status_filter in ['upcoming', 'live']:
            events_queryset = events_queryset.filter(status=status_filter)

        # Limit and order
        events = events_queryset.order_by('start_time')[:50]

        data = []
        for event in events:
            data.append({
                'id': event.id,
                'home_team': event.home_team,
                'away_team': event.away_team,
                'start_time': event.start_time.isoformat(),
                'status': event.status,
                'league': event.league,
                'score': event.get_score_display(),
                'is_live': event.is_live,
                'is_featured': event.is_featured,
                'url': event.get_absolute_url()
            })

        return Response({
            'sport': sport.name,
            'events': data,
            'count': len(data)
        })


class EventsListAPIView(APIView):
    """API endpoint for listing events across all sports with optimization"""
    permission_classes = [AllowAny]

    def get(self, request):
        # Start with optimized base query
        events_queryset = QueryOptimizer.optimize_event_queries(Event.active)

        # Apply filters
        sport_slug = request.GET.get('sport')
        if sport_slug:
            events_queryset = events_queryset.filter(sport__slug=sport_slug)

        status_filter = request.GET.get('status')
        if status_filter in ['upcoming', 'live']:
            events_queryset = events_queryset.filter(status=status_filter)

        # Order and limit
        events = events_queryset.order_by('start_time')[:100]

        data = []
        for event in events:
            data.append({
                'id': event.id,
                'sport': event.sport.name,
                'sport_slug': event.sport.slug,
                'home_team': event.home_team,
                'away_team': event.away_team,
                'start_time': event.start_time.isoformat(),
                'status': event.status,
                'league': event.league,
                'score': event.get_score_display(),
                'is_live': event.is_live,
                'is_featured': event.is_featured,
                'url': event.get_absolute_url()
            })

        return Response({
            'events': data,
            'count': len(data)
        })


class EventDetailAPIView(APIView):
    """API endpoint for event details"""
    permission_classes = [AllowAny]
    
    def get(self, request, event_id):
        event = get_object_or_404(Event.objects.select_related('sport'), id=event_id)
        
        return Response({
            'id': event.pk,
            'sport': {
                'id': event.sport.pk,
                'name': event.sport.name,
                'slug': event.sport.slug
            },
            'home_team': event.home_team,
            'away_team': event.away_team,
            'start_time': event.start_time.isoformat(),
            'status': event.status,
            'league': event.league,
            'season': event.season,
            'round_info': event.round_info,
            'home_score': event.home_score,
            'away_score': event.away_score,
            'match_time': event.match_time,
            'is_featured': event.is_featured,
            'is_live': event.is_live,
            'is_finished': event.is_finished,
            'score_display': event.get_score_display()
        })


class EventMarketsAPIView(APIView):
    """API endpoint for event markets and odds"""
    permission_classes = [AllowAny]
    
    def get(self, request, event_id):
        event = get_object_or_404(Event, id=event_id)
        markets = Market.active.filter(event=event).prefetch_related('odds')
        
        data = []
        for market in markets:
            odds_data = []
            for odds in market.odds.filter(is_active=True):
                odds_data.append({
                    'id': odds.id,
                    'selection': odds.selection,
                    'odds_value': str(odds.odds_value),
                    'previous_odds': str(odds.previous_odds) if odds.previous_odds else None,
                    'has_changed': odds.has_changed,
                    'change_direction': odds.change_direction,
                    'implied_probability': odds.get_implied_probability(),
                    'last_updated': odds.last_updated.isoformat()
                })
            
            data.append({
                'id': market.id,
                'name': market.name,
                'market_type': market.market_type,
                'parameter': str(market.parameter) if market.parameter else None,
                'odds': odds_data
            })
        
        return Response({
            'event_id': event.pk,
            'markets': data
        })


class MarketDetailAPIView(APIView):
    """API endpoint for market details"""
    permission_classes = [AllowAny]
    
    def get(self, request, market_id):
        market = get_object_or_404(Market.active.select_related('event'), id=market_id)
        
        odds_data = []
        for odds in market.odds.filter(is_active=True):
            odds_data.append({
                'id': odds.id,
                'selection': odds.selection,
                'odds_value': str(odds.odds_value),
                'previous_odds': str(odds.previous_odds) if odds.previous_odds else None,
                'has_changed': odds.has_changed,
                'change_direction': odds.change_direction,
                'implied_probability': odds.get_implied_probability(),
                'last_updated': odds.last_updated.isoformat()
            })
        
        return Response({
            'id': market.id,
            'name': market.name,
            'market_type': market.market_type,
            'parameter': str(market.parameter) if market.parameter else None,
            'event': {
                'id': market.event.id,
                'name': str(market.event)
            },
            'odds': odds_data
        })


class MarketOddsAPIView(APIView):
    """API endpoint for market odds only"""
    permission_classes = [AllowAny]
    
    def get(self, request, market_id):
        market = get_object_or_404(Market.active, id=market_id)
        odds = market.odds.filter(is_active=True).order_by('display_order', 'selection')
        
        data = []
        for odd in odds:
            data.append({
                'id': odd.id,
                'selection': odd.selection,
                'odds_value': str(odd.odds_value),
                'previous_odds': str(odd.previous_odds) if odd.previous_odds else None,
                'has_changed': odd.has_changed,
                'change_direction': odd.change_direction,
                'implied_probability': odd.get_implied_probability(),
                'last_updated': odd.last_updated.isoformat()
            })
        
        return Response({
            'market_id': market.id,
            'market_name': market.name,
            'odds': data
        })


class UpdateOddsAPIView(APIView):
    """API endpoint for updating odds (admin only)"""
    permission_classes = [IsAdminUser]
    
    def post(self, request):
        try:
            data = request.data
            event_id = data.get('event_id')
            odds_data = data.get('odds_data', [])
            
            if not event_id or not odds_data:
                return Response({
                    'error': 'event_id and odds_data are required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Use the odds update service
            service = OddsUpdateService()
            result = service.update_event_odds(event_id, odds_data)
            
            if result['success']:
                return Response(result)
            else:
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            return Response({
                'error': f'Error updating odds: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class BulkUpdateOddsAPIView(APIView):
    """API endpoint for bulk odds updates (admin only)"""
    permission_classes = [IsAdminUser]
    
    def post(self, request):
        try:
            updates = request.data.get('updates', [])
            
            if not updates:
                return Response({
                    'error': 'updates array is required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            service = OddsUpdateService()
            result = service.bulk_update_odds(updates)
            
            return Response(result)
            
        except Exception as e:
            return Response({
                'error': f'Error in bulk update: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class OddsChangesAPIView(APIView):
    """API endpoint for getting recent odds changes"""
    permission_classes = [AllowAny]
    
    def get(self, request, event_id):
        # Get changes since specified time or last 1 hour
        since_param = request.GET.get('since')
        if since_param:
            try:
                since = timezone.datetime.fromisoformat(since_param.replace('Z', '+00:00'))
            except ValueError:
                since = timezone.now() - timedelta(hours=1)
        else:
            since = timezone.now() - timedelta(hours=1)
        
        service = OddsUpdateService()
        changes = service.get_odds_changes(event_id, since)
        
        return Response({
            'event_id': event_id,
            'since': since.isoformat(),
            'changes': changes,
            'count': len(changes)
        })


class ValidateOddsAPIView(APIView):
    """API endpoint for validating odds (admin only)"""
    permission_classes = [IsAdminUser]
    
    def post(self, request):
        try:
            market_id = request.data.get('market_id')
            
            if not market_id:
                return Response({
                    'error': 'market_id is required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            validation_result = OddsValidationService.validate_market_odds(market_id)
            return Response(validation_result)
            
        except Exception as e:
            return Response({
                'error': f'Error validating odds: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SearchEventsAPIView(APIView):
    """API endpoint for searching events"""
    permission_classes = [AllowAny]
    
    def get(self, request):
        query = request.GET.get('q', '').strip()
        sport_filter = request.GET.get('sport')
        status_filter = request.GET.get('status', 'active')
        
        events = Event.objects.select_related('sport')
        
        # Apply status filter
        if status_filter == 'active':
            events = events.filter(status__in=['upcoming', 'live'])
        elif status_filter in ['upcoming', 'live', 'finished']:
            events = events.filter(status=status_filter)
        
        # Apply sport filter
        if sport_filter:
            events = events.filter(sport__slug=sport_filter)
        
        # Apply search query
        if query:
            from django.db.models import Q
            events = events.filter(
                Q(home_team__icontains=query) |
                Q(away_team__icontains=query) |
                Q(league__icontains=query)
            )
        
        data = []
        for event in events[:50]:  # Limit to 50 results
            data.append({
                'id': event.pk,
                'sport': event.sport.name,
                'home_team': event.home_team,
                'away_team': event.away_team,
                'start_time': event.start_time.isoformat(),
                'status': event.status,
                'league': event.league,
                'score': event.get_score_display(),
                'is_live': event.is_live,
                'url': event.get_absolute_url()
            })
        
        return Response({
            'query': query,
            'events': data,
            'count': len(data)
        })


class FilterEventsAPIView(APIView):
    """API endpoint for filtering events with advanced options"""
    permission_classes = [AllowAny]
    
    def get(self, request):
        # Get filter parameters
        sport_ids = request.GET.getlist('sports')
        leagues = request.GET.getlist('leagues')
        status_filter = request.GET.get('status', 'active')
        date_from = request.GET.get('date_from')
        date_to = request.GET.get('date_to')
        
        events = Event.objects.select_related('sport')
        
        # Apply filters
        if status_filter == 'active':
            events = events.filter(status__in=['upcoming', 'live'])
        elif status_filter in ['upcoming', 'live', 'finished']:
            events = events.filter(status=status_filter)
        
        if sport_ids:
            events = events.filter(sport__id__in=sport_ids)
        
        if leagues:
            events = events.filter(league__in=leagues)
        
        if date_from:
            try:
                from datetime import datetime
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
                events = events.filter(start_time__date__gte=date_from_obj)
            except ValueError:
                pass
        
        if date_to:
            try:
                from datetime import datetime
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
                events = events.filter(start_time__date__lte=date_to_obj)
            except ValueError:
                pass
        
        data = []
        for event in events[:100]:  # Limit to 100 results
            data.append({
                'id': event.pk,
                'sport': event.sport.name,
                'home_team': event.home_team,
                'away_team': event.away_team,
                'start_time': event.start_time.isoformat(),
                'status': event.status,
                'league': event.league,
                'score': event.get_score_display(),
                'is_live': event.is_live,
                'url': event.get_absolute_url()
            })
        
        return Response({
            'events': data,
            'count': len(data),
            'filters': {
                'sports': sport_ids,
                'leagues': leagues,
                'status': status_filter,
                'date_from': date_from,
                'date_to': date_to
            }
        })