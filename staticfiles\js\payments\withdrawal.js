/**
 * Withdrawal functionality JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    const withdrawalForm = document.getElementById('withdrawalForm');
    const paymentMethodRadios = document.querySelectorAll('input[name="payment_method"]');
    const mpesaDetails = document.getElementById('mpesa-details');
    const bankDetails = document.getElementById('bank-details');
    const amountInput = document.querySelector('input[name="amount"]');
    const submitBtn = document.getElementById('submitBtn');
    const savedMethods = document.querySelectorAll('.use-saved-method');

    // Handle payment method selection
    paymentMethodRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            togglePaymentDetails(this.value);
        });
    });

    // Handle saved payment method selection
    savedMethods.forEach(btn => {
        btn.addEventListener('click', function() {
            const methodDiv = this.closest('.saved-method');
            const methodType = methodDiv.dataset.methodType;
            
            // Select the appropriate payment method radio
            const targetRadio = document.querySelector(`input[name="payment_method"][value="${methodType === 'mpesa' ? 'mpesa' : 'bank_transfer'}"]`);
            if (targetRadio) {
                targetRadio.checked = true;
                togglePaymentDetails(targetRadio.value);
                
                // Fill in the details
                if (methodType === 'mpesa') {
                    document.querySelector('input[name="mpesa_phone_number"]').value = methodDiv.dataset.mpesaPhone;
                } else {
                    document.querySelector('input[name="bank_name"]').value = methodDiv.dataset.bankName;
                    document.querySelector('input[name="account_number"]').value = methodDiv.dataset.accountNumber;
                    document.querySelector('input[name="account_name"]').value = methodDiv.dataset.accountName;
                }
            }
        });
    });

    // Amount validation
    if (amountInput) {
        amountInput.addEventListener('input', function() {
            validateAmount(this.value);
        });
    }

    // Form submission
    if (withdrawalForm) {
        withdrawalForm.addEventListener('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
                return false;
            }
            
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        });
    }

    // Initialize form state
    const checkedRadio = document.querySelector('input[name="payment_method"]:checked');
    if (checkedRadio) {
        togglePaymentDetails(checkedRadio.value);
    }

    function togglePaymentDetails(method) {
        // Hide all payment details
        mpesaDetails.style.display = 'none';
        bankDetails.style.display = 'none';
        
        // Show relevant details
        if (method === 'mpesa') {
            mpesaDetails.style.display = 'block';
        } else if (method === 'bank_transfer') {
            bankDetails.style.display = 'block';
        }
    }

    function validateAmount(amount) {
        const amountValue = parseFloat(amount);
        const userBalance = parseFloat(document.querySelector('.balance-amount').textContent.replace('KES ', '').replace(',', ''));
        const errorDiv = amountInput.parentNode.querySelector('.field-errors');
        
        // Clear previous errors
        if (errorDiv) {
            errorDiv.remove();
        }
        
        let errors = [];
        
        if (isNaN(amountValue) || amountValue <= 0) {
            errors.push('Please enter a valid amount');
        } else if (amountValue < 50) {
            errors.push('Minimum withdrawal amount is KES 50.00');
        } else if (amountValue > 100000) {
            errors.push('Maximum withdrawal amount is KES 100,000.00');
        } else if (amountValue > userBalance) {
            errors.push(`Insufficient balance. Available: KES ${userBalance.toFixed(2)}`);
        }
        
        if (errors.length > 0) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'field-errors';
            errors.forEach(error => {
                const errorSpan = document.createElement('span');
                errorSpan.className = 'error';
                errorSpan.textContent = error;
                errorDiv.appendChild(errorSpan);
            });
            amountInput.parentNode.appendChild(errorDiv);
            return false;
        }
        
        return true;
    }

    function validateForm() {
        let isValid = true;
        
        // Validate amount
        const amount = amountInput.value;
        if (!validateAmount(amount)) {
            isValid = false;
        }
        
        // Validate payment method selection
        const selectedMethod = document.querySelector('input[name="payment_method"]:checked');
        if (!selectedMethod) {
            showError('Please select a withdrawal method');
            isValid = false;
        } else {
            // Validate method-specific fields
            if (selectedMethod.value === 'mpesa') {
                const mpesaPhone = document.querySelector('input[name="mpesa_phone_number"]').value;
                if (!mpesaPhone) {
                    showError('M-Pesa phone number is required');
                    isValid = false;
                } else if (!isValidKenyanPhone(mpesaPhone)) {
                    showError('Please enter a valid Kenyan phone number');
                    isValid = false;
                }
            } else if (selectedMethod.value === 'bank_transfer') {
                const bankName = document.querySelector('input[name="bank_name"]').value;
                const accountNumber = document.querySelector('input[name="account_number"]').value;
                const accountName = document.querySelector('input[name="account_name"]').value;
                
                if (!bankName || !accountNumber || !accountName) {
                    showError('All bank account details are required');
                    isValid = false;
                }
            }
        }
        
        // Validate confirmation checkbox
        const confirmCheckbox = document.querySelector('input[name="confirm_withdrawal"]');
        if (!confirmCheckbox.checked) {
            showError('Please confirm the withdrawal details');
            isValid = false;
        }
        
        return isValid;
    }

    function isValidKenyanPhone(phone) {
        // Remove spaces and special characters
        const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
        
        // Check if it matches Kenyan phone number patterns
        const patterns = [
            /^\+254[0-9]{9}$/,  // +254xxxxxxxxx
            /^254[0-9]{9}$/,    // 254xxxxxxxxx
            /^0[0-9]{9}$/       // 0xxxxxxxxx
        ];
        
        return patterns.some(pattern => pattern.test(cleanPhone));
    }

    function showError(message) {
        // Remove existing error messages
        const existingErrors = document.querySelectorAll('.form-error-message');
        existingErrors.forEach(error => error.remove());
        
        // Create new error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-error form-error-message';
        errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
        
        // Insert before form actions
        const formActions = document.querySelector('.form-actions');
        formActions.parentNode.insertBefore(errorDiv, formActions);
        
        // Scroll to error
        errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    // Real-time balance check
    function checkDailyLimit() {
        const amount = parseFloat(amountInput.value);
        if (amount > 0) {
            // This would typically make an AJAX call to check daily limits
            // For now, we'll just show a warning for large amounts
            if (amount > 10000) {
                const warningDiv = document.createElement('div');
                warningDiv.className = 'alert alert-warning';
                warningDiv.innerHTML = '<i class="fas fa-info-circle"></i> Large withdrawal amounts may require additional verification.';
                
                const existingWarning = amountInput.parentNode.querySelector('.alert-warning');
                if (existingWarning) {
                    existingWarning.remove();
                }
                
                amountInput.parentNode.appendChild(warningDiv);
            }
        }
    }

    // Add amount change listener for daily limit check
    if (amountInput) {
        amountInput.addEventListener('blur', checkDailyLimit);
    }
});

// Utility functions for formatting
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-KE', {
        style: 'currency',
        currency: 'KES',
        minimumFractionDigits: 2
    }).format(amount);
}

function formatPhoneNumber(phone) {
    // Format phone number for display
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.startsWith('254')) {
        return `+${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;
    } else if (cleaned.startsWith('0')) {
        return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7)}`;
    }
    return phone;
}