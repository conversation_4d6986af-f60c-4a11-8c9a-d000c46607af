"""
Template tags for sports functionality
"""

from django import template
from sports.models import Sport

register = template.Library()


@register.simple_tag
def get_sports():
    """
    Get all active sports ordered by display_order
    """
    return Sport.objects.filter(is_active=True).order_by('display_order', 'name')


@register.inclusion_tag('sports/partials/sports_list.html')
def sports_navigation():
    """
    Render sports navigation
    """
    sports = Sport.objects.filter(is_active=True).order_by('display_order', 'name')
    return {'sports': sports}


@register.filter
def has_events(sport):
    """
    Check if sport has any active events
    """
    return sport.events.filter(status__in=['upcoming', 'live']).exists()


@register.simple_tag
def get_sport_events_count(sport):
    """
    Get count of active events for a sport
    """
    return sport.events.filter(status__in=['upcoming', 'live']).count()
