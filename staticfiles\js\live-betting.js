/**
 * Live Betting WebSocket Client
 * Handles real-time odds updates and live betting functionality
 */

class LiveBettingClient {
    constructor() {
        this.oddsSocket = null;
        this.eventSocket = null;
        this.currentEventId = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // Start with 1 second
        this.heartbeatInterval = null;
        this.subscriptions = {
            sportIds: [],
            eventIds: []
        };
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.connectToLiveOdds();
        this.startHeartbeat();
    }
    
    setupEventListeners() {
        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseConnections();
            } else {
                this.resumeConnections();
            }
        });
        
        // Handle window beforeunload
        window.addEventListener('beforeunload', () => {
            this.disconnect();
        });
        
        // Handle network status changes
        window.addEventListener('online', () => {
            console.log('Network connection restored');
            this.reconnectAll();
        });
        
        window.addEventListener('offline', () => {
            console.log('Network connection lost');
            this.handleConnectionLoss();
        });
    }
    
    connectToLiveOdds() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws/live-odds/`;
        
        try {
            this.oddsSocket = new WebSocket(wsUrl);
            this.setupOddsSocketHandlers();
        } catch (error) {
            console.error('Failed to create WebSocket connection:', error);
            this.scheduleReconnect();
        }
    }
    
    setupOddsSocketHandlers() {
        this.oddsSocket.onopen = (event) => {
            console.log('Connected to live odds feed');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.reconnectDelay = 1000;
            this.updateConnectionStatus(true);
            
            // Subscribe to odds if we have preferences
            if (this.subscriptions.sportIds.length > 0 || this.subscriptions.eventIds.length > 0) {
                this.subscribeToOdds(this.subscriptions.sportIds, this.subscriptions.eventIds);
            }
        };
        
        this.oddsSocket.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleOddsMessage(data);
            } catch (error) {
                console.error('Error parsing WebSocket message:', error);
            }
        };
        
        this.oddsSocket.onclose = (event) => {
            console.log('Disconnected from live odds feed:', event.code, event.reason);
            this.isConnected = false;
            this.updateConnectionStatus(false);
            
            // Attempt to reconnect unless it was a clean close
            if (event.code !== 1000) {
                this.scheduleReconnect();
            }
        };
        
        this.oddsSocket.onerror = (error) => {
            console.error('WebSocket error:', error);
            this.handleConnectionError();
        };
    }
    
    handleOddsMessage(data) {
        switch (data.type) {
            case 'connection_established':
                console.log('Connection established for user:', data.user_id);
                break;
                
            case 'odds_subscribed':
                console.log('Subscribed to odds:', data);
                this.showNotification('Subscribed to live odds updates', 'success');
                break;
                
            case 'odds_update':
                this.handleOddsUpdate(data.data);
                break;
                
            case 'pong':
                // Heartbeat response received
                break;
                
            case 'error':
                console.error('WebSocket error:', data.message);
                this.showNotification(data.message, 'error');
                break;
                
            default:
                console.log('Unknown message type:', data.type);
        }
    }
    
    handleOddsUpdate(oddsData) {
        console.log('Odds update received:', oddsData);
        
        if (oddsData.odds && oddsData.odds.updated_odds) {
            oddsData.odds.updated_odds.forEach(odds => {
                this.updateOddsDisplay(odds);
                this.showOddsChangeAnimation(odds);
            });
        }
        
        // Update last update timestamp
        this.updateLastUpdateTime();
    }
    
    updateOddsDisplay(odds) {
        const oddsElement = document.querySelector(`[data-odds-id="${odds.id}"]`);
        if (oddsElement) {
            const oldValue = oddsElement.textContent;
            const newValue = odds.new_value;
            
            // Update the display value
            oddsElement.textContent = newValue;
            
            // Add change indicator class
            const changeClass = parseFloat(newValue) > parseFloat(oldValue) ? 'odds-increased' : 'odds-decreased';
            oddsElement.classList.add(changeClass);
            
            // Remove the class after animation
            setTimeout(() => {
                oddsElement.classList.remove(changeClass);
            }, 2000);
            
            // Update any related bet slip odds
            this.updateBetSlipOdds(odds.id, newValue);
        }
    }
    
    showOddsChangeAnimation(odds) {
        const oddsElement = document.querySelector(`[data-odds-id="${odds.id}"]`);
        if (oddsElement) {
            // Create change indicator
            const changeIndicator = document.createElement('div');
            changeIndicator.className = 'odds-change-indicator';
            
            const changePercentage = odds.change_percentage;
            const isIncrease = changePercentage > 0;
            
            changeIndicator.innerHTML = `
                <span class="change-arrow ${isIncrease ? 'up' : 'down'}">
                    ${isIncrease ? '↑' : '↓'}
                </span>
                <span class="change-percentage">${Math.abs(changePercentage).toFixed(1)}%</span>
            `;
            
            changeIndicator.classList.add(isIncrease ? 'increase' : 'decrease');
            
            // Position the indicator
            const rect = oddsElement.getBoundingClientRect();
            changeIndicator.style.position = 'fixed';
            changeIndicator.style.left = `${rect.right + 5}px`;
            changeIndicator.style.top = `${rect.top}px`;
            changeIndicator.style.zIndex = '1000';
            
            document.body.appendChild(changeIndicator);
            
            // Animate and remove
            setTimeout(() => {
                changeIndicator.style.opacity = '0';
                changeIndicator.style.transform = 'translateY(-20px)';
            }, 100);
            
            setTimeout(() => {
                document.body.removeChild(changeIndicator);
            }, 2100);
        }
    }
    
    connectToEvent(eventId) {
        if (this.eventSocket && this.currentEventId === eventId) {
            return; // Already connected to this event
        }
        
        // Disconnect from previous event if any
        if (this.eventSocket) {
            this.eventSocket.close();
        }
        
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws/live-betting/${eventId}/`;
        
        try {
            this.eventSocket = new WebSocket(wsUrl);
            this.currentEventId = eventId;
            this.setupEventSocketHandlers();
        } catch (error) {
            console.error('Failed to connect to event WebSocket:', error);
        }
    }
    
    setupEventSocketHandlers() {
        this.eventSocket.onopen = (event) => {
            console.log(`Connected to live betting for event ${this.currentEventId}`);
            this.updateEventConnectionStatus(true);
        };
        
        this.eventSocket.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleEventMessage(data);
            } catch (error) {
                console.error('Error parsing event WebSocket message:', error);
            }
        };
        
        this.eventSocket.onclose = (event) => {
            console.log(`Disconnected from event ${this.currentEventId}:`, event.code);
            this.updateEventConnectionStatus(false);
        };
        
        this.eventSocket.onerror = (error) => {
            console.error('Event WebSocket error:', error);
        };
    }
    
    handleEventMessage(data) {
        switch (data.type) {
            case 'event_joined':
                console.log('Joined event:', data.event_id);
                this.displayEventDetails(data.event_details);
                break;
                
            case 'event_update':
                this.handleEventUpdate(data.data);
                break;
                
            case 'odds_change':
                this.handleOddsUpdate(data.data);
                break;
                
            case 'bet_placed':
                this.handleBetPlaced(data);
                break;
                
            case 'bet_update':
                this.handleBetUpdate(data.data);
                break;
                
            case 'event_finished':
                this.handleEventFinished(data.data);
                break;
                
            case 'live_odds':
                this.displayLiveOdds(data.odds);
                break;
                
            case 'event_status':
                this.updateEventStatus(data.status);
                break;
                
            case 'error':
                console.error('Event WebSocket error:', data.message);
                this.showNotification(data.message, 'error');
                break;
        }
    }
    
    // Public methods for interaction
    subscribeToOdds(sportIds = [], eventIds = []) {
        this.subscriptions.sportIds = sportIds;
        this.subscriptions.eventIds = eventIds;
        
        if (this.oddsSocket && this.oddsSocket.readyState === WebSocket.OPEN) {
            this.oddsSocket.send(JSON.stringify({
                type: 'subscribe_odds',
                sport_ids: sportIds,
                event_ids: eventIds
            }));
        }
    }
    
    placeLiveBet(marketId, oddsId, stake) {
        if (this.eventSocket && this.eventSocket.readyState === WebSocket.OPEN) {
            this.eventSocket.send(JSON.stringify({
                type: 'place_bet',
                market_id: marketId,
                odds_id: oddsId,
                stake: stake
            }));
        } else {
            this.showNotification('Not connected to live betting', 'error');
        }
    }
    
    requestEventStatus() {
        if (this.eventSocket && this.eventSocket.readyState === WebSocket.OPEN) {
            this.eventSocket.send(JSON.stringify({
                type: 'get_event_status'
            }));
        }
    }
    
    requestLiveOdds() {
        if (this.eventSocket && this.eventSocket.readyState === WebSocket.OPEN) {
            this.eventSocket.send(JSON.stringify({
                type: 'get_live_odds'
            }));
        }
    }
    
    // Utility methods
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (this.oddsSocket && this.oddsSocket.readyState === WebSocket.OPEN) {
                this.oddsSocket.send(JSON.stringify({ type: 'ping' }));
            }
        }, 30000); // Ping every 30 seconds
    }
    
    scheduleReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${this.reconnectDelay}ms`);
            
            setTimeout(() => {
                this.connectToLiveOdds();
            }, this.reconnectDelay);
            
            // Exponential backoff
            this.reconnectDelay = Math.min(this.reconnectDelay * 2, 30000);
        } else {
            console.error('Max reconnection attempts reached');
            this.showNotification('Connection lost. Please refresh the page.', 'error');
        }
    }
    
    updateConnectionStatus(connected) {
        const statusElement = document.getElementById('connection-status');
        if (statusElement) {
            statusElement.className = connected ? 'connected' : 'disconnected';
            statusElement.textContent = connected ? 'Live' : 'Disconnected';
        }
    }
    
    updateEventConnectionStatus(connected) {
        const statusElement = document.getElementById('event-connection-status');
        if (statusElement) {
            statusElement.className = connected ? 'connected' : 'disconnected';
            statusElement.textContent = connected ? 'Live Betting Active' : 'Live Betting Inactive';
        }
    }
    
    showNotification(message, type = 'info') {
        // Create or update notification element
        let notification = document.getElementById('live-betting-notification');
        if (!notification) {
            notification = document.createElement('div');
            notification.id = 'live-betting-notification';
            notification.className = 'live-betting-notification';
            document.body.appendChild(notification);
        }
        
        notification.className = `live-betting-notification ${type}`;
        notification.textContent = message;
        notification.style.display = 'block';
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            notification.style.display = 'none';
        }, 5000);
    }
    
    updateLastUpdateTime() {
        const timeElement = document.getElementById('last-update-time');
        if (timeElement) {
            timeElement.textContent = new Date().toLocaleTimeString();
        }
    }
    
    disconnect() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
        }
        
        if (this.oddsSocket) {
            this.oddsSocket.close(1000, 'Client disconnect');
        }
        
        if (this.eventSocket) {
            this.eventSocket.close(1000, 'Client disconnect');
        }
    }
    
    // Placeholder methods for UI updates (to be implemented based on specific UI)
    displayEventDetails(eventDetails) {
        console.log('Event details:', eventDetails);
    }
    
    handleEventUpdate(eventData) {
        console.log('Event update:', eventData);
    }
    
    handleBetPlaced(betData) {
        console.log('Bet placed:', betData);
        this.showNotification('Bet placed successfully!', 'success');
    }
    
    handleBetUpdate(betData) {
        console.log('Bet update:', betData);
    }
    
    handleEventFinished(eventData) {
        console.log('Event finished:', eventData);
        this.showNotification('Event has finished', 'info');
    }
    
    displayLiveOdds(oddsData) {
        console.log('Live odds:', oddsData);
    }
    
    updateEventStatus(statusData) {
        console.log('Event status:', statusData);
    }
    
    updateBetSlipOdds(oddsId, newValue) {
        // Update bet slip if odds are selected
        const betSlipOdds = document.querySelector(`#bet-slip [data-odds-id="${oddsId}"]`);
        if (betSlipOdds) {
            betSlipOdds.textContent = newValue;
            // Highlight the change
            betSlipOdds.classList.add('odds-updated');
            setTimeout(() => {
                betSlipOdds.classList.remove('odds-updated');
            }, 2000);
        }
    }
    
    pauseConnections() {
        // Pause heartbeat when page is hidden
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
        }
    }
    
    resumeConnections() {
        // Resume heartbeat when page becomes visible
        this.startHeartbeat();
        
        // Check connection status and reconnect if needed
        if (!this.isConnected) {
            this.reconnectAll();
        }
    }
    
    reconnectAll() {
        this.connectToLiveOdds();
        if (this.currentEventId) {
            this.connectToEvent(this.currentEventId);
        }
    }
    
    handleConnectionLoss() {
        this.isConnected = false;
        this.updateConnectionStatus(false);
        this.updateEventConnectionStatus(false);
    }
    
    handleConnectionError() {
        this.showNotification('Connection error occurred', 'error');
    }
}

// Initialize live betting client when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.liveBettingClient = new LiveBettingClient();
});

// Utility functions for live betting UI
class LiveBettingUI {
    static createOddsChangeIndicator(odds, isIncrease) {
        const indicator = document.createElement('div');
        indicator.className = `odds-change-flash ${isIncrease ? 'increase' : 'decrease'}`;
        return indicator;
    }

    static animateOddsChange(element, isIncrease) {
        element.classList.add('odds-changing');
        element.classList.add(isIncrease ? 'odds-increased' : 'odds-decreased');

        setTimeout(() => {
            element.classList.remove('odds-changing', 'odds-increased', 'odds-decreased');
        }, 2000);
    }

    static updateEventTimer(timeElapsed) {
        const timerElement = document.getElementById('event-timer');
        if (timerElement && timeElapsed !== null) {
            timerElement.textContent = `${timeElapsed}'`;
        }
    }

    static updateEventScore(score) {
        const scoreElement = document.getElementById('event-score');
        if (scoreElement && score) {
            scoreElement.textContent = score;
            scoreElement.classList.add('score-updated');
            setTimeout(() => {
                scoreElement.classList.remove('score-updated');
            }, 2000);
        }
    }

    static createBetSlipEntry(marketName, oddsName, oddsValue, stake) {
        return `
            <div class="bet-slip-entry" data-odds-id="${oddsValue}">
                <div class="bet-details">
                    <div class="market-name">${marketName}</div>
                    <div class="odds-name">${oddsName}</div>
                    <div class="odds-value" data-odds-id="${oddsValue}">${oddsValue}</div>
                </div>
                <div class="stake-input">
                    <input type="number" class="stake" value="${stake}" min="1" step="0.01">
                </div>
                <div class="potential-winnings">
                    Win: KES <span class="winnings-amount">${(parseFloat(stake) * parseFloat(oddsValue)).toFixed(2)}</span>
                </div>
                <button class="remove-bet" onclick="this.parentElement.remove()">×</button>
            </div>
        `;
    }
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { LiveBettingClient, LiveBettingUI };
}
