/* Home Page Styles */

/* Promotional Banner */
.promo-banner {
    background-color: var(--secondary-dark);
    border-radius: 8px;
    margin-bottom: 20px;
    overflow: hidden;
}

.promo-content {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    position: relative;
}

.promo-image {
    flex-shrink: 0;
    width: 60px;
    height: 60px;
    background-color: var(--warning-orange);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
}

.promo-image i {
    font-size: 30px;
    color: white;
}

.promo-text {
    flex: 1;
}

.promo-text h2 {
    color: var(--warning-orange);
    font-size: 24px;
    margin: 0 0 5px 0;
    font-weight: bold;
}

.promo-text p {
    color: var(--text-primary);
    margin: 0;
    font-size: 16px;
}

.promo-text strong {
    color: var(--accent-green);
}

.promo-action {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    gap: 15px;
}

.promo-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 20px;
    cursor: pointer;
}

/* Event Tabs */
.event-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.tab-list {
    display: flex;
    gap: 10px;
    overflow-x: auto;
    padding-bottom: 5px;
}

.tab-btn {
    background: var(--secondary-dark);
    border: none;
    color: var(--text-secondary);
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.tab-btn:hover {
    background: var(--tertiary-dark);
    color: var(--text-primary);
}

.tab-btn.active {
    background: var(--accent-green);
    color: white;
}

.event-filters {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-group {
    position: relative;
}

.filter-btn {
    background: var(--secondary-dark);
    border: none;
    color: var(--text-secondary);
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-btn i {
    font-size: 12px;
}

.filter-select {
    background: var(--secondary-dark);
    border: none;
    color: var(--text-secondary);
    padding: 10px 30px 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23b0b0b0' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: calc(100% - 10px) center;
}

/* Events Content */
.events-content {
    margin-bottom: 30px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.event-card {
    background-color: var(--secondary-dark);
    border-radius: 8px;
    margin-bottom: 15px;
    overflow: hidden;
}

.event-header {
    padding: 12px 15px;
    border-bottom: 1px solid var(--border-color);
}

.event-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.competition {
    color: var(--text-secondary);
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.competition i {
    color: var(--accent-green);
}

.event-time {
    color: var(--text-secondary);
    font-size: 13px;
    font-weight: 500;
}

.event-match {
    padding: 15px;
}

.teams {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 15px;
}

.team-name {
    font-weight: bold;
    color: var(--text-primary);
    font-size: 16px;
    text-align: center;
}

.vs {
    color: var(--text-secondary);
    font-size: 14px;
}

.odds-row {
    margin-bottom: 15px;
}

.market-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.market-label {
    color: var(--text-secondary);
    font-size: 13px;
}

.market-options {
    display: flex;
    gap: 40px;
}

.market-options span {
    color: var(--text-secondary);
    font-size: 13px;
    width: 30px;
    text-align: center;
}

.odds-buttons {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.odds-btn {
    flex: 1;
    background-color: var(--primary-dark);
    border: none;
    color: var(--text-primary);
    padding: 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.odds-btn:hover {
    background-color: var(--accent-green);
    color: white;
}

.odds-btn.selected {
    background-color: var(--accent-green);
    color: white;
}

.market-expansion {
    text-align: center;
}

.expand-btn {
    background: none;
    border: none;
    color: var(--accent-green);
    font-size: 14px;
    cursor: pointer;
    padding: 5px 10px;
}

.expand-btn:hover {
    text-decoration: underline;
}

.load-more {
    text-align: center;
    margin-top: 20px;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    background-color: var(--secondary-dark);
    border-radius: 8px;
}

.empty-state i {
    font-size: 48px;
    color: var(--text-muted);
    margin-bottom: 15px;
}

.empty-state h3 {
    color: var(--text-primary);
    margin-bottom: 10px;
}

.empty-state p {
    color: var(--text-secondary);
}

/* Welcome Overlay */
.welcome-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.welcome-modal {
    background-color: var(--secondary-dark);
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    position: relative;
    padding: 30px;
}

.welcome-content {
    text-align: center;
}

.welcome-content h2 {
    color: var(--accent-green);
    margin-bottom: 15px;
}

.welcome-content p {
    color: var(--text-primary);
    margin-bottom: 20px;
}

.welcome-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.welcome-close {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 20px;
    cursor: pointer;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .promo-content {
        flex-direction: column;
        text-align: center;
        padding: 20px;
    }
    
    .promo-image {
        margin-right: 0;
        margin-bottom: 15px;
    }
    
    .promo-action {
        margin-top: 15px;
    }
    
    .event-tabs {
        flex-direction: column;
        align-items: stretch;
    }
    
    .tab-list {
        justify-content: space-between;
    }
    
    .event-filters {
        justify-content: space-between;
    }
    
    .teams {
        flex-direction: column;
        gap: 5px;
    }
    
    .vs {
        margin: 5px 0;
    }
}

@media (max-width: 480px) {
    .tab-btn {
        padding: 8px 15px;
        font-size: 13px;
    }
    
    .filter-btn,
    .filter-select {
        padding: 8px 10px;
        font-size: 13px;
    }
    
    .odds-buttons {
        flex-direction: column;
        gap: 10px;
    }
    
    .welcome-modal {
        padding: 20px;
    }
    
    .welcome-actions {
        flex-direction: column;
        gap: 10px;
    }
}