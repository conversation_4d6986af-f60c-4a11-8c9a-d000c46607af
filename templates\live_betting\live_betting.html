{% extends 'base.html' %}
{% load static %}

{% block title %}Live Betting - Betika Clone{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/pages/home.css' %}">
<link rel="stylesheet" href="{% static 'css/live-betting.css' %}">
{% endblock %}

{% block content %}
<!-- Promotional Banner -->
<div class="promo-banner">
    <div class="promo-content">
        <div class="promo-image">
            <i class="fas fa-circle"></i>
        </div>
        <div class="promo-text">
            <h2>Live Betting</h2>
            <p>Bet on live events with <strong>real-time odds</strong> and instant action!</p>
        </div>
        <div class="promo-action">
            <div class="status-indicators">
                <div id="connection-status" class="connection-status disconnected">Connecting...</div>
            </div>
            <button class="promo-close">&times;</button>
        </div>
    </div>
</div>

<!-- Event Navigation Tabs -->
<div class="event-tabs">
    <div class="tab-list">
        <button class="tab-btn active" data-tab="live-events">Live Events</button>
        <button class="tab-btn" data-tab="starting-soon">Starting Soon</button>
        <button class="tab-btn" data-tab="popular">Popular</button>
        <button class="tab-btn" data-tab="my-bets">My Live Bets</button>
    </div>

    <div class="event-filters">
        <div class="filter-group">
            <div class="last-update">
                Last update: <span id="last-update-time">--:--:--</span>
            </div>
        </div>
        <div class="filter-group">
            <select class="filter-select">
                <option>All Sports</option>
                <option>Football</option>
                <option>Basketball</option>
                <option>Tennis</option>
            </select>
        </div>
    </div>
</div>

<!-- Events Content -->
<div class="events-content">
    <div class="tab-content active" id="live-events">
        <div class="live-betting-container">

        <!-- Event Status Display -->
        <div class="event-status" id="event-status-display" style="display: none;">
            <div class="event-info">
                <h2 id="event-name">Loading...</h2>
                <div class="event-meta">
                    <span id="event-sport">Sport</span> • 
                    <span class="event-status-badge live" id="event-status-badge">LIVE</span>
                </div>
            </div>
            <div class="event-live-data">
                <div class="event-timer" id="event-timer">0'</div>
                <div class="event-score" id="event-score">0-0</div>
            </div>
        </div>

        <!-- Markets and Odds -->
        <div id="markets-container">
            <!-- Markets will be dynamically loaded here -->
            <div class="loading-message">
                <p>Loading live events...</p>
            </div>
        </div>

        <!-- Sample Market (for demonstration) -->
        <div class="market-section" style="display: none;" id="sample-market">
            <div class="market-title">Match Winner</div>
            <div class="odds-grid">
                <div class="odds-option" onclick="addToBetSlip('market1', 'odds1', 'Home Win', '2.10')">
                    <div class="odds-name">Home Win</div>
                    <div class="odds-value" data-odds-id="odds1">2.10</div>
                </div>
                <div class="odds-option" onclick="addToBetSlip('market1', 'odds2', 'Draw', '3.20')">
                    <div class="odds-name">Draw</div>
                    <div class="odds-value" data-odds-id="odds2">3.20</div>
                </div>
                <div class="odds-option" onclick="addToBetSlip('market1', 'odds3', 'Away Win', '3.50')">
                    <div class="odds-name">Away Win</div>
                    <div class="odds-value" data-odds-id="odds3">3.50</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bet Slip Panel -->
    <div class="bet-slip-panel">
        <div class="bet-slip-header">
            <div class="bet-slip-title">Bet Slip</div>
            <button class="clear-bet-slip" onclick="clearBetSlip()">Clear All</button>
        </div>

        <div id="bet-slip-entries">
            <!-- Bet slip entries will be added here dynamically -->
            <div class="empty-bet-slip">
                <p>Select odds to add to your bet slip</p>
            </div>
        </div>

        <div class="bet-slip-summary" id="bet-slip-summary" style="display: none;">
            <div class="total-stake">
                <span>Total Stake:</span>
                <span>KES <span id="total-stake-amount">0.00</span></span>
            </div>
            <div class="total-potential">
                <span>Potential Winnings:</span>
                <span>KES <span id="total-potential-amount">0.00</span></span>
            </div>
            <button class="place-bet-button" id="place-bet-button" onclick="placeBets()">
                Place Bet
            </button>
        </div>
    </div>
</div>

<!-- Event Selection Modal (if needed) -->
<div id="event-selection-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Select Live Event</h3>
            <button class="close-modal" onclick="closeEventModal()">&times;</button>
        </div>
        <div class="modal-body">
            <div id="live-events-list">
                <!-- Live events will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/live-betting.js' %}"></script>
<script>
// Live Betting Interface Functions
let betSlipEntries = [];
let currentEventId = null;

// Initialize live betting for specific event (if provided)
document.addEventListener('DOMContentLoaded', function() {
    // Check if we have an event ID from URL or context
    const urlParams = new URLSearchParams(window.location.search);
    const eventId = urlParams.get('event') || '{{ event.id|default:"" }}';
    
    if (eventId) {
        connectToEvent(eventId);
    } else {
        loadLiveEvents();
    }
    
    // Subscribe to general odds updates
    if (window.liveBettingClient) {
        window.liveBettingClient.subscribeToOdds([], []); // Subscribe to all
    }
});

function connectToEvent(eventId) {
    currentEventId = eventId;
    if (window.liveBettingClient) {
        window.liveBettingClient.connectToEvent(eventId);
        document.getElementById('event-status-display').style.display = 'flex';
    }
}

function addToBetSlip(marketId, oddsId, oddsName, oddsValue) {
    // Check if already in bet slip
    const existingIndex = betSlipEntries.findIndex(entry => entry.oddsId === oddsId);
    if (existingIndex !== -1) {
        return; // Already in bet slip
    }
    
    // Add to bet slip
    const entry = {
        marketId: marketId,
        oddsId: oddsId,
        marketName: 'Match Winner', // This would come from the market data
        oddsName: oddsName,
        oddsValue: parseFloat(oddsValue),
        stake: 10.00 // Default stake
    };
    
    betSlipEntries.push(entry);
    updateBetSlipDisplay();
    
    // Highlight selected odds
    document.querySelectorAll('.odds-option').forEach(option => {
        option.classList.remove('selected');
    });
    event.target.closest('.odds-option').classList.add('selected');
}

function updateBetSlipDisplay() {
    const container = document.getElementById('bet-slip-entries');
    const summary = document.getElementById('bet-slip-summary');
    
    if (betSlipEntries.length === 0) {
        container.innerHTML = '<div class="empty-bet-slip"><p>Select odds to add to your bet slip</p></div>';
        summary.style.display = 'none';
        return;
    }
    
    // Generate bet slip HTML
    let html = '';
    let totalStake = 0;
    let totalPotential = 0;
    
    betSlipEntries.forEach((entry, index) => {
        const potential = entry.stake * entry.oddsValue;
        totalStake += entry.stake;
        totalPotential += potential;
        
        html += `
            <div class="bet-slip-entry">
                <div class="bet-details">
                    <div class="market-name">${entry.marketName}</div>
                    <div class="odds-name">${entry.oddsName}</div>
                    <div class="odds-value" data-odds-id="${entry.oddsId}">${entry.oddsValue.toFixed(2)}</div>
                </div>
                <div class="stake-input">
                    <input type="number" class="stake" value="${entry.stake.toFixed(2)}" 
                           min="1" step="0.01" onchange="updateStake(${index}, this.value)">
                </div>
                <div class="potential-winnings">
                    Win: KES <span class="winnings-amount">${potential.toFixed(2)}</span>
                </div>
                <button class="remove-bet" onclick="removeBetSlipEntry(${index})">×</button>
            </div>
        `;
    });
    
    container.innerHTML = html;
    
    // Update summary
    document.getElementById('total-stake-amount').textContent = totalStake.toFixed(2);
    document.getElementById('total-potential-amount').textContent = totalPotential.toFixed(2);
    summary.style.display = 'block';
}

function updateStake(index, newStake) {
    betSlipEntries[index].stake = parseFloat(newStake) || 0;
    updateBetSlipDisplay();
}

function removeBetSlipEntry(index) {
    betSlipEntries.splice(index, 1);
    updateBetSlipDisplay();
    
    // Remove selection highlight
    document.querySelectorAll('.odds-option').forEach(option => {
        option.classList.remove('selected');
    });
}

function clearBetSlip() {
    betSlipEntries = [];
    updateBetSlipDisplay();
    
    // Remove all selection highlights
    document.querySelectorAll('.odds-option').forEach(option => {
        option.classList.remove('selected');
    });
}

function placeBets() {
    if (betSlipEntries.length === 0) {
        return;
    }
    
    // Disable button during processing
    const button = document.getElementById('place-bet-button');
    button.disabled = true;
    button.textContent = 'Placing Bet...';
    
    // Place each bet through WebSocket
    betSlipEntries.forEach(entry => {
        if (window.liveBettingClient && currentEventId) {
            window.liveBettingClient.placeLiveBet(entry.marketId, entry.oddsId, entry.stake);
        }
    });
    
    // Reset button after a delay
    setTimeout(() => {
        button.disabled = false;
        button.textContent = 'Place Bet';
        clearBetSlip(); // Clear bet slip after placing
    }, 2000);
}

function loadLiveEvents() {
    // This would typically load from an API endpoint
    // For now, show the sample market
    document.getElementById('sample-market').style.display = 'block';
    document.querySelector('.loading-message').style.display = 'none';
}

// WebSocket event handlers (extend the LiveBettingClient)
if (window.liveBettingClient) {
    // Override some methods to update the UI
    const originalDisplayEventDetails = window.liveBettingClient.displayEventDetails;
    window.liveBettingClient.displayEventDetails = function(eventDetails) {
        document.getElementById('event-name').textContent = eventDetails.name || 'Live Event';
        document.getElementById('event-sport').textContent = eventDetails.sport || 'Sport';
        
        const statusBadge = document.getElementById('event-status-badge');
        statusBadge.textContent = eventDetails.is_live ? 'LIVE' : eventDetails.status.toUpperCase();
        statusBadge.className = `event-status-badge ${eventDetails.is_live ? 'live' : 'finished'}`;
    };
    
    const originalUpdateEventStatus = window.liveBettingClient.updateEventStatus;
    window.liveBettingClient.updateEventStatus = function(statusData) {
        if (statusData.time_elapsed !== null) {
            LiveBettingUI.updateEventTimer(statusData.time_elapsed);
        }
        if (statusData.score) {
            LiveBettingUI.updateEventScore(statusData.score);
        }
    };
}
</script>
{% endblock %}
