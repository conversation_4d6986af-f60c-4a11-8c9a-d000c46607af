{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Change Password - Betzide!</title>
    <link rel="stylesheet" href="{% static 'css/base.css' %}">
    <link rel="stylesheet" href="{% static 'css/auth.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="auth-body">
    <!-- Back to Home -->
    <div class="back-to-home">
        <a href="{% url 'accounts:profile' %}" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Back to profile
        </a>
    </div>

    <!-- Main Auth Container -->
    <div class="auth-container">
        <div class="auth-card">
            <!-- Left Side - Illustration -->
            <div class="auth-illustration">
                <div class="illustration-content">
                    <div class="colorful-shapes">
                        <div class="shape shape-1"></div>
                        <div class="shape shape-2"></div>
                        <div class="shape shape-3"></div>
                        <div class="shape shape-4"></div>
                        <div class="shape shape-5"></div>
                    </div>
                    <div class="character">
                        <div class="character-body"></div>
                        <div class="character-head"></div>
                        <div class="character-arm-left"></div>
                        <div class="character-arm-right"></div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Password Change Form -->
            <div class="auth-form-section">
                <!-- Logo -->
                <div class="auth-logo">
                    <span class="logo-text">B!</span>
                </div>

                <!-- Form Header -->
                <div class="auth-header">
                    <h2>Change Password</h2>
                    <p>Update your account password</p>
                </div>

                <!-- Info Note -->
                <div class="info-note">
                    <i class="fas fa-info-circle"></i>
                    <p><strong>Note:</strong> This is a placeholder page. In a full implementation, this would include a form with current password, new password, and confirm password fields.</p>
                </div>

                <!-- Action Button -->
                <div class="auth-actions">
                    <a href="{% url 'accounts:profile' %}" class="auth-btn auth-btn-secondary">
                        Back to Profile
                    </a>
                </div>
            </div>
        </div>
    </div>

</body>
</html>