// Jackpot functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize jackpot functionality
    initializeJackpotTabs();
    initializeCountdownTimers();
    initializeJackpotSelections();
    initializeJackpotActions();
});

// Tab functionality
function initializeJackpotTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            
            // Remove active class from all tabs and contents
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Add active class to clicked tab and corresponding content
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
        });
    });
}

// Countdown timers
function initializeCountdownTimers() {
    const timers = document.querySelectorAll('.countdown-timer');
    
    timers.forEach(timer => {
        const endTime = new Date(timer.getAttribute('data-end-time')).getTime();
        const jackpotId = timer.closest('.jackpot-card').getAttribute('data-jackpot-id');
        
        updateCountdown(endTime, jackpotId);
        
        // Update every second
        setInterval(() => updateCountdown(endTime, jackpotId), 1000);
    });
}

function updateCountdown(endTime, jackpotId) {
    const now = new Date().getTime();
    const distance = endTime - now;
    
    if (distance < 0) {
        // Timer expired
        document.getElementById(`days-${jackpotId}`).textContent = '00';
        document.getElementById(`hours-${jackpotId}`).textContent = '00';
        document.getElementById(`minutes-${jackpotId}`).textContent = '00';
        document.getElementById(`seconds-${jackpotId}`).textContent = '00';
        return;
    }
    
    const days = Math.floor(distance / (1000 * 60 * 60 * 24));
    const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((distance % (1000 * 60)) / 1000);
    
    document.getElementById(`days-${jackpotId}`).textContent = days.toString().padStart(2, '0');
    document.getElementById(`hours-${jackpotId}`).textContent = hours.toString().padStart(2, '0');
    document.getElementById(`minutes-${jackpotId}`).textContent = minutes.toString().padStart(2, '0');
    document.getElementById(`seconds-${jackpotId}`).textContent = seconds.toString().padStart(2, '0');
}

// Jackpot selections
function initializeJackpotSelections() {
    const oddsButtons = document.querySelectorAll('.jackpot-odds');
    
    oddsButtons.forEach(button => {
        button.addEventListener('click', function() {
            const jackpotId = this.getAttribute('data-jackpot-id');
            const gameId = this.getAttribute('data-game-id');
            const selection = this.getAttribute('data-selection');
            
            // Remove selection from other buttons in the same game
            const gameButtons = document.querySelectorAll(`[data-game-id="${gameId}"]`);
            gameButtons.forEach(btn => btn.classList.remove('selected'));
            
            // Add selection to clicked button
            this.classList.add('selected');
            
            // Update jackpot summary
            updateJackpotSummary(jackpotId);
        });
    });
}

function updateJackpotSummary(jackpotId) {
    const selectedButtons = document.querySelectorAll(`[data-jackpot-id="${jackpotId}"].selected`);
    const pickedCount = selectedButtons.length;
    
    // Calculate combinations (for now, just show 1 if all games selected)
    const totalGames = document.querySelectorAll(`[data-jackpot-id="${jackpotId}"]`).length / 3; // 3 odds per game
    const combinations = pickedCount === totalGames ? 1 : 0;
    
    // Update display
    document.getElementById(`picked-count-${jackpotId}`).textContent = pickedCount;
    document.getElementById(`combination-${jackpotId}`).textContent = combinations;
    document.getElementById(`total-stake-${jackpotId}`).textContent = `KES ${combinations * 50}`; // Assuming 50 KES per combination
    
    // Enable/disable place bet button
    const placeBetButton = document.querySelector(`[data-jackpot-id="${jackpotId}"].place-jackpot-bet-btn`);
    if (placeBetButton) {
        placeBetButton.disabled = combinations === 0;
    }
}

// Jackpot actions
function initializeJackpotActions() {
    // Auto pick functionality
    const autoPickButtons = document.querySelectorAll('.auto-pick-btn');
    autoPickButtons.forEach(button => {
        button.addEventListener('click', function() {
            const jackpotId = this.getAttribute('data-jackpot-id');
            autoPickSelections(jackpotId);
        });
    });
    
    // Clear all functionality
    const clearAllButtons = document.querySelectorAll('.clear-all-btn');
    clearAllButtons.forEach(button => {
        button.addEventListener('click', function() {
            const jackpotId = this.getAttribute('data-jackpot-id');
            clearAllSelections(jackpotId);
        });
    });
    
    // Place bet functionality
    const placeBetButtons = document.querySelectorAll('.place-jackpot-bet-btn');
    placeBetButtons.forEach(button => {
        button.addEventListener('click', function() {
            const jackpotId = this.getAttribute('data-jackpot-id');
            placeJackpotBet(jackpotId);
        });
    });
}

function autoPickSelections(jackpotId) {
    // Get all games for this jackpot
    const gameRows = document.querySelectorAll(`[data-jackpot-id="${jackpotId}"]`).closest('.match-row');
    const uniqueGames = new Set();
    
    document.querySelectorAll(`[data-jackpot-id="${jackpotId}"]`).forEach(button => {
        uniqueGames.add(button.getAttribute('data-game-id'));
    });
    
    // Randomly select one option for each game
    uniqueGames.forEach(gameId => {
        const gameButtons = document.querySelectorAll(`[data-game-id="${gameId}"]`);
        const randomIndex = Math.floor(Math.random() * gameButtons.length);
        
        // Clear previous selections for this game
        gameButtons.forEach(btn => btn.classList.remove('selected'));
        
        // Select random option
        gameButtons[randomIndex].classList.add('selected');
    });
    
    // Update summary
    updateJackpotSummary(jackpotId);
}

function clearAllSelections(jackpotId) {
    const selectedButtons = document.querySelectorAll(`[data-jackpot-id="${jackpotId}"].selected`);
    selectedButtons.forEach(button => {
        button.classList.remove('selected');
    });
    
    // Update summary
    updateJackpotSummary(jackpotId);
}

function placeJackpotBet(jackpotId) {
    const selectedButtons = document.querySelectorAll(`[data-jackpot-id="${jackpotId}"].selected`);
    
    if (selectedButtons.length === 0) {
        showNotification('Please make your selections first', 'warning');
        return;
    }
    
    // Collect selections
    const selections = [];
    selectedButtons.forEach(button => {
        selections.push({
            game_id: button.getAttribute('data-game-id'),
            selection: button.getAttribute('data-selection'),
            odds: button.getAttribute('data-odds'),
            match: button.getAttribute('data-match')
        });
    });
    
    // Show confirmation modal or redirect to bet placement
    showJackpotBetConfirmation(jackpotId, selections);
}

function showJackpotBetConfirmation(jackpotId, selections) {
    // Create confirmation modal
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content jackpot-bet-modal">
            <div class="modal-header">
                <h3>Confirm Jackpot Bet</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="bet-summary">
                    <p><strong>Jackpot ID:</strong> ${jackpotId}</p>
                    <p><strong>Selections:</strong> ${selections.length}</p>
                    <p><strong>Stake:</strong> KES 50</p>
                </div>
                <div class="selections-list">
                    ${selections.map(sel => `
                        <div class="selection-item">
                            <span class="match">${sel.match}</span>
                            <span class="selection">${sel.selection} (${sel.odds})</span>
                        </div>
                    `).join('')}
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary modal-cancel">Cancel</button>
                <button class="btn btn-primary confirm-jackpot-bet" data-jackpot-id="${jackpotId}">
                    Confirm Bet - KES 50
                </button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Add event listeners
    modal.querySelector('.modal-close').addEventListener('click', () => {
        document.body.removeChild(modal);
    });
    
    modal.querySelector('.modal-cancel').addEventListener('click', () => {
        document.body.removeChild(modal);
    });
    
    modal.querySelector('.confirm-jackpot-bet').addEventListener('click', function() {
        const jackpotId = this.getAttribute('data-jackpot-id');
        submitJackpotBet(jackpotId, selections);
        document.body.removeChild(modal);
    });
    
    // Close on overlay click
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });
}

function submitJackpotBet(jackpotId, selections) {
    // Show loading state
    showNotification('Placing your jackpot bet...', 'info');
    
    // Submit to server
    fetch('/jackpot/place-bet/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            jackpot_id: jackpotId,
            selections: selections
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Jackpot bet placed successfully!', 'success');
            // Optionally redirect to bet history or refresh page
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            showNotification(data.message || 'Failed to place bet', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while placing your bet', 'error');
    });
}

// Utility functions
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        if (document.body.contains(notification)) {
            document.body.removeChild(notification);
        }
    }, 3000);
}
