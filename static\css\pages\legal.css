/* Legal Pages Styles */

.legal-page {
    min-height: 100vh;
    background-color: #1a1a1a;
    color: #ffffff;
    padding: 20px 0;
}

.legal-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

.legal-header {
    text-align: center;
    margin-bottom: 40px;
    padding-bottom: 20px;
    border-bottom: 2px solid #4CAF50;
}

.legal-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #4CAF50;
    margin-bottom: 10px;
}

.last-updated {
    color: #b0b0b0;
    font-size: 0.9rem;
    margin: 0;
}

.legal-content {
    line-height: 1.8;
}

.legal-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #2a2a2a;
    border-radius: 8px;
    border-left: 4px solid #4CAF50;
}

.legal-section h2 {
    color: #4CAF50;
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #404040;
}

.legal-section p {
    color: #ffffff;
    margin-bottom: 15px;
    font-size: 1rem;
}

.legal-section ul {
    margin: 15px 0;
    padding-left: 20px;
}

.legal-section li {
    color: #ffffff;
    margin-bottom: 8px;
    font-size: 0.95rem;
}

.legal-section li strong {
    color: #4CAF50;
    font-weight: 600;
}

.legal-footer {
    margin-top: 40px;
    padding-top: 20px;
    border-top: 1px solid #404040;
    text-align: center;
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.legal-footer .btn {
    padding: 12px 24px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 0.95rem;
}

.legal-footer .btn-primary {
    background-color: #4CAF50;
    color: #ffffff;
}

.legal-footer .btn-primary:hover {
    background-color: #45a049;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.legal-footer .btn-secondary {
    background-color: #404040;
    color: #ffffff;
}

.legal-footer .btn-secondary:hover {
    background-color: #505050;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(64, 64, 64, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .legal-container {
        padding: 0 15px;
    }
    
    .legal-header h1 {
        font-size: 2rem;
    }
    
    .legal-section {
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .legal-section h2 {
        font-size: 1.2rem;
    }
    
    .legal-section p,
    .legal-section li {
        font-size: 0.9rem;
    }
    
    .legal-footer {
        flex-direction: column;
        align-items: center;
    }
    
    .legal-footer .btn {
        width: 100%;
        max-width: 200px;
    }
}

@media (max-width: 480px) {
    .legal-page {
        padding: 10px 0;
    }
    
    .legal-header {
        margin-bottom: 30px;
    }
    
    .legal-header h1 {
        font-size: 1.8rem;
    }
    
    .legal-section {
        padding: 12px;
        border-left-width: 3px;
    }
    
    .legal-section h2 {
        font-size: 1.1rem;
    }
    
    .legal-section ul {
        padding-left: 15px;
    }
}

/* Print Styles */
@media print {
    .legal-page {
        background-color: white;
        color: black;
    }
    
    .legal-header h1 {
        color: black;
    }
    
    .legal-section {
        background: white;
        border-left-color: #333;
    }
    
    .legal-section h2 {
        color: black;
    }
    
    .legal-section p,
    .legal-section li {
        color: black;
    }
    
    .legal-footer {
        display: none;
    }
}
