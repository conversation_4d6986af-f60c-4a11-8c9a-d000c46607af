{% extends 'base.html' %}
{% load static %}

{% block title %}Home - Betika!{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/pages/home.css' %}">
{% endblock %}

{% block content %}
<!-- Promotional Banner -->
<div class="promo-banner">
    <div class="promo-content">
        <div class="promo-image">
            <i class="fas fa-fire"></i>
        </div>
        <div class="promo-text">
            <h2>PUNGUZA MOTO</h2>
            <p>ya Bet kuchomeka na up to <strong>5X STAKE BACK BONUS!</strong></p>
        </div>
        <div class="promo-action">
            <button class="btn btn-primary">Try it now</button>
            <button class="promo-close">&times;</button>
        </div>
    </div>
</div>

<!-- Event Navigation Tabs -->
<div class="event-tabs">
    <div class="tab-list">
        <button class="tab-btn active" data-tab="highlights">Highlights</button>
        <button class="tab-btn" data-tab="upcoming">Upcoming</button>
        <button class="tab-btn" data-tab="countries">Countries</button>
        <button class="tab-btn" data-tab="quick-e">Quick-e</button>
    </div>
    
    <div class="event-filters">
        <div class="filter-group">
            <button class="filter-btn">
                <i class="fas fa-filter"></i>
                Filters
            </button>
        </div>
        <div class="filter-group">
            <select class="filter-select">
                <option>Today</option>
                <option>Tomorrow</option>
                <option>This Week</option>
            </select>
        </div>
        <div class="filter-group">
            <select class="filter-select">
                <option>Highlights</option>
                <option>All Events</option>
            </select>
        </div>
        <div class="filter-group">
            <select class="filter-select">
                <option>1-2</option>
                <option>1-5</option>
                <option>1-10</option>
            </select>
        </div>
    </div>
</div>

<!-- Events Content -->
<div class="events-content">
    <div class="tab-content active" id="highlights">
        {% for event in highlighted_events %}
        <div class="event-card" data-event-id="{{ event.id }}">
            <div class="event-header">
                <div class="event-info">
                    <span class="competition">
                        <i class="fas fa-globe"></i>
                        {{ event.sport.name }} • {{ event.league|default:"League" }}
                    </span>
                    <span class="event-time">{{ event.start_time|date:"d/m, H:i" }}</span>
                    {% if event.status == 'live' %}
                    <span class="live-indicator">
                        <i class="fas fa-circle"></i> LIVE
                    </span>
                    {% endif %}
                </div>
            </div>
            <div class="event-match">
                <div class="teams">
                    <span class="team-name">{{ event.home_team }}</span>
                    <span class="vs">vs</span>
                    <span class="team-name">{{ event.away_team }}</span>
                </div>

                {% for market in event.markets.all %}
                {% if market.market_type == 'Match Result' %}
                <div class="odds-row">
                    <div class="market-header">
                        <span class="market-label">{{ market.name|default:"Teams" }}</span>
                        <span class="market-options">
                            {% for odds in market.odds.all %}
                            <span>{{ odds.selection }}</span>
                            {% endfor %}
                        </span>
                    </div>
                    <div class="odds-buttons">
                        {% for odds in market.odds.all %}
                        <button class="odds-btn"
                                data-odds="{{ odds.odds_value }}"
                                data-selection="{{ odds.selection }}"
                                data-event-id="{{ event.id }}"
                                data-event-name="{{ event.home_team }} vs {{ event.away_team }}"
                                data-market-type="{{ market.market_type }}"
                                data-market-name="{{ market.name }}"
                                data-odds-id="{{ odds.id }}"
                                data-market-id="{{ market.id }}">{{ odds.odds_value }}</button>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
                {% endfor %}

                <div class="market-expansion">
                    <button class="expand-btn" data-event-id="{{ event.id }}">+{{ event.markets.count }} Markets</button>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="empty-state">
            <i class="fas fa-calendar-alt"></i>
            <h3>No Events Available</h3>
            <p>Check back later for upcoming matches</p>
        </div>
        {% endfor %}

        <!-- Load More Button -->
        {% if highlighted_events %}
        <div class="load-more">
            <button class="btn btn-outline" id="load-more-highlights">Load More Events</button>
        </div>
        {% endif %}
    </div>

    <div class="tab-content" id="upcoming">
        {% for event in upcoming_events %}
        <div class="event-card" data-event-id="{{ event.id }}">
            <div class="event-header">
                <div class="event-info">
                    <span class="competition">
                        <i class="fas fa-clock"></i>
                        {{ event.sport.name }} • {{ event.league|default:"League" }}
                    </span>
                    <span class="event-time">{{ event.start_time|date:"d/m, H:i" }}</span>
                </div>
            </div>
            <div class="event-match">
                <div class="teams">
                    <span class="team-name">{{ event.home_team }}</span>
                    <span class="vs">vs</span>
                    <span class="team-name">{{ event.away_team }}</span>
                </div>

                {% for market in event.markets.all %}
                {% if market.market_type == 'Match Result' %}
                <div class="odds-row">
                    <div class="market-header">
                        <span class="market-label">{{ market.name|default:"Teams" }}</span>
                        <span class="market-options">
                            {% for odds in market.odds.all %}
                            <span>{{ odds.selection }}</span>
                            {% endfor %}
                        </span>
                    </div>
                    <div class="odds-buttons">
                        {% for odds in market.odds.all %}
                        <button class="odds-btn"
                                data-odds="{{ odds.odds_value }}"
                                data-selection="{{ odds.selection }}"
                                data-event-id="{{ event.id }}"
                                data-event-name="{{ event.home_team }} vs {{ event.away_team }}"
                                data-market-type="{{ market.market_type }}"
                                data-market-name="{{ market.name }}"
                                data-odds-id="{{ odds.id }}"
                                data-market-id="{{ market.id }}">{{ odds.odds_value }}</button>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
                {% endfor %}

                <div class="market-expansion">
                    <button class="expand-btn" data-event-id="{{ event.id }}">+{{ event.markets.count }} Markets</button>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="empty-state">
            <i class="fas fa-calendar-alt"></i>
            <h3>No Upcoming Events</h3>
            <p>Check back later for upcoming matches</p>
        </div>
        {% endfor %}
    </div>

    <div class="tab-content" id="countries">
        <div class="empty-state">
            <i class="fas fa-flag"></i>
            <h3>Countries View</h3>
            <p>Browse events by country</p>
        </div>
    </div>

    <div class="tab-content" id="quick-e">
        <div class="empty-state">
            <i class="fas fa-bolt"></i>
            <h3>Quick Events</h3>
            <p>Fast betting options coming soon</p>
        </div>
    </div>
</div>

<!-- Betslip Component -->
<div class="betslip-container" id="betslip-container">
    <div class="betslip" id="betslip">
        <div class="betslip-header">
            <h3>Betslip (<span id="betslip-count">0</span>)</h3>
            <button class="betslip-clear" id="clear-betslip">Clear All</button>
        </div>

        <div class="betslip-content" id="betslip-content">
            <div class="betslip-empty" id="betslip-empty">
                <i class="fas fa-ticket-alt"></i>
                <p>Your betslip is empty</p>
                <small>Click on odds to add selections</small>
            </div>

            <div class="betslip-selections" id="betslip-selections">
                <!-- Dynamic selections will be added here -->
            </div>
        </div>

        <div class="betslip-footer" id="betslip-footer" style="display: none;">
            <div class="betslip-totals">
                <div class="total-odds">
                    <span>Total Odds:</span>
                    <span id="total-odds">0.00</span>
                </div>
                <div class="potential-win">
                    <span>Potential Win:</span>
                    <span id="potential-win">KES 0.00</span>
                </div>
            </div>

            <div class="stake-input">
                <label for="stake-amount">Stake Amount (KES)</label>
                <input type="number" id="stake-amount" placeholder="100" min="1" step="1">
            </div>

            {% if user.is_authenticated %}
            <div class="user-balance">
                <span>Balance: KES {{ user.balance|default:"0.00" }}</span>
                {% if user.balance < 100 %}
                <a href="{% url 'payments:deposit' %}" class="deposit-link">Deposit</a>
                {% endif %}
            </div>

            <button class="place-bet-btn" id="place-bet-btn">
                <i class="fas fa-check"></i>
                Place Bet
            </button>
            {% else %}
            <div class="login-prompt">
                <p>Login to place bets</p>
                <a href="{% url 'accounts:login' %}" class="btn btn-primary">Login</a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Welcome Message for New Users -->
{% if not user.is_authenticated %}
<div class="welcome-overlay">
    <div class="welcome-modal">
        <div class="welcome-content">
            <h2>Welcome to Betika!</h2>
            <p>Join thousands of users and start betting on your favorite sports today.</p>
            <div class="welcome-actions">
                <a href="/accounts/register/" class="btn btn-primary">Get Started</a>
                <a href="/accounts/login/" class="btn btn-outline">Login</a>
            </div>
        </div>
        <button class="welcome-close">&times;</button>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/pages/home.js' %}"></script>
{% endblock %}