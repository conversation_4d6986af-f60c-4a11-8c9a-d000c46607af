{% extends 'base.html' %}
{% load static %}

{% block title %}Home - Betika!{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/pages/home.css' %}">
{% endblock %}

{% block content %}
<!-- Promotional Banner -->
<div class="promo-banner">
    <div class="promo-content">
        <div class="promo-image">
            <i class="fas fa-fire"></i>
        </div>
        <div class="promo-text">
            <h2>PUNGUZA MOTO</h2>
            <p>ya Bet kuchomeka na up to <strong>5X STAKE BACK BONUS!</strong></p>
        </div>
        <div class="promo-action">
            <button class="btn btn-primary">Try it now</button>
            <button class="promo-close">&times;</button>
        </div>
    </div>
</div>

<!-- Event Navigation Tabs -->
<div class="event-tabs">
    <div class="tab-list">
        <button class="tab-btn active" data-tab="highlights">Highlights</button>
        <button class="tab-btn" data-tab="upcoming">Upcoming</button>
        <button class="tab-btn" data-tab="countries">Countries</button>
        <button class="tab-btn" data-tab="quick-e">Quick-e</button>
    </div>
    
    <div class="event-filters">
        <div class="filter-group">
            <button class="filter-btn">
                <i class="fas fa-filter"></i>
                Filters
            </button>
        </div>
        <div class="filter-group">
            <select class="filter-select">
                <option>Today</option>
                <option>Tomorrow</option>
                <option>This Week</option>
            </select>
        </div>
        <div class="filter-group">
            <select class="filter-select">
                <option>Highlights</option>
                <option>All Events</option>
            </select>
        </div>
        <div class="filter-group">
            <select class="filter-select">
                <option>1-2</option>
                <option>1-5</option>
                <option>1-10</option>
            </select>
        </div>
    </div>
</div>

<!-- Events Content -->
<div class="events-content">
    <div class="tab-content active" id="highlights">
        <!-- Sample Event Cards -->
        <div class="event-card" data-event-id="event_1">
            <div class="event-header">
                <div class="event-info">
                    <span class="competition">
                        <i class="fas fa-globe"></i>
                        International Clubs • UEFA Champions League
                    </span>
                    <span class="event-time">09/07, 19:00</span>
                </div>
            </div>
            <div class="event-match">
                <div class="teams">
                    <span class="team-name">Vilnius FK Zalgiris</span>
                    <span class="vs">vs</span>
                    <span class="team-name">Hamrun Spartans FC</span>
                </div>
                <div class="odds-row">
                    <div class="market-header">
                        <span class="market-label">Teams</span>
                        <span class="market-options">
                            <span>1</span>
                            <span>X</span>
                            <span>2</span>
                        </span>
                    </div>
                    <div class="odds-buttons">
                        <button class="odds-btn" 
                                data-odds="1.70" 
                                data-selection="1" 
                                data-event-id="event_1"
                                data-market-type="Match Result"
                                data-odds-id="odds_1_1">1.70</button>
                        <button class="odds-btn" 
                                data-odds="3.55" 
                                data-selection="X" 
                                data-event-id="event_1"
                                data-market-type="Match Result"
                                data-odds-id="odds_1_x">3.55</button>
                        <button class="odds-btn" 
                                data-odds="5.60" 
                                data-selection="2" 
                                data-event-id="event_1"
                                data-market-type="Match Result"
                                data-odds-id="odds_1_2">5.60</button>
                    </div>
                </div>
                <div class="market-expansion">
                    <button class="expand-btn" data-event-id="event_1">+186 Markets</button>
                </div>
            </div>
        </div>

        <div class="event-card" data-event-id="event_2">
            <div class="event-header">
                <div class="event-info">
                    <span class="competition">
                        <i class="fas fa-globe"></i>
                        International Clubs • UEFA Champions League
                    </span>
                    <span class="event-time">09/07, 20:30</span>
                </div>
            </div>
            <div class="event-match">
                <div class="teams">
                    <span class="team-name">Pfc Ludogorets 1945</span>
                    <span class="vs">vs</span>
                    <span class="team-name">FC Dinamo Minsk</span>
                </div>
                <div class="odds-row">
                    <div class="market-header">
                        <span class="market-label">Teams</span>
                        <span class="market-options">
                            <span>1</span>
                            <span>X</span>
                            <span>2</span>
                        </span>
                    </div>
                    <div class="odds-buttons">
                        <button class="odds-btn" 
                                data-odds="1.36" 
                                data-selection="1" 
                                data-event-id="event_2"
                                data-market-type="Match Result"
                                data-odds-id="odds_2_1">1.36</button>
                        <button class="odds-btn" 
                                data-odds="5.20" 
                                data-selection="X" 
                                data-event-id="event_2"
                                data-market-type="Match Result"
                                data-odds-id="odds_2_x">5.20</button>
                        <button class="odds-btn" 
                                data-odds="8.40" 
                                data-selection="2" 
                                data-event-id="event_2"
                                data-market-type="Match Result"
                                data-odds-id="odds_2_2">8.40</button>
                    </div>
                </div>
                <div class="market-expansion">
                    <button class="expand-btn" data-event-id="event_2">+186 Markets</button>
                </div>
            </div>
        </div>

        <!-- Load More Button -->
        <div class="load-more">
            <button class="btn btn-outline">Load More Events</button>
        </div>
    </div>

    <div class="tab-content" id="upcoming">
        <div class="empty-state">
            <i class="fas fa-calendar-alt"></i>
            <h3>No Upcoming Events</h3>
            <p>Check back later for upcoming matches</p>
        </div>
    </div>

    <div class="tab-content" id="countries">
        <div class="empty-state">
            <i class="fas fa-flag"></i>
            <h3>Countries View</h3>
            <p>Browse events by country</p>
        </div>
    </div>

    <div class="tab-content" id="quick-e">
        <div class="empty-state">
            <i class="fas fa-bolt"></i>
            <h3>Quick Events</h3>
            <p>Fast betting options coming soon</p>
        </div>
    </div>
</div>

<!-- Welcome Message for New Users -->
{% if not user.is_authenticated %}
<div class="welcome-overlay">
    <div class="welcome-modal">
        <div class="welcome-content">
            <h2>Welcome to Betika!</h2>
            <p>Join thousands of users and start betting on your favorite sports today.</p>
            <div class="welcome-actions">
                <a href="/accounts/register/" class="btn btn-primary">Get Started</a>
                <a href="/accounts/login/" class="btn btn-outline">Login</a>
            </div>
        </div>
        <button class="welcome-close">&times;</button>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/pages/home.js' %}"></script>
{% endblock %}