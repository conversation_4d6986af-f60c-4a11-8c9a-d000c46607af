// Home Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tab switching
    initializeTabs();

    // Initialize promotional banner close button
    initializePromoBanner();

    // Initialize welcome modal for new users
    initializeWelcomeModal();

    // Initialize odds buttons
    initializeOddsButtons();

    // Initialize betslip
    initializeBetslip();
});

// Tab Switching Functionality
function initializeTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons and contents
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Add active class to clicked button
            this.classList.add('active');
            
            // Show corresponding content
            const tabId = this.dataset.tab;
            document.getElementById(tabId).classList.add('active');
        });
    });
}

// Promotional Banner
function initializePromoBanner() {
    const closeButton = document.querySelector('.promo-close');
    const promoBanner = document.querySelector('.promo-banner');
    
    if (closeButton && promoBanner) {
        closeButton.addEventListener('click', function() {
            promoBanner.style.display = 'none';
            
            // Store in session storage to keep it closed during session
            sessionStorage.setItem('promoBannerClosed', 'true');
        });
        
        // Check if banner was previously closed
        if (sessionStorage.getItem('promoBannerClosed') === 'true') {
            promoBanner.style.display = 'none';
        }
    }
}

// Welcome Modal for New Users
function initializeWelcomeModal() {
    const welcomeOverlay = document.querySelector('.welcome-overlay');
    const closeButton = document.querySelector('.welcome-close');
    
    if (welcomeOverlay && closeButton) {
        closeButton.addEventListener('click', function() {
            welcomeOverlay.style.display = 'none';
            
            // Store in local storage to not show again
            localStorage.setItem('welcomeModalShown', 'true');
        });
        
        // Check if modal was previously shown
        if (localStorage.getItem('welcomeModalShown') === 'true') {
            welcomeOverlay.style.display = 'none';
        }
    }
}

// Odds Button Functionality
function initializeOddsButtons() {
    const oddsButtons = document.querySelectorAll('.odds-btn');

    oddsButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Get odds data
            const oddsData = {
                odds: this.dataset.odds,
                selection: this.dataset.selection,
                eventId: this.dataset.eventId,
                eventName: this.dataset.eventName,
                marketType: this.dataset.marketType,
                marketName: this.dataset.marketName,
                oddsId: this.dataset.oddsId,
                marketId: this.dataset.marketId
            };

            // Toggle selection state
            if (this.classList.contains('selected')) {
                this.classList.remove('selected');
                if (window.betslipManager) {
                    window.betslipManager.removeSelection(oddsData.oddsId);
                }
                showNotification(`Removed ${oddsData.selection} from betslip`);
            } else {
                this.classList.add('selected');
                if (window.betslipManager) {
                    window.betslipManager.addSelection(oddsData);
                }
                showNotification(`Added ${oddsData.selection} (${oddsData.odds}) to betslip`);
            }
        });
    });
}

// Add selection to betslip
function addToBetslip(selection, odds, button) {
    // Get event information from the button's parent elements
    const eventCard = button ? button.closest('.event-card') : null;
    let eventName = 'Unknown Event';
    let eventId = generateEventId();
    let marketType = 'Match Result';
    let oddsId = button ? button.dataset.oddsId : `odds_${Date.now()}`;
    
    if (eventCard) {
        const teamsElement = eventCard.querySelector('.teams');
        if (teamsElement) {
            eventName = teamsElement.textContent.replace(/\s+vs\s+/, ' vs ').trim();
        }
        
        // Try to get event ID from data attribute or generate one
        if (eventCard.dataset.eventId) {
            eventId = eventCard.dataset.eventId;
        } else if (button && button.dataset.eventId) {
            eventId = button.dataset.eventId;
        }
    }
    
    // Get market type from button if available
    if (button && button.dataset.marketType) {
        marketType = button.dataset.marketType;
    }
    
    // Create selection object
    const selectionData = {
        selection: selection,
        oddsValue: parseFloat(odds),
        eventId: eventId,
        eventName: eventName,
        marketType: marketType,
        oddsId: oddsId
    };
    
    // Check if betslip functionality is available
    if (window.betSlipModule && window.betSlipModule.addToBetSlip) {
        // Use the betslip module
        window.betSlipModule.addToBetSlip(selectionData);
    } else {
        // Fallback notification
        showNotification(`Added ${selection} @ ${odds} to betslip`);
    }
}

// Generate unique event ID
function generateEventId() {
    return 'event_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
}

// Remove selection from betslip
function removeFromBetslip(selection) {
    // Check if betslip functionality is available from betslip.js
    if (window.betSlipModule && window.betSlipModule.removeFromBetSlip) {
        // Use the betslip module
        window.betSlipModule.removeFromBetSlip(selection);
    } else if (window.sportsModule && window.sportsModule.removeFromBetSlip) {
        // Use the sports module as fallback
        window.sportsModule.removeFromBetSlip(selection);
    } else {
        // Simple notification if betslip module not loaded
        showNotification(`Removed ${selection.selection || selection} from betslip`);
    }
}

// Simple notification function
function showNotification(message) {
    // Check if Utils is available from main.js
    if (window.Utils && window.Utils.showNotification) {
        window.Utils.showNotification(message);
        return;
    }
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    
    // Style the notification
    Object.assign(notification.style, {
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        backgroundColor: '#4CAF50',
        color: 'white',
        padding: '12px 20px',
        borderRadius: '4px',
        boxShadow: '0 2px 5px rgba(0,0,0,0.2)',
        zIndex: '9999',
        opacity: '0',
        transition: 'opacity 0.3s ease'
    });
    
    // Add to page
    document.body.appendChild(notification);
    
    // Fade in
    setTimeout(() => {
        notification.style.opacity = '1';
    }, 10);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Market expansion functionality
document.addEventListener('DOMContentLoaded', function() {
    const expandButtons = document.querySelectorAll('.expand-btn');
    
    expandButtons.forEach(button => {
        button.addEventListener('click', function() {
            const eventId = this.dataset.eventId;
            
            // Show loading state
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
            this.disabled = true;
            
            // This would typically load additional markets via AJAX
            // For now, simulate loading with a timeout
            setTimeout(() => {
                // Reset button
                this.innerHTML = '<i class="fas fa-plus"></i> More Markets';
                this.disabled = false;
                
                // Show notification
                showNotification('Additional markets loading feature coming soon');
            }, 1000);
        });
    });
});

// Betslip Management Class
class BetslipManager {
    constructor() {
        this.selections = [];
        this.container = document.getElementById('betslip-container');
        this.betslip = document.getElementById('betslip');
        this.count = document.getElementById('betslip-count');
        this.empty = document.getElementById('betslip-empty');
        this.selectionsContainer = document.getElementById('betslip-selections');
        this.footer = document.getElementById('betslip-footer');
        this.totalOdds = document.getElementById('total-odds');
        this.potentialWin = document.getElementById('potential-win');
        this.stakeInput = document.getElementById('stake-amount');
        this.placeBetBtn = document.getElementById('place-bet-btn');
        this.clearBtn = document.getElementById('clear-betslip');

        this.bindEvents();
    }

    bindEvents() {
        if (this.stakeInput) {
            this.stakeInput.addEventListener('input', () => this.updatePotentialWin());
        }

        if (this.placeBetBtn) {
            this.placeBetBtn.addEventListener('click', () => this.placeBet());
        }

        if (this.clearBtn) {
            this.clearBtn.addEventListener('click', () => this.clearAll());
        }
    }

    addSelection(oddsData) {
        // Check if selection already exists
        const existingIndex = this.selections.findIndex(s => s.oddsId === oddsData.oddsId);

        if (existingIndex !== -1) {
            // Update existing selection
            this.selections[existingIndex] = oddsData;
        } else {
            // Add new selection
            this.selections.push(oddsData);
        }

        this.updateBetslip();
        this.showBetslip();
    }

    removeSelection(oddsId) {
        this.selections = this.selections.filter(s => s.oddsId !== oddsId);
        this.updateBetslip();

        if (this.selections.length === 0) {
            this.hideBetslip();
        }
    }

    clearAll() {
        this.selections = [];
        this.updateBetslip();
        this.hideBetslip();

        // Remove active state from all odds buttons
        document.querySelectorAll('.odds-btn.selected').forEach(btn => {
            btn.classList.remove('selected');
        });
    }

    updateBetslip() {
        this.count.textContent = this.selections.length;

        if (this.selections.length === 0) {
            this.empty.style.display = 'block';
            this.selectionsContainer.style.display = 'none';
            this.footer.style.display = 'none';
        } else {
            this.empty.style.display = 'none';
            this.selectionsContainer.style.display = 'block';
            this.footer.style.display = 'block';

            this.renderSelections();
            this.updateTotals();
        }
    }

    renderSelections() {
        this.selectionsContainer.innerHTML = '';

        this.selections.forEach(selection => {
            const selectionEl = document.createElement('div');
            selectionEl.className = 'betslip-selection';
            selectionEl.innerHTML = `
                <div class="selection-header">
                    <span class="selection-event">${selection.eventName}</span>
                    <button class="remove-selection" data-odds-id="${selection.oddsId}">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="selection-details">
                    <span class="selection-market">${selection.marketName}</span>
                    <span class="selection-choice">${selection.selection}</span>
                </div>
                <div class="selection-odds">
                    <span class="odds-value">${selection.odds}</span>
                </div>
            `;

            this.selectionsContainer.appendChild(selectionEl);
        });

        // Bind remove buttons
        this.selectionsContainer.querySelectorAll('.remove-selection').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const oddsId = e.target.closest('.remove-selection').dataset.oddsId;
                this.removeSelection(oddsId);

                // Remove active state from corresponding odds button
                const oddsBtn = document.querySelector(`[data-odds-id="${oddsId}"]`);
                if (oddsBtn) {
                    oddsBtn.classList.remove('selected');
                }
            });
        });
    }

    updateTotals() {
        const totalOdds = this.selections.reduce((total, selection) => {
            return total * parseFloat(selection.odds);
        }, 1);

        this.totalOdds.textContent = totalOdds.toFixed(2);
        this.updatePotentialWin();
    }

    updatePotentialWin() {
        const stake = parseFloat(this.stakeInput?.value || 0);
        const totalOdds = parseFloat(this.totalOdds.textContent || 0);
        const potentialWin = stake * totalOdds;

        if (this.potentialWin) {
            this.potentialWin.textContent = `KES ${potentialWin.toFixed(2)}`;
        }
    }

    showBetslip() {
        this.container.classList.add('active');
    }

    hideBetslip() {
        this.container.classList.remove('active');
    }

    async placeBet() {
        if (this.selections.length === 0) {
            alert('Please add selections to your betslip');
            return;
        }

        const stake = parseFloat(this.stakeInput.value);
        if (!stake || stake <= 0) {
            alert('Please enter a valid stake amount');
            return;
        }

        try {
            this.placeBetBtn.disabled = true;
            this.placeBetBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Placing Bet...';

            const response = await fetch('/api/v1/betting/place/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify({
                    selections: this.selections,
                    stake: stake,
                    bet_type: this.selections.length > 1 ? 'accumulator' : 'single'
                })
            });

            const data = await response.json();

            if (data.success) {
                alert(`Bet placed successfully! Bet ID: ${data.bet_id}`);
                this.clearAll();

                // Update user balance if shown
                const balanceEl = document.querySelector('.user-section .balance');
                if (balanceEl && data.new_balance !== undefined) {
                    balanceEl.textContent = `KES ${data.new_balance}`;
                }

                // Reload page to show updated balance
                window.location.reload();
            } else {
                alert(`Error: ${data.error}`);
            }
        } catch (error) {
            console.error('Error placing bet:', error);
            alert('Error placing bet. Please try again.');
        } finally {
            this.placeBetBtn.disabled = false;
            this.placeBetBtn.innerHTML = '<i class="fas fa-check"></i> Place Bet';
        }
    }
}

// Initialize betslip
function initializeBetslip() {
    window.betslipManager = new BetslipManager();
}