{% extends 'base.html' %}
{% load static %}

{% block title %}Betting Dashboard - Betzide{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/pages/home.css' %}">
<link rel="stylesheet" href="{% static 'css/betting.css' %}">
{% endblock %}

{% block content %}
<!-- Promotional Banner -->
<div class="promo-banner">
    <div class="promo-content">
        <div class="promo-image">
            <i class="fas fa-chart-bar"></i>
        </div>
        <div class="promo-text">
            <h2>Betting Dashboard</h2>
            <p>Track your betting performance and <strong>manage your bets</strong> efficiently!</p>
        </div>
        <div class="promo-action">
            <a href="{% url 'home' %}" class="btn btn-outline">Place New Bet</a>
            <button class="promo-close">&times;</button>
        </div>
    </div>
</div>

<!-- Event Navigation Tabs -->
<div class="event-tabs">
    <div class="tab-list">
        <button class="tab-btn active" data-tab="overview">Overview</button>
        <button class="tab-btn" data-tab="active-bets">Active Bets</button>
        <button class="tab-btn" data-tab="history">History</button>
        <button class="tab-btn" data-tab="statistics">Statistics</button>
    </div>

    <div class="event-filters">
        <div class="filter-group">
            <button class="filter-btn">
                <i class="fas fa-filter"></i>
                Filters
            </button>
        </div>
        <div class="filter-group">
            <select class="filter-select">
                <option>All Bets</option>
                <option>Won</option>
                <option>Lost</option>
                <option>Pending</option>
            </select>
        </div>
    </div>
</div>

<!-- Events Content -->
<div class="events-content">
    <div class="tab-content active" id="overview">
        <div class="betting-dashboard">
    <!-- User Betting Statistics -->
    <div class="stats-section">
        <h2><i class="fas fa-chart-bar"></i> Your Betting Statistics</h2>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-ticket-alt"></i>
                </div>
                <div class="stat-info">
                    <h3>{{ stats.total_bets }}</h3>
                    <p>Total Bets</p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stat-info">
                    <h3>KES {{ stats.total_stake|floatformat:2 }}</h3>
                    <p>Total Staked</p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-trophy"></i>
                </div>
                <div class="stat-info">
                    <h3>KES {{ stats.total_winnings|floatformat:2 }}</h3>
                    <p>Total Winnings</p>
                </div>
            </div>
            
            <div class="stat-card {% if stats.net_profit >= 0 %}profit{% else %}loss{% endif %}">
                <div class="stat-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-info">
                    <h3>KES {{ stats.net_profit|floatformat:2 }}</h3>
                    <p>Net {% if stats.net_profit >= 0 %}Profit{% else %}Loss{% endif %}</p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-info">
                    <h3>{{ stats.pending_bets }}</h3>
                    <p>Pending Bets</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <h3><i class="fas fa-bolt"></i> Quick Actions</h3>
        <div class="action-buttons">
            <a href="/sports/" class="action-btn primary">
                <i class="fas fa-plus"></i>
                Place New Bet
            </a>
            <a href="{% url 'betting:history' %}" class="action-btn secondary">
                <i class="fas fa-history"></i>
                View All Bets
            </a>
            <a href="{% url 'betting:multi_bet' %}" class="action-btn accent">
                <i class="fas fa-layer-group"></i>
                Multi Bet
            </a>
            <a href="/payments/" class="action-btn success">
                <i class="fas fa-wallet"></i>
                Deposit Funds
            </a>
        </div>
    </div>

    <!-- Recent Bets -->
    <div class="recent-bets">
        <h3><i class="fas fa-ticket-alt"></i> Recent Bets</h3>
        
        {% if recent_bets %}
        <div class="bets-list">
            {% for bet in recent_bets %}
            <div class="bet-card">
                <div class="bet-header">
                    <div class="bet-id">
                        <strong>Bet #{{ bet.id|slice:":8" }}...</strong>
                        <span class="bet-type">{{ bet.get_bet_type_display }}</span>
                    </div>
                    <div class="bet-status">
                        <span class="status-badge {{ bet.status }}">
                            {{ bet.get_status_display }}
                        </span>
                    </div>
                </div>
                
                <div class="bet-details">
                    <div class="bet-info">
                        <div class="info-item">
                            <span class="label">Stake:</span>
                            <span class="value">KES {{ bet.stake|floatformat:2 }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Odds:</span>
                            <span class="value">{{ bet.total_odds|floatformat:2 }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Potential Win:</span>
                            <span class="value">KES {{ bet.potential_winnings|floatformat:2 }}</span>
                        </div>
                        {% if bet.status == 'won' %}
                        <div class="info-item">
                            <span class="label">Actual Win:</span>
                            <span class="value win">KES {{ bet.actual_winnings|floatformat:2 }}</span>
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="bet-time">
                        <i class="fas fa-clock"></i>
                        {{ bet.placed_at|timesince }} ago
                    </div>
                </div>
                
                <div class="bet-actions">
                    <a href="{% url 'betting:bet_detail' bet.id %}" class="btn-small primary">
                        <i class="fas fa-eye"></i> View Details
                    </a>
                    {% if bet.status == 'pending' %}
                    <button class="btn-small danger" onclick="cancelBet('{{ bet.id }}')">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
        
        <div class="view-all">
            <a href="{% url 'betting:history' %}" class="btn-outline">
                View All Betting History
                <i class="fas fa-arrow-right"></i>
            </a>
        </div>
        
        {% else %}
        <div class="empty-state">
            <i class="fas fa-ticket-alt"></i>
            <h4>No bets placed yet</h4>
            <p>Start betting on your favorite sports and events!</p>
            <a href="/sports/" class="btn-primary">
                <i class="fas fa-plus"></i>
                Place Your First Bet
            </a>
        </div>
        {% endif %}
    </div>

    <div class="tab-content" id="active-bets">
        <div class="empty-state">
            <i class="fas fa-clock"></i>
            <h3>Active Bets</h3>
            <p>Your active bets will appear here</p>
        </div>
    </div>

    <div class="tab-content" id="history">
        <div class="empty-state">
            <i class="fas fa-history"></i>
            <h3>Betting History</h3>
            <p>Your betting history will appear here</p>
        </div>
    </div>

    <div class="tab-content" id="statistics">
        <div class="empty-state">
            <i class="fas fa-chart-line"></i>
            <h3>Detailed Statistics</h3>
            <p>Advanced betting statistics coming soon</p>
        </div>
    </div>
</div>

<!-- Betslip Component -->
<div class="betslip-container" id="betslip-container">
    <div class="betslip" id="betslip">
        <div class="betslip-header">
            <h3>Betslip (<span id="betslip-count">0</span>)</h3>
            <button class="betslip-clear" id="clear-betslip">Clear All</button>
        </div>

        <div class="betslip-content" id="betslip-content">
            <div class="betslip-empty" id="betslip-empty">
                <i class="fas fa-ticket-alt"></i>
                <p>Your betslip is empty</p>
                <small>Click on odds to add selections</small>
            </div>

            <div class="betslip-selections" id="betslip-selections">
                <!-- Dynamic selections will be added here -->
            </div>
        </div>

        <div class="betslip-footer" id="betslip-footer" style="display: none;">
            <div class="betslip-totals">
                <div class="total-odds">
                    <span>Total Odds:</span>
                    <span id="total-odds">0.00</span>
                </div>
                <div class="potential-win">
                    <span>Potential Win:</span>
                    <span id="potential-win">KES 0.00</span>
                </div>
            </div>

            <div class="stake-input">
                <label for="stake-amount">Stake Amount (KES)</label>
                <input type="number" id="stake-amount" placeholder="100" min="1" step="1">
            </div>

            {% if user.is_authenticated %}
            <div class="user-balance">
                <span>Balance: KES {{ user.balance|default:"0.00" }}</span>
                {% if user.balance < 100 %}
                <a href="{% url 'payments:deposit' %}" class="deposit-link">Deposit</a>
                {% endif %}
            </div>

            <button class="place-bet-btn" id="place-bet-btn">
                <i class="fas fa-check"></i>
                Place Bet
            </button>
            {% else %}
            <div class="login-prompt">
                <p>Login to place bets</p>
                <a href="{% url 'accounts:login' %}" class="btn btn-primary">Login</a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.betting-dashboard {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.stats-section {
    margin-bottom: 30px;
}

.stats-section h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 24px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    border-left: 4px solid #4CAF50;
}

.stat-card.profit {
    border-left-color: #4CAF50;
}

.stat-card.loss {
    border-left-color: #e74c3c;
}

.stat-icon {
    font-size: 24px;
    color: #4CAF50;
    margin-right: 15px;
}

.stat-info h3 {
    margin: 0;
    font-size: 24px;
    color: #2c3e50;
}

.stat-info p {
    margin: 5px 0 0 0;
    color: #7f8c8d;
    font-size: 14px;
}

.quick-actions {
    margin-bottom: 30px;
}

.quick-actions h3 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.action-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.action-btn {
    padding: 12px 20px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.action-btn.primary {
    background: #4CAF50;
    color: white;
}

.action-btn.secondary {
    background: #34495e;
    color: white;
}

.action-btn.accent {
    background: #3498db;
    color: white;
}

.action-btn.success {
    background: #27ae60;
    color: white;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.recent-bets h3 {
    color: #2c3e50;
    margin-bottom: 20px;
}

.bet-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-left: 4px solid #4CAF50;
}

.bet-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.bet-type {
    background: #ecf0f1;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    color: #2c3e50;
    margin-left: 10px;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.pending {
    background: #f39c12;
    color: white;
}

.status-badge.won {
    background: #27ae60;
    color: white;
}

.status-badge.lost {
    background: #e74c3c;
    color: white;
}

.status-badge.void {
    background: #95a5a6;
    color: white;
}

.bet-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.bet-info {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.info-item {
    display: flex;
    flex-direction: column;
}

.info-item .label {
    font-size: 12px;
    color: #7f8c8d;
    margin-bottom: 2px;
}

.info-item .value {
    font-weight: 600;
    color: #2c3e50;
}

.info-item .value.win {
    color: #27ae60;
}

.bet-time {
    color: #7f8c8d;
    font-size: 14px;
}

.bet-actions {
    display: flex;
    gap: 10px;
}

.btn-small {
    padding: 6px 12px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 12px;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
}

.btn-small.primary {
    background: #3498db;
    color: white;
}

.btn-small.danger {
    background: #e74c3c;
    color: white;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #7f8c8d;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 20px;
    color: #bdc3c7;
}

.empty-state h4 {
    margin-bottom: 10px;
    color: #2c3e50;
}

.view-all {
    text-align: center;
    margin-top: 20px;
}

.btn-outline {
    padding: 12px 24px;
    border: 2px solid #4CAF50;
    color: #4CAF50;
    text-decoration: none;
    border-radius: 6px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-outline:hover {
    background: #4CAF50;
    color: white;
}

@media (max-width: 768px) {
    .bet-details {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .bet-info {
        gap: 15px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .action-btn {
        justify-content: center;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/pages/home.js' %}"></script>
<script>
function cancelBet(betId) {
    if (confirm('Are you sure you want to cancel this bet? Your stake will be refunded.')) {
        fetch(`/betting/bet/${betId}/cancel/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            },
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Error cancelling bet. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error cancelling bet. Please try again.');
        });
    }
}
</script>
{% endblock %}
