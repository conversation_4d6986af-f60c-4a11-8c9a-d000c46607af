"""
Caching utilities for the Betika clone platform
"""

import hashlib
import json
from typing import Any, Optional, Dict, List, Union
from functools import wraps
from django.core.cache import caches, cache
from django.conf import settings
from django.utils import timezone
from datetime import timedelta
import logging

logger = logging.getLogger(__name__)


class CacheManager:
    """
    Centralized cache management for different data types
    """
    
    # Cache aliases
    DEFAULT_CACHE = 'default'
    ODDS_CACHE = 'odds'
    SESSIONS_CACHE = 'sessions'
    ANALYTICS_CACHE = 'analytics'
    
    # Cache key prefixes
    ODDS_PREFIX = 'odds'
    USER_PREFIX = 'user'
    SPORTS_PREFIX = 'sports'
    EVENTS_PREFIX = 'events'
    MARKETS_PREFIX = 'markets'
    ANALYTICS_PREFIX = 'analytics'
    BETS_PREFIX = 'bets'
    
    @classmethod
    def get_cache(cls, cache_alias: str = DEFAULT_CACHE):
        """Get cache instance by alias"""
        try:
            return caches[cache_alias]
        except Exception as e:
            logger.warning(f"Failed to get cache {cache_alias}: {e}")
            return cache  # Fallback to default cache
    
    @classmethod
    def generate_cache_key(cls, prefix: str, *args, **kwargs) -> str:
        """Generate a consistent cache key"""
        key_parts = [prefix]
        
        # Add positional arguments
        for arg in args:
            if isinstance(arg, (dict, list)):
                key_parts.append(hashlib.md5(json.dumps(arg, sort_keys=True).encode()).hexdigest()[:8])
            else:
                key_parts.append(str(arg))
        
        # Add keyword arguments
        if kwargs:
            sorted_kwargs = sorted(kwargs.items())
            kwargs_str = json.dumps(sorted_kwargs, sort_keys=True)
            key_parts.append(hashlib.md5(kwargs_str.encode()).hexdigest()[:8])
        
        return ':'.join(key_parts)
    
    @classmethod
    def set_with_tags(cls, key: str, value: Any, timeout: Optional[int] = None, 
                     cache_alias: str = DEFAULT_CACHE, tags: Optional[List[str]] = None):
        """Set cache value with optional tags for invalidation"""
        cache_instance = cls.get_cache(cache_alias)
        
        try:
            cache_instance.set(key, value, timeout)
            
            # Store tags for later invalidation
            if tags:
                for tag in tags:
                    tag_key = f"tag:{tag}"
                    tagged_keys = cache_instance.get(tag_key, set())
                    if not isinstance(tagged_keys, set):
                        tagged_keys = set()
                    tagged_keys.add(key)
                    cache_instance.set(tag_key, tagged_keys, timeout=86400)  # Tags expire in 24 hours
                    
        except Exception as e:
            logger.error(f"Failed to set cache key {key}: {e}")
    
    @classmethod
    def invalidate_by_tags(cls, tags: List[str], cache_alias: str = DEFAULT_CACHE):
        """Invalidate all cache keys associated with given tags"""
        cache_instance = cls.get_cache(cache_alias)
        
        for tag in tags:
            try:
                tag_key = f"tag:{tag}"
                tagged_keys = cache_instance.get(tag_key, set())
                
                if isinstance(tagged_keys, set) and tagged_keys:
                    # Delete all keys associated with this tag
                    cache_instance.delete_many(list(tagged_keys))
                    # Delete the tag itself
                    cache_instance.delete(tag_key)
                    
            except Exception as e:
                logger.error(f"Failed to invalidate tag {tag}: {e}")


class OddsCache:
    """Specialized caching for odds data"""
    
    @staticmethod
    def get_odds_key(event_id: int, market_type: str = None) -> str:
        """Generate cache key for odds"""
        if market_type:
            return CacheManager.generate_cache_key(
                CacheManager.ODDS_PREFIX, 'event', event_id, 'market', market_type
            )
        return CacheManager.generate_cache_key(
            CacheManager.ODDS_PREFIX, 'event', event_id
        )
    
    @staticmethod
    def cache_odds(event_id: int, odds_data: Dict, market_type: str = None, timeout: int = 30):
        """Cache odds data with short timeout"""
        key = OddsCache.get_odds_key(event_id, market_type)
        tags = [f"event_{event_id}", "odds_data"]
        
        CacheManager.set_with_tags(
            key, odds_data, timeout, 
            cache_alias=CacheManager.ODDS_CACHE, 
            tags=tags
        )
    
    @staticmethod
    def get_cached_odds(event_id: int, market_type: str = None) -> Optional[Dict]:
        """Retrieve cached odds data"""
        key = OddsCache.get_odds_key(event_id, market_type)
        cache_instance = CacheManager.get_cache(CacheManager.ODDS_CACHE)
        
        try:
            return cache_instance.get(key)
        except Exception as e:
            logger.error(f"Failed to get cached odds {key}: {e}")
            return None
    
    @staticmethod
    def invalidate_event_odds(event_id: int):
        """Invalidate all odds for a specific event"""
        CacheManager.invalidate_by_tags([f"event_{event_id}"], CacheManager.ODDS_CACHE)


class UserCache:
    """Specialized caching for user data"""
    
    @staticmethod
    def get_user_key(user_id: int, data_type: str = 'profile') -> str:
        """Generate cache key for user data"""
        return CacheManager.generate_cache_key(
            CacheManager.USER_PREFIX, user_id, data_type
        )
    
    @staticmethod
    def cache_user_data(user_id: int, data: Any, data_type: str = 'profile', timeout: int = 300):
        """Cache user data"""
        key = UserCache.get_user_key(user_id, data_type)
        tags = [f"user_{user_id}"]
        
        CacheManager.set_with_tags(key, data, timeout, tags=tags)
    
    @staticmethod
    def get_cached_user_data(user_id: int, data_type: str = 'profile') -> Optional[Any]:
        """Retrieve cached user data"""
        key = UserCache.get_user_key(user_id, data_type)
        cache_instance = CacheManager.get_cache()
        
        try:
            return cache_instance.get(key)
        except Exception as e:
            logger.error(f"Failed to get cached user data {key}: {e}")
            return None
    
    @staticmethod
    def invalidate_user_cache(user_id: int):
        """Invalidate all cache for a specific user"""
        CacheManager.invalidate_by_tags([f"user_{user_id}"])


class SportsCache:
    """Specialized caching for sports and events data"""
    
    @staticmethod
    def cache_sports_list(sports_data: List[Dict], timeout: int = 3600):
        """Cache sports list"""
        key = CacheManager.generate_cache_key(CacheManager.SPORTS_PREFIX, 'list')
        tags = ['sports_data']
        
        CacheManager.set_with_tags(key, sports_data, timeout, tags=tags)
    
    @staticmethod
    def cache_events_by_sport(sport_id: int, events_data: List[Dict], timeout: int = 300):
        """Cache events for a specific sport"""
        key = CacheManager.generate_cache_key(CacheManager.EVENTS_PREFIX, 'sport', sport_id)
        tags = [f"sport_{sport_id}", 'events_data']
        
        CacheManager.set_with_tags(key, events_data, timeout, tags=tags)
    
    @staticmethod
    def get_cached_events_by_sport(sport_id: int) -> Optional[List[Dict]]:
        """Get cached events for a sport"""
        key = CacheManager.generate_cache_key(CacheManager.EVENTS_PREFIX, 'sport', sport_id)
        cache_instance = CacheManager.get_cache()
        
        try:
            return cache_instance.get(key)
        except Exception as e:
            logger.error(f"Failed to get cached events for sport {sport_id}: {e}")
            return None


def cache_result(timeout: int = 300, cache_alias: str = 'default', key_prefix: str = ''):
    """
    Decorator to cache function results
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            func_name = f"{func.__module__}.{func.__name__}"
            cache_key = CacheManager.generate_cache_key(
                key_prefix or func_name, *args, **kwargs
            )
            
            # Try to get from cache
            cache_instance = CacheManager.get_cache(cache_alias)
            try:
                result = cache_instance.get(cache_key)
                if result is not None:
                    return result
            except Exception as e:
                logger.warning(f"Cache get failed for {cache_key}: {e}")
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            
            try:
                cache_instance.set(cache_key, result, timeout)
            except Exception as e:
                logger.warning(f"Cache set failed for {cache_key}: {e}")
            
            return result
        return wrapper
    return decorator


def invalidate_cache_pattern(pattern: str, cache_alias: str = 'default'):
    """
    Invalidate cache keys matching a pattern
    Note: This is a simplified version. For production, consider using Redis SCAN
    """
    cache_instance = CacheManager.get_cache(cache_alias)
    
    try:
        # This is a basic implementation
        # In production, you might want to use Redis-specific commands
        if hasattr(cache_instance, 'delete_pattern'):
            cache_instance.delete_pattern(pattern)
        else:
            logger.warning(f"Pattern deletion not supported for cache {cache_alias}")
    except Exception as e:
        logger.error(f"Failed to invalidate cache pattern {pattern}: {e}")
