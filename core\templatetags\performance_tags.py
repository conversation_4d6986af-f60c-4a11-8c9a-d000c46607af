"""
Template tags for performance optimization
"""

from django import template
from django.utils.safestring import mark_safe
from django.utils.html import format_html
from django.conf import settings
import hashlib
import json

register = template.Library()


@register.simple_tag
def lazy_image(src, alt="", css_class="", width=None, height=None, critical=False):
    """
    Generate a lazy-loaded image tag
    
    Usage:
    {% lazy_image "path/to/image.jpg" "Alt text" "css-class" width=300 height=200 %}
    {% lazy_image "path/to/image.jpg" critical=True %}  # For critical images
    """
    # Create placeholder SVG
    placeholder = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
    
    # Build attributes
    attrs = {
        'alt': alt,
        'class': css_class,
    }
    
    if width:
        attrs['width'] = width
    if height:
        attrs['height'] = height
    
    if critical:
        # Critical images load immediately
        attrs['src'] = src
        attrs['data-critical'] = 'true'
    else:
        # Lazy-loaded images
        attrs['src'] = placeholder
        attrs['data-src'] = src
        attrs['loading'] = 'lazy'  # Native lazy loading fallback
    
    # Build attribute string
    attr_string = ' '.join([f'{k}="{v}"' for k, v in attrs.items()])
    
    return mark_safe(f'<img {attr_string}>')


@register.simple_tag
def lazy_content(url, css_class="lazy-content", loading_text="Loading..."):
    """
    Generate a lazy-loaded content container
    
    Usage:
    {% lazy_content "/api/v1/sports/events/123/odds/" "odds-container" "Loading odds..." %}
    """
    return format_html(
        '<div class="{}" data-content-url="{}">{}</div>',
        css_class,
        url,
        loading_text
    )


@register.simple_tag
def event_card_lazy(event):
    """
    Generate a lazy-loaded event card
    
    Usage:
    {% event_card_lazy event %}
    """
    return format_html(
        '<div class="event-card" data-event-id="{}" data-sport-slug="{}">'
        '<div class="event-header">'
        '<div class="event-teams">'
        '<span class="team-name">{}</span>'
        '<span class="vs-separator">vs</span>'
        '<span class="team-name">{}</span>'
        '</div>'
        '<div class="event-time">{}</div>'
        '</div>'
        '<div class="odds-container">'
        '<div class="odds-loading">Tap to load odds</div>'
        '</div>'
        '</div>',
        event.id,
        event.sport.slug,
        event.home_team,
        event.away_team,
        event.start_time.strftime('%H:%M')
    )


@register.simple_tag
def preload_critical_resources():
    """
    Generate preload tags for critical resources
    
    Usage:
    {% preload_critical_resources %}
    """
    critical_resources = [
        {'href': '/static/css/critical.css', 'as': 'style'},
        {'href': '/static/js/main.js', 'as': 'script'},
        {'href': '/static/fonts/roboto-regular.woff2', 'as': 'font', 'type': 'font/woff2', 'crossorigin': 'anonymous'},
    ]
    
    preload_tags = []
    for resource in critical_resources:
        attrs = ' '.join([f'{k}="{v}"' for k, v in resource.items()])
        preload_tags.append(f'<link rel="preload" {attrs}>')
    
    return mark_safe('\n'.join(preload_tags))


@register.simple_tag
def resource_hints():
    """
    Generate resource hints for better performance
    
    Usage:
    {% resource_hints %}
    """
    hints = [
        '<link rel="dns-prefetch" href="//fonts.googleapis.com">',
        '<link rel="dns-prefetch" href="//api.betika.com">',
        '<link rel="preconnect" href="//fonts.gstatic.com" crossorigin>',
    ]
    
    return mark_safe('\n'.join(hints))


@register.inclusion_tag('performance/lazy_image.html')
def lazy_img(src, alt="", css_class="", width=None, height=None, critical=False):
    """
    Inclusion tag version of lazy_image for more complex templates
    """
    return {
        'src': src,
        'alt': alt,
        'css_class': css_class,
        'width': width,
        'height': height,
        'critical': critical,
        'placeholder': "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
    }


@register.filter
def cache_bust(url):
    """
    Add cache busting parameter to URL
    
    Usage:
    {{ "style.css"|cache_bust }}
    """
    if settings.DEBUG:
        import time
        return f"{url}?v={int(time.time())}"
    else:
        # Use file hash in production
        try:
            import os
            from django.contrib.staticfiles import finders
            
            file_path = finders.find(url.lstrip('/static/'))
            if file_path and os.path.exists(file_path):
                with open(file_path, 'rb') as f:
                    file_hash = hashlib.md5(f.read()).hexdigest()[:8]
                return f"{url}?v={file_hash}"
        except:
            pass
    
    return url


@register.simple_tag
def inline_critical_css():
    """
    Inline critical CSS for above-the-fold content
    
    Usage:
    {% inline_critical_css %}
    """
    try:
        from django.contrib.staticfiles import finders
        
        critical_css_path = finders.find('css/critical.css')
        if critical_css_path:
            with open(critical_css_path, 'r', encoding='utf-8') as f:
                critical_css = f.read()
            return mark_safe(f'<style>{critical_css}</style>')
    except:
        pass
    
    return ''


@register.simple_tag
def performance_metrics():
    """
    Add performance monitoring script
    
    Usage:
    {% performance_metrics %}
    """
    if settings.DEBUG:
        script = """
        <script>
        // Performance monitoring
        window.addEventListener('load', function() {
            if ('performance' in window) {
                const perfData = performance.getEntriesByType('navigation')[0];
                console.log('Page Load Time:', perfData.loadEventEnd - perfData.fetchStart, 'ms');
                console.log('DOM Content Loaded:', perfData.domContentLoadedEventEnd - perfData.fetchStart, 'ms');
                console.log('First Paint:', performance.getEntriesByType('paint')[0]?.startTime, 'ms');
            }
        });
        </script>
        """
        return mark_safe(script)
    
    return ''


@register.simple_tag(takes_context=True)
def lazy_load_config(context):
    """
    Generate lazy loading configuration
    
    Usage:
    {% lazy_load_config %}
    """
    config = {
        'imageThreshold': '50px',
        'contentThreshold': '100px',
        'oddsThreshold': '200px',
        'enableNativeLazyLoading': True,
        'enableIntersectionObserver': True,
        'fallbackToEager': not context.get('request').user_agent.is_mobile if context.get('request') else False
    }
    
    script = f"""
    <script>
    window.lazyLoadConfig = {json.dumps(config)};
    </script>
    """
    
    return mark_safe(script)


@register.simple_tag
def webp_image(src, alt="", css_class="", width=None, height=None, critical=False):
    """
    Generate WebP image with fallback
    
    Usage:
    {% webp_image "image.jpg" "Alt text" %}
    """
    # Generate WebP version path
    webp_src = src.rsplit('.', 1)[0] + '.webp'
    
    # Build picture element with WebP support
    picture_html = f"""
    <picture>
        <source srcset="{webp_src}" type="image/webp">
        <img src="{src}" alt="{alt}" class="{css_class}"
    """
    
    if width:
        picture_html += f' width="{width}"'
    if height:
        picture_html += f' height="{height}"'
    if critical:
        picture_html += ' data-critical="true"'
    else:
        picture_html += ' loading="lazy"'
    
    picture_html += "></picture>"
    
    return mark_safe(picture_html)


@register.simple_tag
def service_worker():
    """
    Register service worker for caching
    
    Usage:
    {% service_worker %}
    """
    script = """
    <script>
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
            navigator.serviceWorker.register('/sw.js')
                .then(function(registration) {
                    console.log('SW registered: ', registration);
                })
                .catch(function(registrationError) {
                    console.log('SW registration failed: ', registrationError);
                });
        });
    }
    </script>
    """
    
    return mark_safe(script)


@register.filter
def compress_json(value):
    """
    Compress JSON data for better performance
    
    Usage:
    {{ data|compress_json }}
    """
    return json.dumps(value, separators=(',', ':'))
